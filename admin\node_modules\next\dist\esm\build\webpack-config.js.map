{"version": 3, "sources": ["../../src/build/webpack-config.ts"], "sourcesContent": ["import React from 'react'\nimport ReactRefreshWebpackPlugin from 'next/dist/compiled/@next/react-refresh-utils/dist/ReactRefreshWebpackPlugin'\nimport { yellow, bold } from '../lib/picocolors'\nimport crypto from 'crypto'\nimport { webpack } from 'next/dist/compiled/webpack/webpack'\nimport path from 'path'\n\nimport { escapeStringRegexp } from '../shared/lib/escape-regexp'\nimport { WEBPACK_LAYERS, WEBPACK_RESOURCE_QUERIES } from '../lib/constants'\nimport type { WebpackLayerName } from '../lib/constants'\nimport {\n  isWebpackBundledLayer,\n  isWebpackClientOnlyLayer,\n  isWebpackDefaultLayer,\n  isWebpackServerOnlyLayer,\n} from './utils'\nimport type { CustomRoutes } from '../lib/load-custom-routes.js'\nimport {\n  CLIENT_STATIC_FILES_RUNTIME_AMP,\n  CLIENT_STATIC_FILES_RUNTIME_MAIN,\n  CLIENT_STATIC_FILES_RUNTIME_MAIN_APP,\n  CLIENT_STATIC_FILES_RUNTIME_POLYFILLS_SYMBOL,\n  CLIENT_STATIC_FILES_RUNTIME_REACT_REFRESH,\n  CLIENT_STATIC_FILES_RUNTIME_WEBPACK,\n  MIDDLEWARE_REACT_LOADABLE_MANIFEST,\n  REACT_LOADABLE_MANIFEST,\n  SERVER_DIRECTORY,\n  COMPILER_NAMES,\n} from '../shared/lib/constants'\nimport type { CompilerNameValues } from '../shared/lib/constants'\nimport { execOnce } from '../shared/lib/utils'\nimport type { NextConfigComplete } from '../server/config-shared'\nimport { finalizeEntrypoint } from './entries'\nimport * as Log from './output/log'\nimport { buildConfiguration } from './webpack/config'\nimport MiddlewarePlugin, {\n  getEdgePolyfilledModules,\n  handleWebpackExternalForEdgeRuntime,\n} from './webpack/plugins/middleware-plugin'\nimport BuildManifestPlugin from './webpack/plugins/build-manifest-plugin'\nimport { JsConfigPathsPlugin } from './webpack/plugins/jsconfig-paths-plugin'\nimport { DropClientPage } from './webpack/plugins/next-drop-client-page-plugin'\nimport PagesManifestPlugin from './webpack/plugins/pages-manifest-plugin'\nimport { ProfilingPlugin } from './webpack/plugins/profiling-plugin'\nimport { ReactLoadablePlugin } from './webpack/plugins/react-loadable-plugin'\nimport { WellKnownErrorsPlugin } from './webpack/plugins/wellknown-errors-plugin'\nimport { regexLikeCss } from './webpack/config/blocks/css'\nimport { CopyFilePlugin } from './webpack/plugins/copy-file-plugin'\nimport { ClientReferenceManifestPlugin } from './webpack/plugins/flight-manifest-plugin'\nimport { FlightClientEntryPlugin as NextFlightClientEntryPlugin } from './webpack/plugins/flight-client-entry-plugin'\nimport { RspackFlightClientEntryPlugin } from './webpack/plugins/rspack-flight-client-entry-plugin'\nimport { NextTypesPlugin } from './webpack/plugins/next-types-plugin'\nimport type {\n  Feature,\n  SWC_TARGET_TRIPLE,\n} from './webpack/plugins/telemetry-plugin/telemetry-plugin'\nimport type { Span } from '../trace'\nimport type { MiddlewareMatcher } from './analysis/get-page-static-info'\nimport loadJsConfig, {\n  type JsConfig,\n  type ResolvedBaseUrl,\n} from './load-jsconfig'\nimport { loadBindings } from './swc'\nimport { AppBuildManifestPlugin } from './webpack/plugins/app-build-manifest-plugin'\nimport { SubresourceIntegrityPlugin } from './webpack/plugins/subresource-integrity-plugin'\nimport { NextFontManifestPlugin } from './webpack/plugins/next-font-manifest-plugin'\nimport { getSupportedBrowsers } from './utils'\nimport { MemoryWithGcCachePlugin } from './webpack/plugins/memory-with-gc-cache-plugin'\nimport { getBabelConfigFile } from './get-babel-config-file'\nimport { needsExperimentalReact } from '../lib/needs-experimental-react'\nimport { getDefineEnvPlugin } from './webpack/plugins/define-env-plugin'\nimport type { SWCLoaderOptions } from './webpack/loaders/next-swc-loader'\nimport { isResourceInPackages, makeExternalHandler } from './handle-externals'\nimport {\n  getMainField,\n  edgeConditionNames,\n} from './webpack-config-rules/resolve'\nimport { OptionalPeerDependencyResolverPlugin } from './webpack/plugins/optional-peer-dependency-resolve-plugin'\nimport {\n  createWebpackAliases,\n  createServerOnlyClientOnlyAliases,\n  createRSCAliases,\n  createNextApiEsmAliases,\n  createAppRouterApiAliases,\n} from './create-compiler-aliases'\nimport { hasCustomExportOutput } from '../export/utils'\nimport { CssChunkingPlugin } from './webpack/plugins/css-chunking-plugin'\nimport {\n  getBabelLoader,\n  getReactCompilerLoader,\n} from './get-babel-loader-config'\nimport {\n  NEXT_PROJECT_ROOT,\n  NEXT_PROJECT_ROOT_DIST_CLIENT,\n} from './next-dir-paths'\nimport { getRspackCore, getRspackReactRefresh } from '../shared/lib/get-rspack'\nimport { RspackProfilingPlugin } from './webpack/plugins/rspack-profiling-plugin'\nimport getWebpackBundler from '../shared/lib/get-webpack-bundler'\n\ntype ExcludesFalse = <T>(x: T | false) => x is T\ntype ClientEntries = {\n  [key: string]: string | string[]\n}\n\nconst EXTERNAL_PACKAGES =\n  require('../lib/server-external-packages.json') as string[]\n\nconst DEFAULT_TRANSPILED_PACKAGES =\n  require('../lib/default-transpiled-packages.json') as string[]\n\nif (parseInt(React.version) < 18) {\n  throw new Error('Next.js requires react >= 18.2.0 to be installed.')\n}\n\nexport const babelIncludeRegexes: RegExp[] = [\n  /next[\\\\/]dist[\\\\/](esm[\\\\/])?shared[\\\\/]lib/,\n  /next[\\\\/]dist[\\\\/](esm[\\\\/])?client/,\n  /next[\\\\/]dist[\\\\/](esm[\\\\/])?pages/,\n  /[\\\\/](strip-ansi|ansi-regex|styled-jsx)[\\\\/]/,\n]\n\nconst browserNonTranspileModules = [\n  // Transpiling `process/browser` will trigger babel compilation error due to value replacement.\n  // TypeError: Property left of AssignmentExpression expected node to be of a type [\"LVal\"] but instead got \"BooleanLiteral\"\n  // e.g. `process.browser = true` will become `true = true`.\n  /[\\\\/]node_modules[\\\\/]process[\\\\/]browser/,\n  // Exclude precompiled react packages from browser compilation due to SWC helper insertion (#61791),\n  // We fixed the issue but it's safer to exclude them from compilation since they don't need to be re-compiled.\n  /[\\\\/]next[\\\\/]dist[\\\\/]compiled[\\\\/](react|react-dom|react-server-dom-webpack)(-experimental)?($|[\\\\/])/,\n]\nconst precompileRegex = /[\\\\/]next[\\\\/]dist[\\\\/]compiled[\\\\/]/\n\nconst asyncStoragesRegex =\n  /next[\\\\/]dist[\\\\/](esm[\\\\/])?server[\\\\/]app-render[\\\\/](work-async-storage|action-async-storage|work-unit-async-storage)/\n\n// Support for NODE_PATH\nconst nodePathList = (process.env.NODE_PATH || '')\n  .split(process.platform === 'win32' ? ';' : ':')\n  .filter((p) => !!p)\n\nconst baseWatchOptions: webpack.Configuration['watchOptions'] = Object.freeze({\n  aggregateTimeout: 5,\n  ignored:\n    // Matches **/node_modules/**, **/.git/** and **/.next/**\n    /^((?:[^/]*(?:\\/|$))*)(\\.(git|next)|node_modules)(\\/((?:[^/]*(?:\\/|$))*)(?:$|\\/))?/,\n})\n\nfunction isModuleCSS(module: { type: string }) {\n  return (\n    // mini-css-extract-plugin\n    module.type === `css/mini-extract` ||\n    // extract-css-chunks-webpack-plugin (old)\n    module.type === `css/extract-chunks` ||\n    // extract-css-chunks-webpack-plugin (new)\n    module.type === `css/extract-css-chunks`\n  )\n}\n\nconst devtoolRevertWarning = execOnce(\n  (devtool: webpack.Configuration['devtool']) => {\n    console.warn(\n      yellow(bold('Warning: ')) +\n        bold(`Reverting webpack devtool to '${devtool}'.\\n`) +\n        'Changing the webpack devtool in development mode will cause severe performance regressions.\\n' +\n        'Read more: https://nextjs.org/docs/messages/improper-devtool'\n    )\n  }\n)\n\nlet loggedSwcDisabled = false\nlet loggedIgnoredCompilerOptions = false\nconst reactRefreshLoaderName =\n  'next/dist/compiled/@next/react-refresh-utils/dist/loader'\n\nfunction getReactRefreshLoader() {\n  return process.env.NEXT_RSPACK\n    ? getRspackReactRefresh().loader\n    : require.resolve(reactRefreshLoaderName)\n}\n\nexport function attachReactRefresh(\n  webpackConfig: webpack.Configuration,\n  targetLoader: webpack.RuleSetUseItem\n) {\n  const reactRefreshLoader = getReactRefreshLoader()\n  webpackConfig.module?.rules?.forEach((rule) => {\n    if (rule && typeof rule === 'object' && 'use' in rule) {\n      const curr = rule.use\n      // When the user has configured `defaultLoaders.babel` for a input file:\n      if (curr === targetLoader) {\n        rule.use = [reactRefreshLoader, curr as webpack.RuleSetUseItem]\n      } else if (\n        Array.isArray(curr) &&\n        curr.some((r) => r === targetLoader) &&\n        // Check if loader already exists:\n        !curr.some(\n          (r) => r === reactRefreshLoader || r === reactRefreshLoaderName\n        )\n      ) {\n        const idx = curr.findIndex((r) => r === targetLoader)\n        // Clone to not mutate user input\n        rule.use = [...curr]\n\n        // inject / input: [other, babel] output: [other, refresh, babel]:\n        rule.use.splice(idx, 0, reactRefreshLoader)\n      }\n    }\n  })\n}\n\nexport const NODE_RESOLVE_OPTIONS = {\n  dependencyType: 'commonjs',\n  modules: ['node_modules'],\n  fallback: false,\n  exportsFields: ['exports'],\n  importsFields: ['imports'],\n  conditionNames: ['node', 'require'],\n  descriptionFiles: ['package.json'],\n  extensions: ['.js', '.json', '.node'],\n  enforceExtensions: false,\n  symlinks: true,\n  mainFields: ['main'],\n  mainFiles: ['index'],\n  roots: [],\n  fullySpecified: false,\n  preferRelative: false,\n  preferAbsolute: false,\n  restrictions: [],\n}\n\nexport const NODE_BASE_RESOLVE_OPTIONS = {\n  ...NODE_RESOLVE_OPTIONS,\n  alias: false,\n}\n\nexport const NODE_ESM_RESOLVE_OPTIONS = {\n  ...NODE_RESOLVE_OPTIONS,\n  alias: false,\n  dependencyType: 'esm',\n  conditionNames: ['node', 'import'],\n  fullySpecified: true,\n}\n\nexport const NODE_BASE_ESM_RESOLVE_OPTIONS = {\n  ...NODE_ESM_RESOLVE_OPTIONS,\n  alias: false,\n}\n\nexport const nextImageLoaderRegex =\n  /\\.(png|jpg|jpeg|gif|webp|avif|ico|bmp|svg)$/i\n\nexport async function loadProjectInfo({\n  dir,\n  config,\n  dev,\n}: {\n  dir: string\n  config: NextConfigComplete\n  dev: boolean\n}): Promise<{\n  jsConfig: JsConfig\n  jsConfigPath?: string\n  resolvedBaseUrl: ResolvedBaseUrl\n  supportedBrowsers: string[] | undefined\n}> {\n  const { jsConfig, jsConfigPath, resolvedBaseUrl } = await loadJsConfig(\n    dir,\n    config\n  )\n  const supportedBrowsers = await getSupportedBrowsers(dir, dev)\n  return {\n    jsConfig,\n    jsConfigPath,\n    resolvedBaseUrl,\n    supportedBrowsers,\n  }\n}\n\nexport function hasExternalOtelApiPackage(): boolean {\n  try {\n    require('@opentelemetry/api')\n    return true\n  } catch {\n    return false\n  }\n}\n\nconst UNSAFE_CACHE_REGEX = /[\\\\/]pages[\\\\/][^\\\\/]+(?:$|\\?|#)/\n\nexport default async function getBaseWebpackConfig(\n  dir: string,\n  {\n    buildId,\n    encryptionKey,\n    config,\n    compilerType,\n    dev = false,\n    entrypoints,\n    isDevFallback = false,\n    pagesDir,\n    reactProductionProfiling = false,\n    rewrites,\n    originalRewrites,\n    originalRedirects,\n    runWebpackSpan,\n    appDir,\n    middlewareMatchers,\n    noMangling,\n    jsConfig,\n    jsConfigPath,\n    resolvedBaseUrl,\n    supportedBrowsers,\n    clientRouterFilters,\n    fetchCacheKeyPrefix,\n    edgePreviewProps,\n    isCompileMode,\n  }: {\n    isCompileMode?: boolean\n    buildId: string\n    encryptionKey: string\n    config: NextConfigComplete\n    compilerType: CompilerNameValues\n    dev?: boolean\n    entrypoints: webpack.EntryObject\n    isDevFallback?: boolean\n    pagesDir: string | undefined\n    reactProductionProfiling?: boolean\n    rewrites: CustomRoutes['rewrites']\n    originalRewrites: CustomRoutes['rewrites'] | undefined\n    originalRedirects: CustomRoutes['redirects'] | undefined\n    runWebpackSpan: Span\n    appDir: string | undefined\n    middlewareMatchers?: MiddlewareMatcher[]\n    noMangling?: boolean\n    jsConfig: any\n    jsConfigPath?: string\n    resolvedBaseUrl: ResolvedBaseUrl\n    supportedBrowsers: string[] | undefined\n    edgePreviewProps?: Record<string, string>\n    clientRouterFilters?: {\n      staticFilter: ReturnType<\n        import('../shared/lib/bloom-filter').BloomFilter['export']\n      >\n      dynamicFilter: ReturnType<\n        import('../shared/lib/bloom-filter').BloomFilter['export']\n      >\n    }\n    fetchCacheKeyPrefix?: string\n  }\n): Promise<webpack.Configuration> {\n  const bundler = getWebpackBundler()\n  const isClient = compilerType === COMPILER_NAMES.client\n  const isEdgeServer = compilerType === COMPILER_NAMES.edgeServer\n  const isNodeServer = compilerType === COMPILER_NAMES.server\n\n  const isRspack = Boolean(process.env.NEXT_RSPACK)\n\n  const FlightClientEntryPlugin =\n    isRspack && process.env.BUILTIN_FLIGHT_CLIENT_ENTRY_PLUGIN\n      ? RspackFlightClientEntryPlugin\n      : NextFlightClientEntryPlugin\n\n  // If the current compilation is aimed at server-side code instead of client-side code.\n  const isNodeOrEdgeCompilation = isNodeServer || isEdgeServer\n\n  const hasRewrites =\n    rewrites.beforeFiles.length > 0 ||\n    rewrites.afterFiles.length > 0 ||\n    rewrites.fallback.length > 0\n\n  const hasAppDir = !!appDir\n  const disableOptimizedLoading = true\n  const enableTypedRoutes = !!config.experimental.typedRoutes && hasAppDir\n  const bundledReactChannel = needsExperimentalReact(config)\n    ? '-experimental'\n    : ''\n\n  const babelConfigFile = getBabelConfigFile(dir)\n\n  if (!dev && hasCustomExportOutput(config)) {\n    config.distDir = '.next'\n  }\n  const distDir = path.join(dir, config.distDir)\n\n  let useSWCLoader = !babelConfigFile || config.experimental.forceSwcTransforms\n  let SWCBinaryTarget: [Feature, boolean] | undefined = undefined\n  if (useSWCLoader) {\n    // TODO: we do not collect wasm target yet\n    const binaryTarget = require('./swc')?.getBinaryMetadata?.()\n      ?.target as SWC_TARGET_TRIPLE\n    SWCBinaryTarget = binaryTarget\n      ? [`swc/target/${binaryTarget}` as const, true]\n      : undefined\n  }\n\n  if (!loggedSwcDisabled && !useSWCLoader && babelConfigFile) {\n    Log.info(\n      `Disabled SWC as replacement for Babel because of custom Babel configuration \"${path.relative(\n        dir,\n        babelConfigFile\n      )}\" https://nextjs.org/docs/messages/swc-disabled`\n    )\n    loggedSwcDisabled = true\n  }\n\n  // eagerly load swc bindings instead of waiting for transform calls\n  if (!babelConfigFile && isClient) {\n    await loadBindings(config.experimental.useWasmBinary)\n  }\n\n  // since `pages` doesn't always bundle by default we need to\n  // auto-include optimizePackageImports in transpilePackages\n  const finalTranspilePackages: string[] = (\n    config.transpilePackages || []\n  ).concat(DEFAULT_TRANSPILED_PACKAGES)\n\n  for (const pkg of config.experimental.optimizePackageImports || []) {\n    if (!finalTranspilePackages.includes(pkg)) {\n      finalTranspilePackages.push(pkg)\n    }\n  }\n\n  if (!loggedIgnoredCompilerOptions && !useSWCLoader && config.compiler) {\n    Log.info(\n      '`compiler` options in `next.config.js` will be ignored while using Babel https://nextjs.org/docs/messages/ignored-compiler-options'\n    )\n    loggedIgnoredCompilerOptions = true\n  }\n\n  const shouldIncludeExternalDirs =\n    config.experimental.externalDir || !!config.transpilePackages\n  const codeCondition = {\n    test: { or: [/\\.(tsx|ts|js|cjs|mjs|jsx)$/, /__barrel_optimize__/] },\n    ...(shouldIncludeExternalDirs\n      ? // Allowing importing TS/TSX files from outside of the root dir.\n        {}\n      : { include: [dir, ...babelIncludeRegexes] }),\n    exclude: (excludePath: string) => {\n      if (babelIncludeRegexes.some((r) => r.test(excludePath))) {\n        return false\n      }\n\n      const shouldBeBundled = isResourceInPackages(\n        excludePath,\n        finalTranspilePackages\n      )\n      if (shouldBeBundled) return false\n\n      return excludePath.includes('node_modules')\n    },\n  }\n\n  const babelLoader = getBabelLoader(\n    useSWCLoader,\n    babelConfigFile,\n    isNodeOrEdgeCompilation,\n    distDir,\n    pagesDir,\n    dir,\n    (appDir || pagesDir)!,\n    dev,\n    isClient,\n    config.experimental?.reactCompiler,\n    codeCondition.exclude\n  )\n\n  const reactCompilerLoader = babelLoader\n    ? undefined\n    : getReactCompilerLoader(\n        config.experimental?.reactCompiler,\n        dir,\n        dev,\n        isNodeOrEdgeCompilation,\n        codeCondition.exclude\n      )\n\n  let swcTraceProfilingInitialized = false\n  const getSwcLoader = (extraOptions: Partial<SWCLoaderOptions>) => {\n    if (\n      config?.experimental?.swcTraceProfiling &&\n      !swcTraceProfilingInitialized\n    ) {\n      // This will init subscribers once only in a single process lifecycle,\n      // even though it can be called multiple times.\n      // Subscriber need to be initialized _before_ any actual swc's call (transform, etcs)\n      // to collect correct trace spans when they are called.\n      swcTraceProfilingInitialized = true\n      require('./swc')?.initCustomTraceSubscriber?.(\n        path.join(distDir, `swc-trace-profile-${Date.now()}.json`)\n      )\n    }\n\n    const useBuiltinSwcLoader = process.env.BUILTIN_SWC_LOADER\n    if (isRspack && useBuiltinSwcLoader) {\n      return {\n        loader: 'builtin:next-swc-loader',\n        options: {\n          isServer: isNodeOrEdgeCompilation,\n          rootDir: dir,\n          pagesDir,\n          appDir,\n          hasReactRefresh: dev && isClient,\n          transpilePackages: finalTranspilePackages,\n          supportedBrowsers,\n          swcCacheDir: path.join(\n            dir,\n            config?.distDir ?? '.next',\n            'cache',\n            'swc'\n          ),\n          serverReferenceHashSalt: encryptionKey,\n\n          // rspack specific options\n          pnp: Boolean(process.versions.pnp),\n          optimizeServerReact: Boolean(config.experimental.optimizeServerReact),\n          modularizeImports: config.modularizeImports,\n          decorators: Boolean(\n            jsConfig?.compilerOptions?.experimentalDecorators\n          ),\n          emitDecoratorMetadata: Boolean(\n            jsConfig?.compilerOptions?.emitDecoratorMetadata\n          ),\n          regeneratorRuntimePath: require.resolve(\n            'next/dist/compiled/regenerator-runtime'\n          ),\n\n          ...extraOptions,\n        },\n      }\n    }\n\n    return {\n      loader: 'next-swc-loader',\n      options: {\n        isServer: isNodeOrEdgeCompilation,\n        rootDir: dir,\n        pagesDir,\n        appDir,\n        hasReactRefresh: dev && isClient,\n        nextConfig: config,\n        jsConfig,\n        transpilePackages: finalTranspilePackages,\n        supportedBrowsers,\n        swcCacheDir: path.join(dir, config?.distDir ?? '.next', 'cache', 'swc'),\n        serverReferenceHashSalt: encryptionKey,\n        ...extraOptions,\n      } satisfies SWCLoaderOptions,\n    }\n  }\n\n  // RSC loaders, prefer ESM, set `esm` to true\n  const swcServerLayerLoader = getSwcLoader({\n    serverComponents: true,\n    bundleLayer: WEBPACK_LAYERS.reactServerComponents,\n    esm: true,\n  })\n  const swcSSRLayerLoader = getSwcLoader({\n    serverComponents: true,\n    bundleLayer: WEBPACK_LAYERS.serverSideRendering,\n    esm: true,\n  })\n  const swcBrowserLayerLoader = getSwcLoader({\n    serverComponents: true,\n    bundleLayer: WEBPACK_LAYERS.appPagesBrowser,\n    esm: true,\n  })\n  // Default swc loaders for pages doesn't prefer ESM.\n  const swcDefaultLoader = getSwcLoader({\n    serverComponents: true,\n    esm: false,\n  })\n\n  const defaultLoaders = {\n    babel: useSWCLoader ? swcDefaultLoader : babelLoader!,\n  }\n\n  const appServerLayerLoaders = hasAppDir\n    ? [\n        // When using Babel, we will have to add the SWC loader\n        // as an additional pass to handle RSC correctly.\n        // This will cause some performance overhead but\n        // acceptable as Babel will not be recommended.\n        swcServerLayerLoader,\n        babelLoader,\n        reactCompilerLoader,\n      ].filter(Boolean)\n    : []\n\n  const instrumentLayerLoaders = [\n    'next-flight-loader',\n    // When using Babel, we will have to add the SWC loader\n    // as an additional pass to handle RSC correctly.\n    // This will cause some performance overhead but\n    // acceptable as Babel will not be recommended.\n    swcServerLayerLoader,\n    babelLoader,\n  ].filter(Boolean)\n\n  const middlewareLayerLoaders = [\n    'next-flight-loader',\n    // When using Babel, we will have to use SWC to do the optimization\n    // for middleware to tree shake the unused default optimized imports like \"next/server\".\n    // This will cause some performance overhead but\n    // acceptable as Babel will not be recommended.\n    getSwcLoader({\n      serverComponents: true,\n      bundleLayer: WEBPACK_LAYERS.middleware,\n    }),\n    babelLoader,\n  ].filter(Boolean)\n\n  const reactRefreshLoaders = dev && isClient ? [getReactRefreshLoader()] : []\n\n  // client components layers: SSR or browser\n  const createClientLayerLoader = ({\n    isBrowserLayer,\n    reactRefresh,\n  }: {\n    isBrowserLayer: boolean\n    reactRefresh: boolean\n  }) => [\n    ...(reactRefresh ? reactRefreshLoaders : []),\n    {\n      // This loader handles actions and client entries\n      // in the client layer.\n      loader: 'next-flight-client-module-loader',\n    },\n    ...(hasAppDir\n      ? [\n          // When using Babel, we will have to add the SWC loader\n          // as an additional pass to handle RSC correctly.\n          // This will cause some performance overhead but\n          // acceptable as Babel will not be recommended.\n          isBrowserLayer ? swcBrowserLayerLoader : swcSSRLayerLoader,\n          babelLoader,\n          reactCompilerLoader,\n        ].filter(Boolean)\n      : []),\n  ]\n\n  const appBrowserLayerLoaders = createClientLayerLoader({\n    isBrowserLayer: true,\n    // reactRefresh for browser layer is applied conditionally to user-land source\n    reactRefresh: false,\n  })\n  const appSSRLayerLoaders = createClientLayerLoader({\n    isBrowserLayer: false,\n    reactRefresh: true,\n  })\n\n  // Loader for API routes needs to be differently configured as it shouldn't\n  // have RSC transpiler enabled, so syntax checks such as invalid imports won't\n  // be performed.\n  const apiRoutesLayerLoaders = useSWCLoader\n    ? getSwcLoader({\n        serverComponents: false,\n        bundleLayer: WEBPACK_LAYERS.apiNode,\n      })\n    : defaultLoaders.babel\n\n  const pageExtensions = config.pageExtensions\n\n  const outputPath = isNodeOrEdgeCompilation\n    ? path.join(distDir, SERVER_DIRECTORY)\n    : distDir\n\n  const reactServerCondition = [\n    'react-server',\n    ...(isEdgeServer ? edgeConditionNames : []),\n    // inherits the default conditions\n    '...',\n  ]\n\n  const reactRefreshEntry = isRspack\n    ? getRspackReactRefresh().entry\n    : require.resolve(\n        `next/dist/compiled/@next/react-refresh-utils/dist/runtime`\n      )\n\n  const clientEntries = isClient\n    ? ({\n        // Backwards compatibility\n        'main.js': [],\n        ...(dev\n          ? {\n              [CLIENT_STATIC_FILES_RUNTIME_REACT_REFRESH]: reactRefreshEntry,\n              [CLIENT_STATIC_FILES_RUNTIME_AMP]:\n                `./` +\n                path\n                  .relative(\n                    dir,\n                    path.join(NEXT_PROJECT_ROOT_DIST_CLIENT, 'dev', 'amp-dev')\n                  )\n                  .replace(/\\\\/g, '/'),\n            }\n          : {}),\n        [CLIENT_STATIC_FILES_RUNTIME_MAIN]:\n          `./` +\n          path\n            .relative(\n              dir,\n              path.join(\n                NEXT_PROJECT_ROOT_DIST_CLIENT,\n                dev ? `next-dev.js` : 'next.js'\n              )\n            )\n            .replace(/\\\\/g, '/'),\n        ...(hasAppDir\n          ? {\n              [CLIENT_STATIC_FILES_RUNTIME_MAIN_APP]: dev\n                ? [\n                    reactRefreshEntry,\n                    `./` +\n                      path\n                        .relative(\n                          dir,\n                          path.join(\n                            NEXT_PROJECT_ROOT_DIST_CLIENT,\n                            'app-next-dev.js'\n                          )\n                        )\n                        .replace(/\\\\/g, '/'),\n                  ]\n                : [\n                    `./` +\n                      path\n                        .relative(\n                          dir,\n                          path.join(\n                            NEXT_PROJECT_ROOT_DIST_CLIENT,\n                            'app-next.js'\n                          )\n                        )\n                        .replace(/\\\\/g, '/'),\n                  ],\n            }\n          : {}),\n      } satisfies ClientEntries)\n    : undefined\n\n  const resolveConfig: webpack.Configuration['resolve'] = {\n    // Disable .mjs for node_modules bundling\n    extensions: ['.js', '.mjs', '.tsx', '.ts', '.jsx', '.json', '.wasm'],\n    extensionAlias: config.experimental.extensionAlias,\n    modules: [\n      'node_modules',\n      ...nodePathList, // Support for NODE_PATH environment variable\n    ],\n    alias: createWebpackAliases({\n      distDir,\n      isClient,\n      isEdgeServer,\n      isNodeServer,\n      dev,\n      config,\n      pagesDir,\n      appDir,\n      dir,\n      reactProductionProfiling,\n      hasRewrites,\n    }),\n    ...(isClient\n      ? {\n          fallback: {\n            process: require.resolve('./polyfills/process'),\n          },\n        }\n      : undefined),\n    // default main fields use pages dir ones, and customize app router ones in loaders.\n    mainFields: getMainField(compilerType, false),\n    ...(isEdgeServer && {\n      conditionNames: edgeConditionNames,\n    }),\n    plugins: [\n      isNodeServer ? new OptionalPeerDependencyResolverPlugin() : undefined,\n    ].filter(Boolean) as webpack.ResolvePluginInstance[],\n    ...((isRspack && jsConfigPath\n      ? {\n          tsConfig: {\n            configFile: jsConfigPath,\n          },\n        }\n      : {}) as any),\n  }\n\n  // Packages which will be split into the 'framework' chunk.\n  // Only top-level packages are included, e.g. nested copies like\n  // 'node_modules/meow/node_modules/object-assign' are not included.\n  const nextFrameworkPaths: string[] = []\n  const topLevelFrameworkPaths: string[] = []\n  const visitedFrameworkPackages = new Set<string>()\n  // Adds package-paths of dependencies recursively\n  const addPackagePath = (\n    packageName: string,\n    relativeToPath: string,\n    paths: string[]\n  ) => {\n    try {\n      if (visitedFrameworkPackages.has(packageName)) {\n        return\n      }\n      visitedFrameworkPackages.add(packageName)\n\n      const packageJsonPath = require.resolve(`${packageName}/package.json`, {\n        paths: [relativeToPath],\n      })\n\n      // Include a trailing slash so that a `.startsWith(packagePath)` check avoids false positives\n      // when one package name starts with the full name of a different package.\n      // For example:\n      //   \"node_modules/react-slider\".startsWith(\"node_modules/react\")  // true\n      //   \"node_modules/react-slider\".startsWith(\"node_modules/react/\") // false\n      const directory = path.join(packageJsonPath, '../')\n\n      // Returning from the function in case the directory has already been added and traversed\n      if (paths.includes(directory)) return\n      paths.push(directory)\n      const dependencies = require(packageJsonPath).dependencies || {}\n      for (const name of Object.keys(dependencies)) {\n        addPackagePath(name, directory, paths)\n      }\n    } catch (_) {\n      // don't error on failing to resolve framework packages\n    }\n  }\n\n  for (const packageName of [\n    'react',\n    'react-dom',\n    ...(hasAppDir\n      ? [\n          `next/dist/compiled/react${bundledReactChannel}`,\n          `next/dist/compiled/react-dom${bundledReactChannel}`,\n        ]\n      : []),\n  ]) {\n    addPackagePath(packageName, dir, topLevelFrameworkPaths)\n  }\n  addPackagePath('next', dir, nextFrameworkPaths)\n\n  const crossOrigin = config.crossOrigin\n\n  // The `serverExternalPackages` should not conflict with\n  // the `transpilePackages`.\n  if (config.serverExternalPackages && finalTranspilePackages) {\n    const externalPackageConflicts = finalTranspilePackages.filter((pkg) =>\n      config.serverExternalPackages?.includes(pkg)\n    )\n    if (externalPackageConflicts.length > 0) {\n      throw new Error(\n        `The packages specified in the 'transpilePackages' conflict with the 'serverExternalPackages': ${externalPackageConflicts.join(\n          ', '\n        )}`\n      )\n    }\n  }\n\n  // For original request, such as `package name`\n  const optOutBundlingPackages = EXTERNAL_PACKAGES.concat(\n    ...(config.serverExternalPackages || [])\n  ).filter((pkg) => !finalTranspilePackages?.includes(pkg))\n  // For resolved request, such as `absolute path/package name/foo/bar.js`\n  const optOutBundlingPackageRegex = new RegExp(\n    `[/\\\\\\\\]node_modules[/\\\\\\\\](${optOutBundlingPackages\n      .map((p) => p.replace(/\\//g, '[/\\\\\\\\]'))\n      .join('|')})[/\\\\\\\\]`\n  )\n\n  const transpilePackagesRegex = new RegExp(\n    `[/\\\\\\\\]node_modules[/\\\\\\\\](${finalTranspilePackages\n      ?.map((p) => p.replace(/\\//g, '[/\\\\\\\\]'))\n      .join('|')})[/\\\\\\\\]`\n  )\n\n  const handleExternals = makeExternalHandler({\n    config,\n    optOutBundlingPackageRegex,\n    transpiledPackages: finalTranspilePackages,\n    dir,\n  })\n\n  const pageExtensionsRegex = new RegExp(`\\\\.(${pageExtensions.join('|')})$`)\n\n  const aliasCodeConditionTest = [codeCondition.test, pageExtensionsRegex]\n\n  const builtinModules = require('module').builtinModules\n\n  const shouldEnableSlowModuleDetection =\n    !!config.experimental.slowModuleDetection && dev\n\n  const getParallelism = () => {\n    const override = Number(process.env.NEXT_WEBPACK_PARALLELISM)\n    if (shouldEnableSlowModuleDetection) {\n      if (override) {\n        console.warn(\n          'NEXT_WEBPACK_PARALLELISM is specified but will be ignored due to experimental.slowModuleDetection being enabled.'\n        )\n      }\n      return 1\n    }\n    return override || undefined\n  }\n\n  const telemetryPlugin =\n    !dev &&\n    isClient &&\n    new (\n      require('./webpack/plugins/telemetry-plugin/telemetry-plugin') as typeof import('./webpack/plugins/telemetry-plugin/telemetry-plugin')\n    ).TelemetryPlugin(\n      new Map(\n        [\n          ['swcLoader', useSWCLoader],\n          ['swcRelay', !!config.compiler?.relay],\n          ['swcStyledComponents', !!config.compiler?.styledComponents],\n          [\n            'swcReactRemoveProperties',\n            !!config.compiler?.reactRemoveProperties,\n          ],\n          [\n            'swcExperimentalDecorators',\n            !!jsConfig?.compilerOptions?.experimentalDecorators,\n          ],\n          ['swcRemoveConsole', !!config.compiler?.removeConsole],\n          ['swcImportSource', !!jsConfig?.compilerOptions?.jsxImportSource],\n          ['swcEmotion', !!config.compiler?.emotion],\n          ['transpilePackages', !!config.transpilePackages],\n          ['skipMiddlewareUrlNormalize', !!config.skipMiddlewareUrlNormalize],\n          ['skipTrailingSlashRedirect', !!config.skipTrailingSlashRedirect],\n          ['modularizeImports', !!config.modularizeImports],\n          // If esmExternals is not same as default value, it represents customized usage\n          ['esmExternals', config.experimental.esmExternals !== true],\n          SWCBinaryTarget,\n        ].filter<[Feature, boolean]>(Boolean as any)\n      )\n    )\n\n  let webpackConfig: webpack.Configuration = {\n    parallelism: getParallelism(),\n    ...(isNodeServer ? { externalsPresets: { node: true } } : {}),\n    // @ts-ignore\n    externals:\n      isClient || isEdgeServer\n        ? // make sure importing \"next\" is handled gracefully for client\n          // bundles in case a user imported types and it wasn't removed\n          // TODO: should we warn/error for this instead?\n          [\n            'next',\n            ...(isEdgeServer\n              ? [\n                  {\n                    '@builder.io/partytown': '{}',\n                    'next/dist/compiled/etag': '{}',\n                  },\n                  getEdgePolyfilledModules(),\n                  handleWebpackExternalForEdgeRuntime,\n                ]\n              : []),\n          ]\n        : [\n            ...builtinModules,\n            ({\n              context,\n              request,\n              dependencyType,\n              contextInfo,\n              getResolve,\n            }: {\n              context: string\n              request: string\n              dependencyType: string\n              contextInfo: {\n                issuer: string\n                issuerLayer: string | null\n                compiler: string\n              }\n              getResolve: (\n                options: any\n              ) => (\n                resolveContext: string,\n                resolveRequest: string,\n                callback: (\n                  err?: Error,\n                  result?: string,\n                  resolveData?: { descriptionFileData?: { type?: any } }\n                ) => void\n              ) => void\n            }) =>\n              handleExternals(\n                context,\n                request,\n                dependencyType,\n                contextInfo.issuerLayer as WebpackLayerName,\n                (options) => {\n                  const resolveFunction = getResolve(options)\n                  return (resolveContext: string, requestToResolve: string) =>\n                    new Promise((resolve, reject) => {\n                      resolveFunction(\n                        resolveContext,\n                        requestToResolve,\n                        (err, result, resolveData) => {\n                          if (err) return reject(err)\n                          if (!result) return resolve([null, false])\n                          const isEsm = /\\.js$/i.test(result)\n                            ? resolveData?.descriptionFileData?.type ===\n                              'module'\n                            : /\\.mjs$/i.test(result)\n                          resolve([result, isEsm])\n                        }\n                      )\n                    })\n                }\n              ),\n          ],\n\n    optimization: {\n      emitOnErrors: !dev,\n      checkWasmTypes: false,\n      nodeEnv: false,\n\n      splitChunks: (():\n        | Required<webpack.Configuration>['optimization']['splitChunks']\n        | false => {\n        // server chunking\n        if (dev) {\n          if (isNodeServer) {\n            /*\n              In development, we want to split code that comes from `node_modules` into their own chunks.\n              This is because in development, we often need to reload the user bundle due to changes in the code.\n              To work around this, we put all the vendor code into separate chunks so that we don't need to reload them.\n              This is safe because the vendor code doesn't change between reloads.\n            */\n            const extractRootNodeModule = (modulePath: string) => {\n              // This regex is used to extract the root node module name to be used as the chunk group name.\n              // example: ../../node_modules/.pnpm/next@10/foo/node_modules/bar -> next@10\n              const regex =\n                /node_modules(?:\\/|\\\\)\\.?(?:pnpm(?:\\/|\\\\))?([^/\\\\]+)/\n              const match = modulePath.match(regex)\n              return match ? match[1] : null\n            }\n            return {\n              cacheGroups: {\n                // this chunk configuration gives us a separate chunk for each top level module in node_modules\n                // or a hashed chunk if we can't extract the module name.\n                vendor: {\n                  chunks: 'all',\n                  reuseExistingChunk: true,\n                  test: /[\\\\/]node_modules[\\\\/]/,\n                  minSize: 0,\n                  minChunks: 1,\n                  maxAsyncRequests: 300,\n                  maxInitialRequests: 300,\n                  name: (module: webpack.Module) => {\n                    const moduleId = module.nameForCondition()!\n                    const rootModule = extractRootNodeModule(moduleId)\n                    if (rootModule) {\n                      return `vendor-chunks/${rootModule}`\n                    } else {\n                      const hash = crypto.createHash('sha1').update(moduleId)\n                      hash.update(moduleId)\n                      return `vendor-chunks/${hash.digest('hex')}`\n                    }\n                  },\n                },\n                // disable the default chunk groups\n                default: false,\n                defaultVendors: false,\n              },\n            }\n          }\n\n          return false\n        }\n\n        if (isNodeServer || isEdgeServer) {\n          return {\n            filename: `${isEdgeServer ? `edge-chunks/` : ''}[name].js`,\n            chunks: 'all',\n            minChunks: 2,\n          }\n        }\n\n        const frameworkCacheGroup = {\n          chunks: 'all' as const,\n          name: 'framework',\n          // Ensures the framework chunk is not created for App Router.\n          layer: isWebpackDefaultLayer,\n          test(module: any) {\n            const resource = module.nameForCondition?.()\n            return resource\n              ? topLevelFrameworkPaths.some((pkgPath) =>\n                  resource.startsWith(pkgPath)\n                )\n              : false\n          },\n          priority: 40,\n          // Don't let webpack eliminate this chunk (prevents this chunk from\n          // becoming a part of the commons chunk)\n          enforce: true,\n        }\n\n        const libCacheGroup = {\n          test(module: {\n            type: string\n            size: Function\n            nameForCondition: Function\n          }): boolean {\n            return (\n              !module.type?.startsWith('css') &&\n              module.size() > 160000 &&\n              /node_modules[/\\\\]/.test(module.nameForCondition() || '')\n            )\n          },\n          name(module: {\n            layer: string | null | undefined\n            type: string\n            libIdent?: Function\n            updateHash: (hash: crypto.Hash) => void\n          }): string {\n            const hash = crypto.createHash('sha1')\n            if (isModuleCSS(module)) {\n              module.updateHash(hash)\n            } else {\n              if (!module.libIdent) {\n                throw new Error(\n                  `Encountered unknown module type: ${module.type}. Please open an issue.`\n                )\n              }\n              hash.update(module.libIdent({ context: dir }))\n            }\n\n            // Ensures the name of the chunk is not the same between two modules in different layers\n            // E.g. if you import 'button-library' in App Router and Pages Router we don't want these to be bundled in the same chunk\n            // as they're never used on the same page.\n            if (module.layer) {\n              hash.update(module.layer)\n            }\n\n            return hash.digest('hex').substring(0, 8)\n          },\n          priority: 30,\n          minChunks: 1,\n          reuseExistingChunk: true,\n        }\n\n        // client chunking\n        return {\n          // Keep main and _app chunks unsplitted in webpack 5\n          // as we don't need a separate vendor chunk from that\n          // and all other chunk depend on them so there is no\n          // duplication that need to be pulled out.\n          chunks: isRspack\n            ? // using a function here causes noticable slowdown\n              // in rspack\n              /(?!polyfills|main|pages\\/_app)/\n            : (chunk: any) =>\n                !/^(polyfills|main|pages\\/_app)$/.test(chunk.name),\n\n          // TODO: investigate these cache groups with rspack\n          cacheGroups: isRspack\n            ? {}\n            : {\n                framework: frameworkCacheGroup,\n                lib: libCacheGroup,\n              },\n          maxInitialRequests: 25,\n          minSize: 20000,\n        }\n      })(),\n      runtimeChunk: isClient\n        ? { name: CLIENT_STATIC_FILES_RUNTIME_WEBPACK }\n        : undefined,\n\n      minimize:\n        !dev &&\n        (isClient ||\n          isEdgeServer ||\n          (isNodeServer && config.experimental.serverMinification)),\n      minimizer: isRspack\n        ? [\n            new (getRspackCore().SwcJsMinimizerRspackPlugin)({\n              // JS minimizer configuration\n              // options should align with crates/napi/src/minify.rs#patch_opts\n              minimizerOptions: {\n                compress: {\n                  inline: 2,\n                  global_defs: {\n                    'process.env.__NEXT_PRIVATE_MINIMIZE_MACRO_FALSE': false,\n                  },\n                },\n                mangle: !noMangling && {\n                  reserved: ['AbortSignal'],\n                  disableCharFreq: !isClient,\n                },\n              },\n            }),\n            new (getRspackCore().LightningCssMinimizerRspackPlugin)({\n              // CSS minimizer configuration\n              minimizerOptions: {\n                targets: supportedBrowsers,\n              },\n            }),\n          ]\n        : [\n            // Minify JavaScript\n            (compiler: webpack.Compiler) => {\n              // @ts-ignore No typings yet\n              const { MinifyPlugin } =\n                require('./webpack/plugins/minify-webpack-plugin/src/index.js') as typeof import('./webpack/plugins/minify-webpack-plugin/src')\n              new MinifyPlugin({ noMangling }).apply(compiler)\n            },\n            // Minify CSS\n            (compiler: webpack.Compiler) => {\n              const {\n                CssMinimizerPlugin,\n              } = require('./webpack/plugins/css-minimizer-plugin')\n              new CssMinimizerPlugin({\n                postcssOptions: {\n                  map: {\n                    // `inline: false` generates the source map in a separate file.\n                    // Otherwise, the CSS file is needlessly large.\n                    inline: false,\n                    // `annotation: false` skips appending the `sourceMappingURL`\n                    // to the end of the CSS file. Webpack already handles this.\n                    annotation: false,\n                  },\n                },\n              }).apply(compiler)\n            },\n          ],\n    },\n    context: dir,\n    // Kept as function to be backwards compatible\n    entry: async () => {\n      return {\n        ...(clientEntries ? clientEntries : {}),\n        ...entrypoints,\n      }\n    },\n    watchOptions: Object.freeze({\n      ...baseWatchOptions,\n      poll: config.watchOptions?.pollIntervalMs,\n    }),\n    output: {\n      // we must set publicPath to an empty value to override the default of\n      // auto which doesn't work in IE11\n      publicPath: `${\n        config.assetPrefix\n          ? config.assetPrefix.endsWith('/')\n            ? config.assetPrefix.slice(0, -1)\n            : config.assetPrefix\n          : ''\n      }/_next/`,\n      path: !dev && isNodeServer ? path.join(outputPath, 'chunks') : outputPath,\n      // On the server we don't use hashes\n      filename: isNodeOrEdgeCompilation\n        ? dev || isEdgeServer\n          ? `[name].js`\n          : `../[name].js`\n        : `static/chunks/${isDevFallback ? 'fallback/' : ''}[name]${\n            dev ? '' : appDir ? '-[chunkhash]' : '-[contenthash]'\n          }.js`,\n      library: isClient || isEdgeServer ? '_N_E' : undefined,\n      libraryTarget: isClient || isEdgeServer ? 'assign' : 'commonjs2',\n      hotUpdateChunkFilename: 'static/webpack/[id].[fullhash].hot-update.js',\n      hotUpdateMainFilename:\n        'static/webpack/[fullhash].[runtime].hot-update.json',\n      // This saves chunks with the name given via `import()`\n      chunkFilename: isNodeOrEdgeCompilation\n        ? '[name].js'\n        : `static/chunks/${isDevFallback ? 'fallback/' : ''}${\n            dev ? '[name]' : '[name].[contenthash]'\n          }.js`,\n      strictModuleExceptionHandling: true,\n      crossOriginLoading: crossOrigin,\n      // if `sources[number]` is not an absolute path, it's is resolved\n      // relative to the location of the source map file (https://tc39.es/source-map/#resolving-sources).\n      // However, Webpack's `resource-path` is relative to the app dir.\n      // TODO: Either `sourceRoot` should be populated with the root and then we can use `[resource-path]`\n      // or we need a way to resolve return `path.relative(sourceMapLocation, info.resourcePath)`\n      devtoolModuleFilenameTemplate: dev\n        ? '[absolute-resource-path]'\n        : undefined,\n      webassemblyModuleFilename: 'static/wasm/[modulehash].wasm',\n      hashFunction: 'xxhash64',\n      hashDigestLength: 16,\n    },\n    performance: false,\n    resolve: resolveConfig,\n    resolveLoader: {\n      // The loaders Next.js provides\n      alias: [\n        'error-loader',\n        'next-swc-loader',\n        'next-client-pages-loader',\n        'next-image-loader',\n        'next-metadata-image-loader',\n        'next-style-loader',\n        'next-flight-loader',\n        'next-flight-client-entry-loader',\n        'next-flight-action-entry-loader',\n        'next-flight-client-module-loader',\n        'next-flight-server-reference-proxy-loader',\n        'empty-loader',\n        'next-middleware-loader',\n        'next-edge-function-loader',\n        'next-edge-app-route-loader',\n        'next-edge-ssr-loader',\n        'next-middleware-asset-loader',\n        'next-middleware-wasm-loader',\n        'next-app-loader',\n        'next-route-loader',\n        'next-font-loader',\n        'next-invalid-import-error-loader',\n        'next-metadata-route-loader',\n        'modularize-import-loader',\n        'next-barrel-loader',\n        'next-error-browser-binary-loader',\n      ].reduce(\n        (alias, loader) => {\n          // using multiple aliases to replace `resolveLoader.modules`\n          alias[loader] = path.join(__dirname, 'webpack', 'loaders', loader)\n\n          return alias\n        },\n        {} as Record<string, string>\n      ),\n      modules: [\n        'node_modules',\n        ...nodePathList, // Support for NODE_PATH environment variable\n      ],\n      plugins: [],\n    },\n    module: {\n      rules: [\n        // Alias server-only and client-only to proper exports based on bundling layers\n        {\n          issuerLayer: {\n            or: [\n              ...WEBPACK_LAYERS.GROUP.serverOnly,\n              ...WEBPACK_LAYERS.GROUP.neutralTarget,\n            ],\n          },\n          resolve: {\n            // Error on client-only but allow server-only\n            alias: createServerOnlyClientOnlyAliases(true),\n          },\n        },\n        {\n          issuerLayer: {\n            not: [\n              ...WEBPACK_LAYERS.GROUP.serverOnly,\n              ...WEBPACK_LAYERS.GROUP.neutralTarget,\n            ],\n          },\n          resolve: {\n            // Error on server-only but allow client-only\n            alias: createServerOnlyClientOnlyAliases(false),\n          },\n        },\n        // Detect server-only / client-only imports and error in build time\n        {\n          test: [\n            /^client-only$/,\n            /next[\\\\/]dist[\\\\/]compiled[\\\\/]client-only[\\\\/]error/,\n          ],\n          loader: 'next-invalid-import-error-loader',\n          issuerLayer: {\n            or: WEBPACK_LAYERS.GROUP.serverOnly,\n          },\n          options: {\n            message:\n              \"'client-only' cannot be imported from a Server Component module. It should only be used from a Client Component.\",\n          },\n        },\n        {\n          test: [\n            /^server-only$/,\n            /next[\\\\/]dist[\\\\/]compiled[\\\\/]server-only[\\\\/]index/,\n          ],\n          loader: 'next-invalid-import-error-loader',\n          issuerLayer: {\n            not: [\n              ...WEBPACK_LAYERS.GROUP.serverOnly,\n              ...WEBPACK_LAYERS.GROUP.neutralTarget,\n            ],\n          },\n          options: {\n            message:\n              \"'server-only' cannot be imported from a Client Component module. It should only be used from a Server Component.\",\n          },\n        },\n        // Potential the bundle introduced into middleware and api can be poisoned by client-only\n        // but not being used, so we disabled the `client-only` erroring on these layers.\n        // `server-only` is still available.\n        {\n          test: [\n            /^client-only$/,\n            /next[\\\\/]dist[\\\\/]compiled[\\\\/]client-only[\\\\/]error/,\n          ],\n          loader: 'empty-loader',\n          issuerLayer: {\n            or: WEBPACK_LAYERS.GROUP.neutralTarget,\n          },\n        },\n        ...(isNodeServer\n          ? []\n          : [\n              {\n                test: /[\\\\/].*?\\.node$/,\n                loader: 'next-error-browser-binary-loader',\n              },\n            ]),\n        ...(hasAppDir\n          ? [\n              {\n                // Make sure that AsyncLocalStorage module instance is shared between server and client\n                // layers.\n                layer: WEBPACK_LAYERS.shared,\n                test: asyncStoragesRegex,\n              },\n              // Convert metadata routes to separate layer\n              {\n                resourceQuery: new RegExp(\n                  WEBPACK_RESOURCE_QUERIES.metadataRoute\n                ),\n                layer: WEBPACK_LAYERS.reactServerComponents,\n              },\n              {\n                // Ensure that the app page module is in the client layers, this\n                // enables React to work correctly for RSC.\n                layer: WEBPACK_LAYERS.serverSideRendering,\n                test: /next[\\\\/]dist[\\\\/](esm[\\\\/])?server[\\\\/]route-modules[\\\\/]app-page[\\\\/]module/,\n              },\n              {\n                issuerLayer: isWebpackBundledLayer,\n                resolve: {\n                  alias: createNextApiEsmAliases(),\n                },\n              },\n              {\n                issuerLayer: isWebpackServerOnlyLayer,\n                resolve: {\n                  alias: createAppRouterApiAliases(true),\n                },\n              },\n              {\n                issuerLayer: isWebpackClientOnlyLayer,\n                resolve: {\n                  alias: createAppRouterApiAliases(false),\n                },\n              },\n            ]\n          : []),\n        ...(hasAppDir && !isClient\n          ? [\n              {\n                issuerLayer: isWebpackServerOnlyLayer,\n                test: {\n                  // Resolve it if it is a source code file, and it has NOT been\n                  // opted out of bundling.\n                  and: [\n                    aliasCodeConditionTest,\n                    {\n                      not: [optOutBundlingPackageRegex, asyncStoragesRegex],\n                    },\n                  ],\n                },\n                resourceQuery: {\n                  // Do not apply next-flight-loader to imports generated by the\n                  // next-metadata-image-loader, to avoid generating unnecessary\n                  // and conflicting entries in the flight client entry plugin.\n                  // These are already covered by the next-metadata-route-loader\n                  // entries.\n                  not: [\n                    new RegExp(WEBPACK_RESOURCE_QUERIES.metadata),\n                    new RegExp(WEBPACK_RESOURCE_QUERIES.metadataImageMeta),\n                  ],\n                },\n                resolve: {\n                  mainFields: getMainField(compilerType, true),\n                  conditionNames: reactServerCondition,\n                  // If missing the alias override here, the default alias will be used which aliases\n                  // react to the direct file path, not the package name. In that case the condition\n                  // will be ignored completely.\n                  alias: createRSCAliases(bundledReactChannel, {\n                    // No server components profiling\n                    reactProductionProfiling,\n                    layer: WEBPACK_LAYERS.reactServerComponents,\n                    isEdgeServer,\n                  }),\n                },\n                use: 'next-flight-loader',\n              },\n            ]\n          : []),\n        // TODO: FIXME: do NOT webpack 5 support with this\n        // x-ref: https://github.com/webpack/webpack/issues/11467\n        ...(!config.experimental.fullySpecified\n          ? [\n              {\n                test: /\\.m?js/,\n                resolve: {\n                  fullySpecified: false,\n                },\n              } as any,\n            ]\n          : []),\n        ...(hasAppDir && isEdgeServer\n          ? [\n              // The Edge bundle includes the server in its entrypoint, so it has to\n              // be in the SSR layer — here we convert the actual page request to\n              // the RSC layer via a webpack rule.\n              {\n                resourceQuery: new RegExp(\n                  WEBPACK_RESOURCE_QUERIES.edgeSSREntry\n                ),\n                layer: WEBPACK_LAYERS.reactServerComponents,\n              },\n            ]\n          : []),\n        ...(hasAppDir\n          ? [\n              {\n                // Alias react-dom for ReactDOM.preload usage.\n                // Alias react for switching between default set and share subset.\n                oneOf: [\n                  {\n                    issuerLayer: isWebpackServerOnlyLayer,\n                    test: {\n                      // Resolve it if it is a source code file, and it has NOT been\n                      // opted out of bundling.\n                      and: [\n                        aliasCodeConditionTest,\n                        {\n                          not: [optOutBundlingPackageRegex, asyncStoragesRegex],\n                        },\n                      ],\n                    },\n                    resolve: {\n                      // It needs `conditionNames` here to require the proper asset,\n                      // when react is acting as dependency of compiled/react-dom.\n                      alias: createRSCAliases(bundledReactChannel, {\n                        reactProductionProfiling,\n                        layer: WEBPACK_LAYERS.reactServerComponents,\n                        isEdgeServer,\n                      }),\n                    },\n                  },\n                  {\n                    test: aliasCodeConditionTest,\n                    issuerLayer: WEBPACK_LAYERS.serverSideRendering,\n                    resolve: {\n                      alias: createRSCAliases(bundledReactChannel, {\n                        reactProductionProfiling,\n                        layer: WEBPACK_LAYERS.serverSideRendering,\n                        isEdgeServer,\n                      }),\n                    },\n                  },\n                ],\n              },\n              {\n                test: aliasCodeConditionTest,\n                issuerLayer: WEBPACK_LAYERS.appPagesBrowser,\n                resolve: {\n                  alias: createRSCAliases(bundledReactChannel, {\n                    reactProductionProfiling,\n                    layer: WEBPACK_LAYERS.appPagesBrowser,\n                    isEdgeServer,\n                  }),\n                },\n              },\n            ]\n          : []),\n        // Do not apply react-refresh-loader to node_modules for app router browser layer\n        ...(hasAppDir && dev && isClient\n          ? [\n              {\n                test: codeCondition.test,\n                exclude: [\n                  // exclude unchanged modules from react-refresh\n                  codeCondition.exclude,\n                  transpilePackagesRegex,\n                  precompileRegex,\n                ],\n                issuerLayer: WEBPACK_LAYERS.appPagesBrowser,\n                use: reactRefreshLoaders,\n                resolve: {\n                  mainFields: getMainField(compilerType, true),\n                },\n              },\n            ]\n          : []),\n        {\n          oneOf: [\n            {\n              ...codeCondition,\n              issuerLayer: WEBPACK_LAYERS.apiNode,\n              use: apiRoutesLayerLoaders,\n              // In Node.js, switch back to normal URL handling.\n              // We won't bundle `new URL()` cases in Node.js bundler layer.\n              parser: {\n                url: true,\n              },\n            },\n            {\n              ...codeCondition,\n              issuerLayer: WEBPACK_LAYERS.apiEdge,\n              use: apiRoutesLayerLoaders,\n              // In Edge runtime, we leave the url handling by default.\n              // The new URL assets will be converted into edge assets through assets loader.\n            },\n            {\n              test: codeCondition.test,\n              issuerLayer: WEBPACK_LAYERS.middleware,\n              use: middlewareLayerLoaders,\n              resolve: {\n                mainFields: getMainField(compilerType, true),\n                conditionNames: reactServerCondition,\n                alias: createRSCAliases(bundledReactChannel, {\n                  reactProductionProfiling,\n                  layer: WEBPACK_LAYERS.middleware,\n                  isEdgeServer,\n                }),\n              },\n            },\n            {\n              test: codeCondition.test,\n              issuerLayer: WEBPACK_LAYERS.instrument,\n              use: instrumentLayerLoaders,\n              resolve: {\n                mainFields: getMainField(compilerType, true),\n                conditionNames: reactServerCondition,\n                alias: createRSCAliases(bundledReactChannel, {\n                  reactProductionProfiling,\n                  layer: WEBPACK_LAYERS.instrument,\n                  isEdgeServer,\n                }),\n              },\n            },\n            ...(hasAppDir\n              ? [\n                  {\n                    test: codeCondition.test,\n                    issuerLayer: isWebpackServerOnlyLayer,\n                    exclude: asyncStoragesRegex,\n                    use: appServerLayerLoaders,\n                  },\n                  {\n                    test: codeCondition.test,\n                    resourceQuery: new RegExp(\n                      WEBPACK_RESOURCE_QUERIES.edgeSSREntry\n                    ),\n                    use: appServerLayerLoaders,\n                  },\n                  {\n                    test: codeCondition.test,\n                    issuerLayer: WEBPACK_LAYERS.appPagesBrowser,\n                    // Exclude the transpilation of the app layer due to compilation issues\n                    exclude: browserNonTranspileModules,\n                    use: appBrowserLayerLoaders,\n                    resolve: {\n                      mainFields: getMainField(compilerType, true),\n                    },\n                  },\n                  {\n                    test: codeCondition.test,\n                    issuerLayer: WEBPACK_LAYERS.serverSideRendering,\n                    exclude: asyncStoragesRegex,\n                    use: appSSRLayerLoaders,\n                    resolve: {\n                      mainFields: getMainField(compilerType, true),\n                    },\n                  },\n                ]\n              : []),\n            {\n              ...codeCondition,\n              use: [\n                ...reactRefreshLoaders,\n                defaultLoaders.babel,\n                reactCompilerLoader,\n              ].filter(Boolean),\n            },\n          ],\n        },\n\n        ...(!config.images.disableStaticImages\n          ? [\n              {\n                test: nextImageLoaderRegex,\n                loader: 'next-image-loader',\n                issuer: { not: regexLikeCss },\n                dependency: { not: ['url'] },\n                resourceQuery: {\n                  not: [\n                    new RegExp(WEBPACK_RESOURCE_QUERIES.metadata),\n                    new RegExp(WEBPACK_RESOURCE_QUERIES.metadataRoute),\n                    new RegExp(WEBPACK_RESOURCE_QUERIES.metadataImageMeta),\n                  ],\n                },\n                options: {\n                  isDev: dev,\n                  compilerType,\n                  basePath: config.basePath,\n                  assetPrefix: config.assetPrefix,\n                },\n              },\n            ]\n          : []),\n        ...(isEdgeServer\n          ? [\n              {\n                resolve: {\n                  fallback: {\n                    process: require.resolve('./polyfills/process'),\n                  },\n                },\n              },\n            ]\n          : isClient\n            ? [\n                {\n                  resolve: {\n                    fallback:\n                      config.experimental.fallbackNodePolyfills === false\n                        ? {\n                            assert: false,\n                            buffer: false,\n                            constants: false,\n                            crypto: false,\n                            domain: false,\n                            http: false,\n                            https: false,\n                            os: false,\n                            path: false,\n                            punycode: false,\n                            process: false,\n                            querystring: false,\n                            stream: false,\n                            string_decoder: false,\n                            sys: false,\n                            timers: false,\n                            tty: false,\n                            util: false,\n                            vm: false,\n                            zlib: false,\n                            events: false,\n                            setImmediate: false,\n                          }\n                        : {\n                            assert: require.resolve(\n                              'next/dist/compiled/assert'\n                            ),\n                            buffer: require.resolve(\n                              'next/dist/compiled/buffer'\n                            ),\n                            constants: require.resolve(\n                              'next/dist/compiled/constants-browserify'\n                            ),\n                            crypto: require.resolve(\n                              'next/dist/compiled/crypto-browserify'\n                            ),\n                            domain: require.resolve(\n                              'next/dist/compiled/domain-browser'\n                            ),\n                            http: require.resolve(\n                              'next/dist/compiled/stream-http'\n                            ),\n                            https: require.resolve(\n                              'next/dist/compiled/https-browserify'\n                            ),\n                            os: require.resolve(\n                              'next/dist/compiled/os-browserify'\n                            ),\n                            path: require.resolve(\n                              'next/dist/compiled/path-browserify'\n                            ),\n                            punycode: require.resolve(\n                              'next/dist/compiled/punycode'\n                            ),\n                            process: require.resolve('./polyfills/process'),\n                            // Handled in separate alias\n                            querystring: require.resolve(\n                              'next/dist/compiled/querystring-es3'\n                            ),\n                            stream: require.resolve(\n                              'next/dist/compiled/stream-browserify'\n                            ),\n                            string_decoder: require.resolve(\n                              'next/dist/compiled/string_decoder'\n                            ),\n                            sys: require.resolve('next/dist/compiled/util'),\n                            timers: require.resolve(\n                              'next/dist/compiled/timers-browserify'\n                            ),\n                            tty: require.resolve(\n                              'next/dist/compiled/tty-browserify'\n                            ),\n                            // Handled in separate alias\n                            // url: require.resolve('url'),\n                            util: require.resolve('next/dist/compiled/util'),\n                            vm: require.resolve(\n                              'next/dist/compiled/vm-browserify'\n                            ),\n                            zlib: require.resolve(\n                              'next/dist/compiled/browserify-zlib'\n                            ),\n                            events: require.resolve(\n                              'next/dist/compiled/events'\n                            ),\n                            setImmediate: require.resolve(\n                              'next/dist/compiled/setimmediate'\n                            ),\n                          },\n                  },\n                },\n              ]\n            : []),\n        {\n          // Mark `image-response.js` as side-effects free to make sure we can\n          // tree-shake it if not used.\n          test: /[\\\\/]next[\\\\/]dist[\\\\/](esm[\\\\/])?server[\\\\/]og[\\\\/]image-response\\.js/,\n          sideEffects: false,\n        },\n        // Mark the action-client-wrapper module as side-effects free to make sure\n        // the individual transformed module of client action can be tree-shaken.\n        // This will make modules processed by `next-flight-server-reference-proxy-loader` become side-effects free,\n        // then on client side the module ids will become tree-shakable.\n        // e.g. the output of client action module will look like:\n        // `export { a } from 'next-flight-server-reference-proxy-loader?id=idOfA&name=a!\n        // `export { b } from 'next-flight-server-reference-proxy-loader?id=idOfB&name=b!\n        {\n          test: /[\\\\/]next[\\\\/]dist[\\\\/](esm[\\\\/])?build[\\\\/]webpack[\\\\/]loaders[\\\\/]next-flight-loader[\\\\/]action-client-wrapper\\.js/,\n          sideEffects: false,\n        },\n        {\n          // This loader rule should be before other rules, as it can output code\n          // that still contains `\"use client\"` or `\"use server\"` statements that\n          // needs to be re-transformed by the RSC compilers.\n          // This loader rule works like a bridge between user's import and\n          // the target module behind a package's barrel file. It reads SWC's\n          // analysis result from the previous loader, and directly returns the\n          // code that only exports values that are asked by the user.\n          test: /__barrel_optimize__/,\n          use: ({ resourceQuery }: { resourceQuery: string }) => {\n            const names = (\n              resourceQuery.match(/\\?names=([^&]+)/)?.[1] || ''\n            ).split(',')\n\n            return [\n              {\n                loader: 'next-barrel-loader',\n                options: {\n                  names,\n                  swcCacheDir: path.join(\n                    dir,\n                    config?.distDir ?? '.next',\n                    'cache',\n                    'swc'\n                  ),\n                },\n                // This is part of the request value to serve as the module key.\n                // The barrel loader are no-op re-exported modules keyed by\n                // export names.\n                ident: 'next-barrel-loader:' + resourceQuery,\n              },\n            ]\n          },\n        },\n        {\n          resolve: {\n            alias: {\n              next: NEXT_PROJECT_ROOT,\n            },\n          },\n        },\n      ],\n    },\n    plugins: [\n      isNodeServer &&\n        new bundler.NormalModuleReplacementPlugin(\n          /\\.\\/(.+)\\.shared-runtime$/,\n          function (resource) {\n            const moduleName = path.basename(\n              resource.request,\n              '.shared-runtime'\n            )\n            const layer = resource.contextInfo.issuerLayer\n            let runtime\n\n            switch (layer) {\n              case WEBPACK_LAYERS.serverSideRendering:\n              case WEBPACK_LAYERS.reactServerComponents:\n              case WEBPACK_LAYERS.appPagesBrowser:\n              case WEBPACK_LAYERS.actionBrowser:\n                runtime = 'app-page'\n                break\n              default:\n                runtime = 'pages'\n            }\n            resource.request = `next/dist/server/route-modules/${runtime}/vendored/contexts/${moduleName}`\n          }\n        ),\n      dev && new MemoryWithGcCachePlugin({ maxGenerations: 5 }),\n      dev &&\n        isClient &&\n        (isRspack\n          ? // eslint-disable-next-line\n            new (getRspackReactRefresh() as any)({\n              injectLoader: false,\n              injectEntry: false,\n              overlay: false,\n            })\n          : new ReactRefreshWebpackPlugin(webpack)),\n      // Makes sure `Buffer` and `process` are polyfilled in client and flight bundles (same behavior as webpack 4)\n      (isClient || isEdgeServer) &&\n        new bundler.ProvidePlugin({\n          // Buffer is used by getInlineScriptSource\n          Buffer: [require.resolve('buffer'), 'Buffer'],\n          // Avoid process being overridden when in web run time\n          ...(isClient && { process: [require.resolve('process')] }),\n        }),\n      getDefineEnvPlugin({\n        isTurbopack: false,\n        config,\n        dev,\n        distDir,\n        fetchCacheKeyPrefix,\n        hasRewrites,\n        isClient,\n        isEdgeServer,\n        isNodeOrEdgeCompilation,\n        isNodeServer,\n        middlewareMatchers,\n        omitNonDeterministic: isCompileMode,\n      }),\n      isClient &&\n        new ReactLoadablePlugin({\n          filename: REACT_LOADABLE_MANIFEST,\n          pagesDir,\n          appDir,\n          runtimeAsset: `server/${MIDDLEWARE_REACT_LOADABLE_MANIFEST}.js`,\n          dev,\n        }),\n      // rspack doesn't support the parser hooks used here\n      !isRspack && (isClient || isEdgeServer) && new DropClientPage(),\n      isNodeServer &&\n        !dev &&\n        new (require('./webpack/plugins/next-trace-entrypoints-plugin')\n          .TraceEntryPointsPlugin as typeof import('./webpack/plugins/next-trace-entrypoints-plugin').TraceEntryPointsPlugin)(\n          {\n            rootDir: dir,\n            appDir: appDir,\n            pagesDir: pagesDir,\n            esmExternals: config.experimental.esmExternals,\n            outputFileTracingRoot: config.outputFileTracingRoot,\n            appDirEnabled: hasAppDir,\n            traceIgnores: [],\n            compilerType,\n          }\n        ),\n      // Moment.js is an extremely popular library that bundles large locale files\n      // by default due to how Webpack interprets its code. This is a practical\n      // solution that requires the user to opt into importing specific locales.\n      // https://github.com/jmblog/how-to-optimize-momentjs-with-webpack\n      config.excludeDefaultMomentLocales &&\n        new bundler.IgnorePlugin({\n          resourceRegExp: /^\\.\\/locale$/,\n          contextRegExp: /moment$/,\n        }),\n      ...(dev\n        ? (() => {\n            // Even though require.cache is server only we have to clear assets from both compilations\n            // This is because the client compilation generates the build manifest that's used on the server side\n            const { NextJsRequireCacheHotReloader } =\n              require('./webpack/plugins/nextjs-require-cache-hot-reloader') as typeof import('./webpack/plugins/nextjs-require-cache-hot-reloader')\n            const devPlugins: any[] = [\n              new NextJsRequireCacheHotReloader({\n                serverComponents: hasAppDir,\n              }),\n            ]\n\n            if (isClient || isEdgeServer) {\n              devPlugins.push(new bundler.HotModuleReplacementPlugin())\n            }\n\n            return devPlugins\n          })()\n        : []),\n      !dev &&\n        new bundler.IgnorePlugin({\n          resourceRegExp: /react-is/,\n          contextRegExp: /next[\\\\/]dist[\\\\/]/,\n        }),\n      isNodeOrEdgeCompilation &&\n        new PagesManifestPlugin({\n          dev,\n          appDirEnabled: hasAppDir,\n          isEdgeRuntime: isEdgeServer,\n          distDir: !dev ? distDir : undefined,\n        }),\n      // MiddlewarePlugin should be after DefinePlugin so NEXT_PUBLIC_*\n      // replacement is done before its process.env.* handling\n      isEdgeServer &&\n        new MiddlewarePlugin({\n          dev,\n          sriEnabled: !dev && !!config.experimental.sri?.algorithm,\n          rewrites,\n          edgeEnvironments: {\n            __NEXT_BUILD_ID: buildId,\n            NEXT_SERVER_ACTIONS_ENCRYPTION_KEY: encryptionKey,\n            ...edgePreviewProps,\n          },\n        }),\n      isClient &&\n        new BuildManifestPlugin({\n          buildId,\n          rewrites,\n          isDevFallback,\n          appDirEnabled: hasAppDir,\n          clientRouterFilters,\n        }),\n      isRspack\n        ? new RspackProfilingPlugin({ runWebpackSpan })\n        : new ProfilingPlugin({ runWebpackSpan, rootDir: dir }),\n      new WellKnownErrorsPlugin(),\n      isClient &&\n        new CopyFilePlugin({\n          // file path to build output of `@next/polyfill-nomodule`\n          filePath: require.resolve('./polyfills/polyfill-nomodule'),\n          cacheKey: process.env.__NEXT_VERSION as string,\n          name: `static/chunks/polyfills${dev ? '' : '-[hash]'}.js`,\n          minimize: false,\n          info: {\n            [CLIENT_STATIC_FILES_RUNTIME_POLYFILLS_SYMBOL]: 1,\n            // This file is already minified\n            minimized: true,\n          },\n        }),\n      hasAppDir && isClient && new AppBuildManifestPlugin({ dev }),\n      hasAppDir &&\n        (isClient\n          ? new ClientReferenceManifestPlugin({\n              dev,\n              appDir,\n              experimentalInlineCss: !!config.experimental.inlineCss,\n            })\n          : new FlightClientEntryPlugin({\n              appDir,\n              dev,\n              isEdgeServer,\n              encryptionKey,\n            })),\n      hasAppDir &&\n        !isClient &&\n        new NextTypesPlugin({\n          dir,\n          distDir: config.distDir,\n          appDir,\n          dev,\n          isEdgeServer,\n          pageExtensions: config.pageExtensions,\n          typedRoutes: enableTypedRoutes,\n          cacheLifeConfig: config.experimental.cacheLife,\n          originalRewrites,\n          originalRedirects,\n        }),\n      !dev &&\n        isClient &&\n        !!config.experimental.sri?.algorithm &&\n        new SubresourceIntegrityPlugin(config.experimental.sri.algorithm),\n      isClient &&\n        new NextFontManifestPlugin({\n          appDir,\n        }),\n      !isRspack &&\n        !dev &&\n        isClient &&\n        config.experimental.cssChunking &&\n        new CssChunkingPlugin(config.experimental.cssChunking === 'strict'),\n      telemetryPlugin,\n      !dev &&\n        isNodeServer &&\n        new (\n          require('./webpack/plugins/telemetry-plugin/telemetry-plugin') as typeof import('./webpack/plugins/telemetry-plugin/telemetry-plugin')\n        ).TelemetryPlugin(new Map()),\n      shouldEnableSlowModuleDetection &&\n        new (\n          require('./webpack/plugins/slow-module-detection-plugin') as typeof import('./webpack/plugins/slow-module-detection-plugin')\n        ).default({\n          compilerType,\n          ...config.experimental.slowModuleDetection!,\n        }),\n    ].filter(Boolean as any as ExcludesFalse),\n  }\n\n  // Support tsconfig and jsconfig baseUrl\n  // Only add the baseUrl if it's explicitly set in tsconfig/jsconfig\n  if (resolvedBaseUrl && !resolvedBaseUrl.isImplicit) {\n    webpackConfig.resolve?.modules?.push(resolvedBaseUrl.baseUrl)\n  }\n\n  // always add JsConfigPathsPlugin to allow hot-reloading\n  // if the config is added/removed\n  webpackConfig.resolve?.plugins?.unshift(\n    new JsConfigPathsPlugin(\n      jsConfig?.compilerOptions?.paths || {},\n      resolvedBaseUrl\n    )\n  )\n\n  const webpack5Config = webpackConfig as webpack.Configuration\n\n  if (isEdgeServer) {\n    webpack5Config.module?.rules?.unshift({\n      test: /\\.wasm$/,\n      loader: 'next-middleware-wasm-loader',\n      type: 'javascript/auto',\n      resourceQuery: /module/i,\n    })\n    webpack5Config.module?.rules?.unshift({\n      dependency: 'url',\n      loader: 'next-middleware-asset-loader',\n      type: 'javascript/auto',\n      layer: WEBPACK_LAYERS.edgeAsset,\n    })\n    webpack5Config.module?.rules?.unshift({\n      issuerLayer: WEBPACK_LAYERS.edgeAsset,\n      type: 'asset/source',\n    })\n  }\n\n  webpack5Config.experiments = {\n    layers: true,\n    cacheUnaffected: true,\n    buildHttp: Array.isArray(config.experimental.urlImports)\n      ? {\n          allowedUris: config.experimental.urlImports,\n          cacheLocation: path.join(dir, 'next.lock/data'),\n          lockfileLocation: path.join(dir, 'next.lock/lock.json'),\n        }\n      : config.experimental.urlImports\n        ? {\n            cacheLocation: path.join(dir, 'next.lock/data'),\n            lockfileLocation: path.join(dir, 'next.lock/lock.json'),\n            ...config.experimental.urlImports,\n          }\n        : undefined,\n  }\n\n  webpack5Config.module!.parser = {\n    javascript: {\n      url: 'relative',\n    },\n  }\n  webpack5Config.module!.generator = {\n    asset: {\n      filename: 'static/media/[name].[hash:8][ext]',\n    },\n  }\n\n  if (!webpack5Config.output) {\n    webpack5Config.output = {}\n  }\n  if (isClient) {\n    webpack5Config.output.trustedTypes = 'nextjs#bundler'\n  }\n\n  if (isClient || isEdgeServer) {\n    webpack5Config.output.enabledLibraryTypes = ['assign']\n  }\n\n  // This enables managedPaths for all node_modules\n  // and also for the unplugged folder when using yarn pnp\n  // It also add the yarn cache to the immutable paths\n  webpack5Config.snapshot = {}\n  if (process.versions.pnp === '3') {\n    webpack5Config.snapshot.managedPaths = [\n      /^(.+?(?:[\\\\/]\\.yarn[\\\\/]unplugged[\\\\/][^\\\\/]+)?[\\\\/]node_modules[\\\\/])/,\n    ]\n  } else {\n    webpack5Config.snapshot.managedPaths = [/^(.+?[\\\\/]node_modules[\\\\/])/]\n  }\n  if (process.versions.pnp === '3') {\n    webpack5Config.snapshot.immutablePaths = [\n      /^(.+?[\\\\/]cache[\\\\/][^\\\\/]+\\.zip[\\\\/]node_modules[\\\\/])/,\n    ]\n  }\n\n  if (dev) {\n    if (!webpack5Config.optimization) {\n      webpack5Config.optimization = {}\n    }\n\n    // For Server Components, it's necessary to have provided exports collected\n    // to generate the correct flight manifest.\n    if (!hasAppDir) {\n      webpack5Config.optimization.providedExports = false\n    }\n    webpack5Config.optimization.usedExports = false\n  }\n\n  const configVars = JSON.stringify({\n    optimizePackageImports: config?.experimental?.optimizePackageImports,\n    crossOrigin: config.crossOrigin,\n    pageExtensions: pageExtensions,\n    trailingSlash: config.trailingSlash,\n    buildActivityPosition:\n      config.devIndicators === false\n        ? undefined\n        : config.devIndicators.position,\n    productionBrowserSourceMaps: !!config.productionBrowserSourceMaps,\n    reactStrictMode: config.reactStrictMode,\n    optimizeCss: config.experimental.optimizeCss,\n    nextScriptWorkers: config.experimental.nextScriptWorkers,\n    scrollRestoration: config.experimental.scrollRestoration,\n    typedRoutes: config.experimental.typedRoutes,\n    basePath: config.basePath,\n    excludeDefaultMomentLocales: config.excludeDefaultMomentLocales,\n    assetPrefix: config.assetPrefix,\n    disableOptimizedLoading,\n    isEdgeRuntime: isEdgeServer,\n    reactProductionProfiling,\n    webpack: !!config.webpack,\n    hasRewrites,\n    swcLoader: useSWCLoader,\n    removeConsole: config.compiler?.removeConsole,\n    reactRemoveProperties: config.compiler?.reactRemoveProperties,\n    styledComponents: config.compiler?.styledComponents,\n    relay: config.compiler?.relay,\n    emotion: config.compiler?.emotion,\n    modularizeImports: config.modularizeImports,\n    imageLoaderFile: config.images.loaderFile,\n    clientTraceMetadata: config.experimental.clientTraceMetadata,\n    serverSourceMaps: config.experimental.serverSourceMaps,\n    serverReferenceHashSalt: encryptionKey,\n  })\n\n  const cache: any = {\n    type: 'filesystem',\n    // Disable memory cache in development in favor of our own MemoryWithGcCachePlugin.\n    maxMemoryGenerations: dev ? 0 : Infinity, // Infinity is default value for production in webpack currently.\n    // Includes:\n    //  - Next.js location on disk (some loaders use absolute paths and some resolve options depend on absolute paths)\n    //  - Next.js version\n    //  - next.config.js keys that affect compilation\n    version: `${__dirname}|${process.env.__NEXT_VERSION}|${configVars}`,\n    cacheDirectory: path.join(distDir, 'cache', 'webpack'),\n    // For production builds, it's more efficient to compress all cache files together instead of compression each one individually.\n    // So we disable compression here and allow the build runner to take care of compressing the cache as a whole.\n    // For local development, we still want to compress the cache files individually to avoid I/O bottlenecks\n    // as we are seeing 1~10 seconds of fs I/O time from user reports.\n    compression: dev ? 'gzip' : false,\n  }\n\n  // Adds `next.config.js` as a buildDependency when custom webpack config is provided\n  if (config.webpack && config.configFile) {\n    cache.buildDependencies = {\n      config: [config.configFile],\n      // We don't want to use the webpack default buildDependencies as we already include the next.js version\n      defaultWebpack: [],\n    }\n  } else {\n    cache.buildDependencies = {\n      // We don't want to use the webpack default buildDependencies as we already include the next.js version\n      defaultWebpack: [],\n    }\n  }\n  webpack5Config.plugins?.push((compiler) => {\n    compiler.hooks.done.tap('next-build-dependencies', (stats) => {\n      const buildDependencies = stats.compilation.buildDependencies\n      const nextPackage = path.dirname(require.resolve('next/package.json'))\n      // Remove all next.js build dependencies, they are already covered by the cacheVersion\n      // and next.js also imports the output files which leads to broken caching.\n      for (const dep of buildDependencies) {\n        if (dep.startsWith(nextPackage)) {\n          buildDependencies.delete(dep)\n        }\n      }\n    })\n  })\n\n  webpack5Config.cache = cache\n\n  if (process.env.NEXT_WEBPACK_LOGGING) {\n    const infra = process.env.NEXT_WEBPACK_LOGGING.includes('infrastructure')\n    const profileClient =\n      process.env.NEXT_WEBPACK_LOGGING.includes('profile-client')\n    const profileServer =\n      process.env.NEXT_WEBPACK_LOGGING.includes('profile-server')\n    const summaryClient =\n      process.env.NEXT_WEBPACK_LOGGING.includes('summary-client')\n    const summaryServer =\n      process.env.NEXT_WEBPACK_LOGGING.includes('summary-server')\n\n    const profile =\n      (profileClient && isClient) || (profileServer && isNodeOrEdgeCompilation)\n    const summary =\n      (summaryClient && isClient) || (summaryServer && isNodeOrEdgeCompilation)\n\n    const logDefault = !infra && !profile && !summary\n\n    if (logDefault || infra) {\n      webpack5Config.infrastructureLogging = {\n        level: 'verbose',\n        debug: /FileSystemInfo/,\n      }\n    }\n\n    if (logDefault || profile) {\n      webpack5Config.plugins!.push((compiler: webpack.Compiler) => {\n        compiler.hooks.done.tap('next-webpack-logging', (stats) => {\n          console.log(\n            stats.toString({\n              colors: true,\n              logging: logDefault ? 'log' : 'verbose',\n            })\n          )\n        })\n      })\n    } else if (summary) {\n      webpack5Config.plugins!.push((compiler: webpack.Compiler) => {\n        compiler.hooks.done.tap('next-webpack-logging', (stats) => {\n          console.log(\n            stats.toString({\n              preset: 'summary',\n              colors: true,\n              timings: true,\n            })\n          )\n        })\n      })\n    }\n\n    if (profile) {\n      const ProgressPlugin =\n        webpack.ProgressPlugin as unknown as typeof webpack.ProgressPlugin\n      webpack5Config.plugins!.push(\n        new ProgressPlugin({\n          profile: true,\n        })\n      )\n      webpack5Config.profile = true\n    }\n  }\n\n  webpackConfig = await buildConfiguration(webpackConfig, {\n    supportedBrowsers,\n    rootDirectory: dir,\n    customAppFile: pagesDir\n      ? new RegExp(escapeStringRegexp(path.join(pagesDir, `_app`)))\n      : undefined,\n    hasAppDir,\n    isDevelopment: dev,\n    isServer: isNodeOrEdgeCompilation,\n    isEdgeRuntime: isEdgeServer,\n    targetWeb: isClient || isEdgeServer,\n    assetPrefix: config.assetPrefix || '',\n    sassOptions: config.sassOptions,\n    productionBrowserSourceMaps: config.productionBrowserSourceMaps,\n    future: config.future,\n    experimental: config.experimental,\n    disableStaticImages: config.images.disableStaticImages,\n    transpilePackages: config.transpilePackages,\n    serverSourceMaps: config.experimental.serverSourceMaps,\n  })\n\n  // @ts-ignore Cache exists\n  webpackConfig.cache.name = `${webpackConfig.name}-${webpackConfig.mode}${\n    isDevFallback ? '-fallback' : ''\n  }`\n\n  if (dev) {\n    if (webpackConfig.module) {\n      webpackConfig.module.unsafeCache = (module: any) =>\n        !UNSAFE_CACHE_REGEX.test(module.resource)\n    } else {\n      webpackConfig.module = {\n        unsafeCache: (module: any) => !UNSAFE_CACHE_REGEX.test(module.resource),\n      }\n    }\n  }\n\n  let originalDevtool = webpackConfig.devtool\n  if (typeof config.webpack === 'function') {\n    const pluginCountBefore = webpackConfig.plugins?.length\n\n    webpackConfig = config.webpack(webpackConfig, {\n      dir,\n      dev,\n      isServer: isNodeOrEdgeCompilation,\n      buildId,\n      config,\n      defaultLoaders,\n      totalPages: Object.keys(entrypoints).length,\n      webpack,\n      ...(isNodeOrEdgeCompilation\n        ? {\n            nextRuntime: isEdgeServer ? 'edge' : 'nodejs',\n          }\n        : {}),\n    })\n\n    if (telemetryPlugin && pluginCountBefore) {\n      const pluginCountAfter = webpackConfig.plugins?.length\n      if (pluginCountAfter) {\n        const pluginsChanged = pluginCountAfter !== pluginCountBefore\n        telemetryPlugin.addUsage('webpackPlugins', pluginsChanged ? 1 : 0)\n      }\n    }\n\n    if (!webpackConfig) {\n      throw new Error(\n        `Webpack config is undefined. You may have forgot to return properly from within the \"webpack\" method of your ${config.configFileName}.\\n` +\n          'See more info here https://nextjs.org/docs/messages/undefined-webpack-config'\n      )\n    }\n\n    if (dev && originalDevtool !== webpackConfig.devtool) {\n      webpackConfig.devtool = originalDevtool\n      devtoolRevertWarning(originalDevtool)\n    }\n\n    // eslint-disable-next-line @typescript-eslint/no-shadow\n    const webpack5Config = webpackConfig as webpack.Configuration\n\n    // disable lazy compilation of entries as next.js has it's own method here\n    if (webpack5Config.experiments?.lazyCompilation === true) {\n      webpack5Config.experiments.lazyCompilation = {\n        entries: false,\n      }\n    } else if (\n      typeof webpack5Config.experiments?.lazyCompilation === 'object' &&\n      webpack5Config.experiments.lazyCompilation.entries !== false\n    ) {\n      webpack5Config.experiments.lazyCompilation.entries = false\n    }\n\n    if (typeof (webpackConfig as any).then === 'function') {\n      console.warn(\n        '> Promise returned in next config. https://nextjs.org/docs/messages/promise-in-next-config'\n      )\n    }\n  }\n  const rules = webpackConfig.module?.rules || []\n\n  const customSvgRule = rules.find(\n    (rule): rule is webpack.RuleSetRule =>\n      (rule &&\n        typeof rule === 'object' &&\n        rule.loader !== 'next-image-loader' &&\n        'test' in rule &&\n        rule.test instanceof RegExp &&\n        rule.test.test('.svg')) ||\n      false\n  )\n\n  if (customSvgRule && hasAppDir) {\n    // Create React aliases for SVG components that were transformed using a\n    // custom webpack config with e.g. the `@svgr/webpack` loader, or the\n    // `babel-plugin-inline-react-svg` plugin.\n    rules.push({\n      test: customSvgRule.test,\n      oneOf: [\n        WEBPACK_LAYERS.reactServerComponents,\n        WEBPACK_LAYERS.serverSideRendering,\n        WEBPACK_LAYERS.appPagesBrowser,\n      ].map((layer) => ({\n        issuerLayer: layer,\n        resolve: {\n          alias: createRSCAliases(bundledReactChannel, {\n            reactProductionProfiling,\n            layer,\n            isEdgeServer,\n          }),\n        },\n      })),\n    })\n  }\n\n  if (!config.images.disableStaticImages) {\n    const nextImageRule = rules.find(\n      (rule) =>\n        rule && typeof rule === 'object' && rule.loader === 'next-image-loader'\n    )\n    if (customSvgRule && nextImageRule && typeof nextImageRule === 'object') {\n      // Exclude svg if the user already defined it in custom\n      // webpack config such as the `@svgr/webpack` loader, or\n      // the `babel-plugin-inline-react-svg` plugin.\n      nextImageRule.test = /\\.(png|jpg|jpeg|gif|webp|avif|ico|bmp)$/i\n    }\n  }\n\n  if (\n    config.experimental.craCompat &&\n    webpackConfig.module?.rules &&\n    webpackConfig.plugins\n  ) {\n    // CRA allows importing non-webpack handled files with file-loader\n    // these need to be the last rule to prevent catching other items\n    // https://github.com/facebook/create-react-app/blob/fddce8a9e21bf68f37054586deb0c8636a45f50b/packages/react-scripts/config/webpack.config.js#L594\n    const fileLoaderExclude = [/\\.(js|mjs|jsx|ts|tsx|json)$/]\n    const fileLoader = {\n      exclude: fileLoaderExclude,\n      issuer: fileLoaderExclude,\n      type: 'asset/resource',\n    }\n\n    const topRules = []\n    const innerRules = []\n\n    for (const rule of webpackConfig.module.rules) {\n      if (!rule || typeof rule !== 'object') continue\n      if (rule.resolve) {\n        topRules.push(rule)\n      } else {\n        if (\n          rule.oneOf &&\n          !(rule.test || rule.exclude || rule.resource || rule.issuer)\n        ) {\n          rule.oneOf.forEach((r) => innerRules.push(r))\n        } else {\n          innerRules.push(rule)\n        }\n      }\n    }\n\n    webpackConfig.module.rules = [\n      ...(topRules as any),\n      {\n        oneOf: [...innerRules, fileLoader],\n      },\n    ]\n  }\n\n  // Backwards compat with webpack-dev-middleware options object\n  if (typeof config.webpackDevMiddleware === 'function') {\n    const options = config.webpackDevMiddleware({\n      watchOptions: webpackConfig.watchOptions,\n    })\n    if (options.watchOptions) {\n      webpackConfig.watchOptions = options.watchOptions\n    }\n  }\n\n  function canMatchCss(rule: webpack.RuleSetCondition | undefined): boolean {\n    if (!rule) {\n      return false\n    }\n\n    const fileNames = [\n      '/tmp/NEXTJS_CSS_DETECTION_FILE.css',\n      '/tmp/NEXTJS_CSS_DETECTION_FILE.scss',\n      '/tmp/NEXTJS_CSS_DETECTION_FILE.sass',\n      '/tmp/NEXTJS_CSS_DETECTION_FILE.less',\n      '/tmp/NEXTJS_CSS_DETECTION_FILE.styl',\n    ]\n\n    if (rule instanceof RegExp && fileNames.some((input) => rule.test(input))) {\n      return true\n    }\n\n    if (typeof rule === 'function') {\n      if (\n        fileNames.some((input) => {\n          try {\n            if (rule(input)) {\n              return true\n            }\n          } catch {}\n          return false\n        })\n      ) {\n        return true\n      }\n    }\n\n    if (Array.isArray(rule) && rule.some(canMatchCss)) {\n      return true\n    }\n\n    return false\n  }\n\n  const hasUserCssConfig =\n    webpackConfig.module?.rules?.some(\n      (rule: any) => canMatchCss(rule.test) || canMatchCss(rule.include)\n    ) ?? false\n\n  if (hasUserCssConfig) {\n    // only show warning for one build\n    if (isNodeOrEdgeCompilation) {\n      console.warn(\n        yellow(bold('Warning: ')) +\n          bold(\n            'Built-in CSS support is being disabled due to custom CSS configuration being detected.\\n'\n          ) +\n          'See here for more info: https://nextjs.org/docs/messages/built-in-css-disabled\\n'\n      )\n    }\n\n    if (webpackConfig.module?.rules?.length) {\n      // Remove default CSS Loaders\n      webpackConfig.module.rules.forEach((r) => {\n        if (!r || typeof r !== 'object') return\n        if (Array.isArray(r.oneOf)) {\n          r.oneOf = r.oneOf.filter(\n            (o) => (o as any)[Symbol.for('__next_css_remove')] !== true\n          )\n        }\n      })\n    }\n    if (webpackConfig.plugins?.length) {\n      // Disable CSS Extraction Plugin\n      webpackConfig.plugins = webpackConfig.plugins.filter(\n        (p) => (p as any).__next_css_remove !== true\n      )\n    }\n    if (webpackConfig.optimization?.minimizer?.length) {\n      // Disable CSS Minifier\n      webpackConfig.optimization.minimizer =\n        webpackConfig.optimization.minimizer.filter(\n          (e) => (e as any).__next_css_remove !== true\n        )\n    }\n  }\n\n  // Inject missing React Refresh loaders so that development mode is fast:\n  if (dev && isClient) {\n    attachReactRefresh(webpackConfig, defaultLoaders.babel)\n  }\n\n  // Backwards compat for `main.js` entry key\n  // and setup of dependencies between entries\n  // we can't do that in the initial entry for\n  // backward-compat reasons\n  const originalEntry: any = webpackConfig.entry\n  if (typeof originalEntry !== 'undefined') {\n    const updatedEntry = async () => {\n      const entry: webpack.EntryObject =\n        typeof originalEntry === 'function'\n          ? await originalEntry()\n          : originalEntry\n      // Server compilation doesn't have main.js\n      if (\n        clientEntries &&\n        Array.isArray(entry['main.js']) &&\n        entry['main.js'].length > 0\n      ) {\n        const originalFile = clientEntries[\n          CLIENT_STATIC_FILES_RUNTIME_MAIN\n        ] as string\n        entry[CLIENT_STATIC_FILES_RUNTIME_MAIN] = [\n          ...entry['main.js'],\n          originalFile,\n        ]\n      }\n      delete entry['main.js']\n\n      for (const name of Object.keys(entry)) {\n        entry[name] = finalizeEntrypoint({\n          value: entry[name],\n          compilerType,\n          name,\n          hasAppDir,\n        })\n      }\n\n      return entry\n    }\n    // @ts-ignore webpack 5 typings needed\n    webpackConfig.entry = updatedEntry\n  }\n\n  if (!dev && typeof webpackConfig.entry === 'function') {\n    // entry is always a function\n    webpackConfig.entry = await webpackConfig.entry()\n  }\n\n  return webpackConfig\n}\n"], "names": ["React", "ReactRefreshWebpackPlugin", "yellow", "bold", "crypto", "webpack", "path", "escapeStringRegexp", "WEBPACK_LAYERS", "WEBPACK_RESOURCE_QUERIES", "isWebpackBundledLayer", "isWebpackClientOnlyLayer", "isWebpackDefaultLayer", "isWebpackServerOnlyLayer", "CLIENT_STATIC_FILES_RUNTIME_AMP", "CLIENT_STATIC_FILES_RUNTIME_MAIN", "CLIENT_STATIC_FILES_RUNTIME_MAIN_APP", "CLIENT_STATIC_FILES_RUNTIME_POLYFILLS_SYMBOL", "CLIENT_STATIC_FILES_RUNTIME_REACT_REFRESH", "CLIENT_STATIC_FILES_RUNTIME_WEBPACK", "MIDDLEWARE_REACT_LOADABLE_MANIFEST", "REACT_LOADABLE_MANIFEST", "SERVER_DIRECTORY", "COMPILER_NAMES", "execOnce", "finalizeEntrypoint", "Log", "buildConfiguration", "MiddlewarePlugin", "getEdgePolyfilledModules", "handleWebpackExternalForEdgeRuntime", "BuildManifestPlugin", "JsConfigPathsPlugin", "DropClientPage", "PagesManifestPlugin", "Profiling<PERSON><PERSON><PERSON>", "ReactLoadablePlugin", "WellKnownErrorsPlugin", "regexLikeCss", "CopyFilePlugin", "ClientReferenceManifestPlugin", "FlightClientEntryPlugin", "NextFlightClientEntryPlugin", "RspackFlightClientEntryPlugin", "NextTypesPlugin", "loadJsConfig", "loadBindings", "AppBuildManifestPlugin", "SubresourceIntegrityPlugin", "NextFontManifestPlugin", "getSupportedBrowsers", "MemoryWithGcCachePlugin", "getBabelConfigFile", "needsExperimentalReact", "getDefineEnvPlugin", "isResourceInPackages", "makeExternalHandler", "getMainField", "edgeConditionNames", "OptionalPeerDependencyResolverPlugin", "createWebpackAliases", "createServerOnlyClientOnlyAliases", "createRSCAliases", "createNextApiEsmAliases", "createAppRouterApiAliases", "hasCustomExportOutput", "CssChunkingPlugin", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "getReactCompilerLoader", "NEXT_PROJECT_ROOT", "NEXT_PROJECT_ROOT_DIST_CLIENT", "getRspackCore", "getRspackReactRefresh", "RspackProfilingPlugin", "getWebpackBundler", "EXTERNAL_PACKAGES", "require", "DEFAULT_TRANSPILED_PACKAGES", "parseInt", "version", "Error", "babelIncludeRegexes", "browserNonTranspileModules", "precompileRegex", "asyncStoragesRegex", "nodePathList", "process", "env", "NODE_PATH", "split", "platform", "filter", "p", "baseWatchOptions", "Object", "freeze", "aggregateTimeout", "ignored", "isModuleCSS", "module", "type", "devtoolRevertWarning", "devtool", "console", "warn", "loggedSwcDisabled", "loggedIgnoredCompilerOptions", "reactRefreshLoaderName", "getReactRefreshLoader", "NEXT_RSPACK", "loader", "resolve", "attachReactRefresh", "webpackConfig", "target<PERSON><PERSON><PERSON>", "reactRefreshLoader", "rules", "for<PERSON>ach", "rule", "curr", "use", "Array", "isArray", "some", "r", "idx", "findIndex", "splice", "NODE_RESOLVE_OPTIONS", "dependencyType", "modules", "fallback", "exportsFields", "importsFields", "conditionNames", "descriptionFiles", "extensions", "enforceExtensions", "symlinks", "mainFields", "mainFiles", "roots", "fullySpecified", "preferRelative", "preferAbsolute", "restrictions", "NODE_BASE_RESOLVE_OPTIONS", "alias", "NODE_ESM_RESOLVE_OPTIONS", "NODE_BASE_ESM_RESOLVE_OPTIONS", "nextImageLoaderRegex", "loadProjectInfo", "dir", "config", "dev", "jsConfig", "jsConfigPath", "resolvedBaseUrl", "supportedBrowsers", "hasExternalOtelApiPackage", "UNSAFE_CACHE_REGEX", "getBaseWebpackConfig", "buildId", "<PERSON><PERSON><PERSON>", "compilerType", "entrypoints", "isDev<PERSON><PERSON><PERSON>", "pagesDir", "reactProductionProfiling", "rewrites", "originalRewrites", "originalRedirects", "runWebpackSpan", "appDir", "middlewareMatchers", "noMangling", "clientRouterFilters", "fetchCacheKeyPrefix", "edgePreviewProps", "isCompileMode", "webpack5Config", "bundler", "isClient", "client", "isEdgeServer", "edgeServer", "isNodeServer", "server", "isRspack", "Boolean", "BUILTIN_FLIGHT_CLIENT_ENTRY_PLUGIN", "isNodeOrEdgeCompilation", "hasRewrites", "beforeFiles", "length", "afterFiles", "hasAppDir", "disableOptimizedLoading", "enableTypedRoutes", "experimental", "typedRoutes", "bundledReactChannel", "babelConfigFile", "distDir", "join", "useSWCLoader", "forceSwcTransforms", "SWCBinaryTarget", "undefined", "binaryTarget", "getBinaryMetadata", "target", "info", "relative", "useWasmBinary", "finalTranspilePackages", "transpilePackages", "concat", "pkg", "optimizePackageImports", "includes", "push", "compiler", "shouldIncludeExternalDirs", "externalDir", "codeCondition", "test", "or", "include", "exclude", "excludePath", "shouldBeBundled", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "reactCompiler", "reactCompilerLoader", "swcTraceProfilingInitialized", "getSwcLoader", "extraOptions", "swcTraceProfiling", "initCustomTraceSubscriber", "Date", "now", "useBuiltinSwcLoader", "BUILTIN_SWC_LOADER", "options", "isServer", "rootDir", "hasReactRefresh", "swcCacheDir", "serverReferenceHashSalt", "pnp", "versions", "optimizeServerReact", "modularizeImports", "decorators", "compilerOptions", "experimentalDecorators", "emitDecoratorMetadata", "regeneratorRuntimePath", "nextConfig", "swcServerLayerLoader", "serverComponents", "bundleLayer", "reactServerComponents", "esm", "swcSS<PERSON>ayer<PERSON><PERSON>der", "serverSideRendering", "swcBrowser<PERSON><PERSON><PERSON><PERSON><PERSON>der", "appPagesBrowser", "swcDefaultLoader", "defaultLoaders", "babel", "appServerLayerLoaders", "instrumentLayerLoaders", "middlewareLayerLoaders", "middleware", "reactRefreshLoaders", "createClientLayerLoader", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "reactRefresh", "appBrowserLayerLoaders", "appSSRLayerLoaders", "apiRoutesLayerLoaders", "apiNode", "pageExtensions", "outputPath", "reactServerCondition", "reactRefreshEntry", "entry", "clientEntries", "replace", "resolveConfig", "extensionAlias", "plugins", "tsConfig", "configFile", "<PERSON><PERSON><PERSON>eworkP<PERSON><PERSON>", "topLevelFrameworkPaths", "visitedFrameworkPackages", "Set", "addPackagePath", "packageName", "relativeToPath", "paths", "has", "add", "packageJsonPath", "directory", "dependencies", "name", "keys", "_", "crossOrigin", "serverExternalPackages", "externalPackageConflicts", "optOutBundlingPackages", "optOutBundlingPackageRegex", "RegExp", "map", "transpilePackagesRegex", "handleExternals", "transpiledPackages", "pageExtensionsRegex", "aliasCodeConditionTest", "builtinModules", "shouldEnableSlowModuleDetection", "slowModuleDetection", "getParallelism", "override", "Number", "NEXT_WEBPACK_PARALLELISM", "telemetryPlugin", "TelemetryPlugin", "Map", "relay", "styledComponents", "reactRemoveProperties", "removeConsole", "jsxImportSource", "emotion", "skipMiddlewareUrlNormalize", "skipTrailingSlashRedirect", "esmExternals", "parallelism", "externalsPresets", "node", "externals", "context", "request", "contextInfo", "getResolve", "issuer<PERSON><PERSON>er", "resolveFunction", "resolveContext", "requestToResolve", "Promise", "reject", "err", "result", "resolveData", "isEsm", "descriptionFileData", "optimization", "emitOnErrors", "checkWasmTypes", "nodeEnv", "splitChunks", "extractRootNodeModule", "modulePath", "regex", "match", "cacheGroups", "vendor", "chunks", "reuseExistingChunk", "minSize", "minChunks", "maxAsyncRequests", "maxInitialRequests", "moduleId", "nameForCondition", "rootModule", "hash", "createHash", "update", "digest", "default", "defaultVendors", "filename", "frameworkCacheGroup", "layer", "resource", "pkgPath", "startsWith", "priority", "enforce", "libCacheGroup", "size", "updateHash", "libIdent", "substring", "chunk", "framework", "lib", "runtimeChunk", "minimize", "serverMinification", "minimizer", "SwcJsMinimizerRspackPlugin", "minimizerOptions", "compress", "inline", "global_defs", "mangle", "reserved", "disableCharFreq", "LightningCssMinimizerRspackPlugin", "targets", "MinifyPlugin", "apply", "CssMinimizerPlugin", "postcssOptions", "annotation", "watchOptions", "poll", "pollIntervalMs", "output", "publicPath", "assetPrefix", "endsWith", "slice", "library", "libraryTarget", "hotUpdateChunkFilename", "hotUpdateMainFilename", "chunkFilename", "strictModuleExceptionHandling", "crossOriginLoading", "devtoolModuleFilenameTemplate", "webassemblyModuleFilename", "hashFunction", "hashDigestLength", "performance", "<PERSON><PERSON><PERSON><PERSON>", "reduce", "__dirname", "GROUP", "serverOnly", "neutralTarget", "not", "message", "shared", "resourceQuery", "metadataRoute", "and", "metadata", "metadataImageMeta", "edgeSSREntry", "oneOf", "parser", "url", "apiEdge", "instrument", "images", "disableStaticImages", "issuer", "dependency", "isDev", "basePath", "fallbackNodePolyfills", "assert", "buffer", "constants", "domain", "http", "https", "os", "punycode", "querystring", "stream", "string_decoder", "sys", "timers", "tty", "util", "vm", "zlib", "events", "setImmediate", "sideEffects", "names", "ident", "next", "NormalModuleReplacementPlugin", "moduleName", "basename", "runtime", "<PERSON><PERSON><PERSON><PERSON>", "maxGenerations", "injectLoader", "injectEntry", "overlay", "ProvidePlugin", "<PERSON><PERSON><PERSON>", "isTurbopack", "omitNonDeterministic", "runtimeAsset", "TraceEntryPointsPlugin", "outputFileTracingRoot", "appDirEnabled", "traceIgnores", "excludeDefaultMomentLocales", "IgnorePlugin", "resourceRegExp", "contextRegExp", "NextJsRequireCacheHotReloader", "devP<PERSON><PERSON>", "HotModuleReplacementPlugin", "isEdgeRuntime", "sriEnabled", "sri", "algorithm", "edgeEnvironments", "__NEXT_BUILD_ID", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY", "filePath", "cache<PERSON>ey", "__NEXT_VERSION", "minimized", "experimentalInlineCss", "inlineCss", "cacheLifeConfig", "cacheLife", "cssChunking", "isImplicit", "baseUrl", "unshift", "edgeAsset", "experiments", "layers", "cacheUnaffected", "buildHttp", "urlImports", "<PERSON><PERSON><PERSON>", "cacheLocation", "lockfileLocation", "javascript", "generator", "asset", "trustedTypes", "enabledLibraryTypes", "snapshot", "managedPaths", "immutablePaths", "providedExports", "usedExports", "configVars", "JSON", "stringify", "trailingSlash", "buildActivityPosition", "devIndicators", "position", "productionBrowserSourceMaps", "reactStrictMode", "optimizeCss", "nextScriptWorkers", "scrollRestoration", "sw<PERSON><PERSON><PERSON><PERSON>", "imageLoaderFile", "loaderFile", "clientTraceMetadata", "serverSourceMaps", "cache", "maxMemoryGenerations", "Infinity", "cacheDirectory", "compression", "buildDependencies", "defaultWebpack", "hooks", "done", "tap", "stats", "compilation", "nextPackage", "dirname", "dep", "delete", "NEXT_WEBPACK_LOGGING", "infra", "profileClient", "profileServer", "summaryClient", "summaryServer", "profile", "summary", "logDefault", "infrastructureLogging", "level", "debug", "log", "toString", "colors", "logging", "preset", "timings", "ProgressPlugin", "rootDirectory", "customAppFile", "isDevelopment", "targetWeb", "sassOptions", "future", "mode", "unsafeCache", "originalDevtool", "pluginCountBefore", "totalPages", "nextRuntime", "pluginCountAfter", "pluginsChanged", "addUsage", "configFileName", "lazyCompilation", "entries", "then", "customSvgRule", "find", "nextImageRule", "craCompat", "fileLoaderExclude", "fileLoader", "topRules", "innerRules", "webpackDevMiddleware", "canMatchCss", "fileNames", "input", "hasUserCssConfig", "o", "Symbol", "for", "__next_css_remove", "e", "originalEntry", "updatedEntry", "originalFile", "value"], "mappings": "AAAA,OAAOA,WAAW,QAAO;AACzB,OAAOC,+BAA+B,8EAA6E;AACnH,SAASC,MAAM,EAAEC,IAAI,QAAQ,oBAAmB;AAChD,OAAOC,YAAY,SAAQ;AAC3B,SAASC,OAAO,QAAQ,qCAAoC;AAC5D,OAAOC,UAAU,OAAM;AAEvB,SAASC,kBAAkB,QAAQ,8BAA6B;AAChE,SAASC,cAAc,EAAEC,wBAAwB,QAAQ,mBAAkB;AAE3E,SACEC,qBAAqB,EACrBC,wBAAwB,EACxBC,qBAAqB,EACrBC,wBAAwB,QACnB,UAAS;AAEhB,SACEC,+BAA+B,EAC/BC,gCAAgC,EAChCC,oCAAoC,EACpCC,4CAA4C,EAC5CC,yCAAyC,EACzCC,mCAAmC,EACnCC,kCAAkC,EAClCC,uBAAuB,EACvBC,gBAAgB,EAChBC,cAAc,QACT,0BAAyB;AAEhC,SAASC,QAAQ,QAAQ,sBAAqB;AAE9C,SAASC,kBAAkB,QAAQ,YAAW;AAC9C,YAAYC,SAAS,eAAc;AACnC,SAASC,kBAAkB,QAAQ,mBAAkB;AACrD,OAAOC,oBACLC,wBAAwB,EACxBC,mCAAmC,QAC9B,sCAAqC;AAC5C,OAAOC,yBAAyB,0CAAyC;AACzE,SAASC,mBAAmB,QAAQ,0CAAyC;AAC7E,SAASC,cAAc,QAAQ,iDAAgD;AAC/E,OAAOC,yBAAyB,0CAAyC;AACzE,SAASC,eAAe,QAAQ,qCAAoC;AACpE,SAASC,mBAAmB,QAAQ,0CAAyC;AAC7E,SAASC,qBAAqB,QAAQ,4CAA2C;AACjF,SAASC,YAAY,QAAQ,8BAA6B;AAC1D,SAASC,cAAc,QAAQ,qCAAoC;AACnE,SAASC,6BAA6B,QAAQ,2CAA0C;AACxF,SAASC,2BAA2BC,2BAA2B,QAAQ,+CAA8C;AACrH,SAASC,6BAA6B,QAAQ,sDAAqD;AACnG,SAASC,eAAe,QAAQ,sCAAqC;AAOrE,OAAOC,kBAGA,kBAAiB;AACxB,SAASC,YAAY,QAAQ,QAAO;AACpC,SAASC,sBAAsB,QAAQ,8CAA6C;AACpF,SAASC,0BAA0B,QAAQ,iDAAgD;AAC3F,SAASC,sBAAsB,QAAQ,8CAA6C;AACpF,SAASC,oBAAoB,QAAQ,UAAS;AAC9C,SAASC,uBAAuB,QAAQ,gDAA+C;AACvF,SAASC,kBAAkB,QAAQ,0BAAyB;AAC5D,SAASC,sBAAsB,QAAQ,kCAAiC;AACxE,SAASC,kBAAkB,QAAQ,sCAAqC;AAExE,SAASC,oBAAoB,EAAEC,mBAAmB,QAAQ,qBAAoB;AAC9E,SACEC,YAAY,EACZC,kBAAkB,QACb,iCAAgC;AACvC,SAASC,oCAAoC,QAAQ,4DAA2D;AAChH,SACEC,oBAAoB,EACpBC,iCAAiC,EACjCC,gBAAgB,EAChBC,uBAAuB,EACvBC,yBAAyB,QACpB,4BAA2B;AAClC,SAASC,qBAAqB,QAAQ,kBAAiB;AACvD,SAASC,iBAAiB,QAAQ,wCAAuC;AACzE,SACEC,cAAc,EACdC,sBAAsB,QACjB,4BAA2B;AAClC,SACEC,iBAAiB,EACjBC,6BAA6B,QACxB,mBAAkB;AACzB,SAASC,aAAa,EAAEC,qBAAqB,QAAQ,2BAA0B;AAC/E,SAASC,qBAAqB,QAAQ,4CAA2C;AACjF,OAAOC,uBAAuB,oCAAmC;AAOjE,MAAMC,oBACJC,QAAQ;AAEV,MAAMC,8BACJD,QAAQ;AAEV,IAAIE,SAAS9E,MAAM+E,OAAO,IAAI,IAAI;IAChC,MAAM,qBAA8D,CAA9D,IAAIC,MAAM,sDAAV,qBAAA;eAAA;oBAAA;sBAAA;IAA6D;AACrE;AAEA,OAAO,MAAMC,sBAAgC;IAC3C;IACA;IACA;IACA;CACD,CAAA;AAED,MAAMC,6BAA6B;IACjC,+FAA+F;IAC/F,2HAA2H;IAC3H,2DAA2D;IAC3D;IACA,oGAAoG;IACpG,8GAA8G;IAC9G;CACD;AACD,MAAMC,kBAAkB;AAExB,MAAMC,qBACJ;AAEF,wBAAwB;AACxB,MAAMC,eAAe,AAACC,CAAAA,QAAQC,GAAG,CAACC,SAAS,IAAI,EAAC,EAC7CC,KAAK,CAACH,QAAQI,QAAQ,KAAK,UAAU,MAAM,KAC3CC,MAAM,CAAC,CAACC,IAAM,CAAC,CAACA;AAEnB,MAAMC,mBAA0DC,OAAOC,MAAM,CAAC;IAC5EC,kBAAkB;IAClBC,SACE,yDAAyD;IACzD;AACJ;AAEA,SAASC,YAAYC,MAAwB;IAC3C,OACE,0BAA0B;IAC1BA,OAAOC,IAAI,KAAK,CAAC,gBAAgB,CAAC,IAClC,0CAA0C;IAC1CD,OAAOC,IAAI,KAAK,CAAC,kBAAkB,CAAC,IACpC,0CAA0C;IAC1CD,OAAOC,IAAI,KAAK,CAAC,sBAAsB,CAAC;AAE5C;AAEA,MAAMC,uBAAuB7E,SAC3B,CAAC8E;IACCC,QAAQC,IAAI,CACVtG,OAAOC,KAAK,gBACVA,KAAK,CAAC,8BAA8B,EAAEmG,QAAQ,IAAI,CAAC,IACnD,kGACA;AAEN;AAGF,IAAIG,oBAAoB;AACxB,IAAIC,+BAA+B;AACnC,MAAMC,yBACJ;AAEF,SAASC;IACP,OAAOtB,QAAQC,GAAG,CAACsB,WAAW,GAC1BrC,wBAAwBsC,MAAM,GAC9BlC,QAAQmC,OAAO,CAACJ;AACtB;AAEA,OAAO,SAASK,mBACdC,aAAoC,EACpCC,YAAoC;QAGpCD,6BAAAA;IADA,MAAME,qBAAqBP;KAC3BK,wBAAAA,cAAcd,MAAM,sBAApBc,8BAAAA,sBAAsBG,KAAK,qBAA3BH,4BAA6BI,OAAO,CAAC,CAACC;QACpC,IAAIA,QAAQ,OAAOA,SAAS,YAAY,SAASA,MAAM;YACrD,MAAMC,OAAOD,KAAKE,GAAG;YACrB,wEAAwE;YACxE,IAAID,SAASL,cAAc;gBACzBI,KAAKE,GAAG,GAAG;oBAACL;oBAAoBI;iBAA+B;YACjE,OAAO,IACLE,MAAMC,OAAO,CAACH,SACdA,KAAKI,IAAI,CAAC,CAACC,IAAMA,MAAMV,iBACvB,kCAAkC;YAClC,CAACK,KAAKI,IAAI,CACR,CAACC,IAAMA,MAAMT,sBAAsBS,MAAMjB,yBAE3C;gBACA,MAAMkB,MAAMN,KAAKO,SAAS,CAAC,CAACF,IAAMA,MAAMV;gBACxC,iCAAiC;gBACjCI,KAAKE,GAAG,GAAG;uBAAID;iBAAK;gBAEpB,kEAAkE;gBAClED,KAAKE,GAAG,CAACO,MAAM,CAACF,KAAK,GAAGV;YAC1B;QACF;IACF;AACF;AAEA,OAAO,MAAMa,uBAAuB;IAClCC,gBAAgB;IAChBC,SAAS;QAAC;KAAe;IACzBC,UAAU;IACVC,eAAe;QAAC;KAAU;IAC1BC,eAAe;QAAC;KAAU;IAC1BC,gBAAgB;QAAC;QAAQ;KAAU;IACnCC,kBAAkB;QAAC;KAAe;IAClCC,YAAY;QAAC;QAAO;QAAS;KAAQ;IACrCC,mBAAmB;IACnBC,UAAU;IACVC,YAAY;QAAC;KAAO;IACpBC,WAAW;QAAC;KAAQ;IACpBC,OAAO,EAAE;IACTC,gBAAgB;IAChBC,gBAAgB;IAChBC,gBAAgB;IAChBC,cAAc,EAAE;AAClB,EAAC;AAED,OAAO,MAAMC,4BAA4B;IACvC,GAAGlB,oBAAoB;IACvBmB,OAAO;AACT,EAAC;AAED,OAAO,MAAMC,2BAA2B;IACtC,GAAGpB,oBAAoB;IACvBmB,OAAO;IACPlB,gBAAgB;IAChBK,gBAAgB;QAAC;QAAQ;KAAS;IAClCQ,gBAAgB;AAClB,EAAC;AAED,OAAO,MAAMO,gCAAgC;IAC3C,GAAGD,wBAAwB;IAC3BD,OAAO;AACT,EAAC;AAED,OAAO,MAAMG,uBACX,+CAA8C;AAEhD,OAAO,eAAeC,gBAAgB,EACpCC,GAAG,EACHC,MAAM,EACNC,GAAG,EAKJ;IAMC,MAAM,EAAEC,QAAQ,EAAEC,YAAY,EAAEC,eAAe,EAAE,GAAG,MAAMhH,aACxD2G,KACAC;IAEF,MAAMK,oBAAoB,MAAM5G,qBAAqBsG,KAAKE;IAC1D,OAAO;QACLC;QACAC;QACAC;QACAC;IACF;AACF;AAEA,OAAO,SAASC;IACd,IAAI;QACFnF,QAAQ;QACR,OAAO;IACT,EAAE,OAAM;QACN,OAAO;IACT;AACF;AAEA,MAAMoF,qBAAqB;AAE3B,eAAe,eAAeC,qBAC5BT,GAAW,EACX,EACEU,OAAO,EACPC,aAAa,EACbV,MAAM,EACNW,YAAY,EACZV,MAAM,KAAK,EACXW,WAAW,EACXC,gBAAgB,KAAK,EACrBC,QAAQ,EACRC,2BAA2B,KAAK,EAChCC,QAAQ,EACRC,gBAAgB,EAChBC,iBAAiB,EACjBC,cAAc,EACdC,MAAM,EACNC,kBAAkB,EAClBC,UAAU,EACVpB,QAAQ,EACRC,YAAY,EACZC,eAAe,EACfC,iBAAiB,EACjBkB,mBAAmB,EACnBC,mBAAmB,EACnBC,gBAAgB,EAChBC,aAAa,EAiCd;QAkHC1B,sBAOIA,uBA2biBA,kBACWA,mBAGtBA,mBAIAE,2BAEmBF,mBACDE,4BACLF,mBA4TfA,sBA+vBoBA,0BA+DtBA,2BAqCJE,4BAJJ,wDAAwD;IACxD,iCAAiC;IACjC1C,gCAAAA,wBAmG0BwC,uBAuBTA,mBACQA,mBACLA,mBACXA,mBACEA,mBAsCX2B,yBAgLcnE,uBAmDZA,wBA0FAA,6BAAAA;IA5rEF,MAAMoE,UAAU3G;IAChB,MAAM4G,WAAWlB,iBAAiB7I,eAAegK,MAAM;IACvD,MAAMC,eAAepB,iBAAiB7I,eAAekK,UAAU;IAC/D,MAAMC,eAAetB,iBAAiB7I,eAAeoK,MAAM;IAE3D,MAAMC,WAAWC,QAAQvG,QAAQC,GAAG,CAACsB,WAAW;IAEhD,MAAMpE,0BACJmJ,YAAYtG,QAAQC,GAAG,CAACuG,kCAAkC,GACtDnJ,gCACAD;IAEN,uFAAuF;IACvF,MAAMqJ,0BAA0BL,gBAAgBF;IAEhD,MAAMQ,cACJvB,SAASwB,WAAW,CAACC,MAAM,GAAG,KAC9BzB,SAAS0B,UAAU,CAACD,MAAM,GAAG,KAC7BzB,SAAStC,QAAQ,CAAC+D,MAAM,GAAG;IAE7B,MAAME,YAAY,CAAC,CAACvB;IACpB,MAAMwB,0BAA0B;IAChC,MAAMC,oBAAoB,CAAC,CAAC7C,OAAO8C,YAAY,CAACC,WAAW,IAAIJ;IAC/D,MAAMK,sBAAsBpJ,uBAAuBoG,UAC/C,kBACA;IAEJ,MAAMiD,kBAAkBtJ,mBAAmBoG;IAE3C,IAAI,CAACE,OAAOzF,sBAAsBwF,SAAS;QACzCA,OAAOkD,OAAO,GAAG;IACnB;IACA,MAAMA,UAAUrM,KAAKsM,IAAI,CAACpD,KAAKC,OAAOkD,OAAO;IAE7C,IAAIE,eAAe,CAACH,mBAAmBjD,OAAO8C,YAAY,CAACO,kBAAkB;IAC7E,IAAIC,kBAAkDC;IACtD,IAAIH,cAAc;YAEKjI,4BAAAA,6BAAAA;QADrB,0CAA0C;QAC1C,MAAMqI,gBAAerI,WAAAA,QAAQ,8BAARA,8BAAAA,SAAkBsI,iBAAiB,sBAAnCtI,6BAAAA,iCAAAA,8BAAAA,2BACjBuI,MAAM;QACVJ,kBAAkBE,eACd;YAAC,CAAC,WAAW,EAAEA,cAAc;YAAW;SAAK,GAC7CD;IACN;IAEA,IAAI,CAACvG,qBAAqB,CAACoG,gBAAgBH,iBAAiB;QAC1DhL,IAAI0L,IAAI,CACN,CAAC,6EAA6E,EAAE9M,KAAK+M,QAAQ,CAC3F7D,KACAkD,iBACA,+CAA+C,CAAC;QAEpDjG,oBAAoB;IACtB;IAEA,mEAAmE;IACnE,IAAI,CAACiG,mBAAmBpB,UAAU;QAChC,MAAMxI,aAAa2G,OAAO8C,YAAY,CAACe,aAAa;IACtD;IAEA,4DAA4D;IAC5D,2DAA2D;IAC3D,MAAMC,yBAAmC,AACvC9D,CAAAA,OAAO+D,iBAAiB,IAAI,EAAE,AAAD,EAC7BC,MAAM,CAAC5I;IAET,KAAK,MAAM6I,OAAOjE,OAAO8C,YAAY,CAACoB,sBAAsB,IAAI,EAAE,CAAE;QAClE,IAAI,CAACJ,uBAAuBK,QAAQ,CAACF,MAAM;YACzCH,uBAAuBM,IAAI,CAACH;QAC9B;IACF;IAEA,IAAI,CAAChH,gCAAgC,CAACmG,gBAAgBpD,OAAOqE,QAAQ,EAAE;QACrEpM,IAAI0L,IAAI,CACN;QAEF1G,+BAA+B;IACjC;IAEA,MAAMqH,4BACJtE,OAAO8C,YAAY,CAACyB,WAAW,IAAI,CAAC,CAACvE,OAAO+D,iBAAiB;IAC/D,MAAMS,gBAAgB;QACpBC,MAAM;YAAEC,IAAI;gBAAC;gBAA8B;aAAsB;QAAC;QAClE,GAAIJ,4BAEA,CAAC,IACD;YAAEK,SAAS;gBAAC5E;mBAAQvE;aAAoB;QAAC,CAAC;QAC9CoJ,SAAS,CAACC;YACR,IAAIrJ,oBAAoB0C,IAAI,CAAC,CAACC,IAAMA,EAAEsG,IAAI,CAACI,eAAe;gBACxD,OAAO;YACT;YAEA,MAAMC,kBAAkBhL,qBACtB+K,aACAf;YAEF,IAAIgB,iBAAiB,OAAO;YAE5B,OAAOD,YAAYV,QAAQ,CAAC;QAC9B;IACF;IAEA,MAAMY,cAAcrK,eAClB0I,cACAH,iBACAX,yBACAY,SACApC,UACAf,KACCqB,UAAUN,UACXb,KACA4B,WACA7B,uBAAAA,OAAO8C,YAAY,qBAAnB9C,qBAAqBgF,aAAa,EAClCR,cAAcI,OAAO;IAGvB,MAAMK,sBAAsBF,cACxBxB,YACA5I,wBACEqF,wBAAAA,OAAO8C,YAAY,qBAAnB9C,sBAAqBgF,aAAa,EAClCjF,KACAE,KACAqC,yBACAkC,cAAcI,OAAO;IAG3B,IAAIM,+BAA+B;IACnC,MAAMC,eAAe,CAACC;YAElBpF;QADF,IACEA,CAAAA,2BAAAA,uBAAAA,OAAQ8C,YAAY,qBAApB9C,qBAAsBqF,iBAAiB,KACvC,CAACH,8BACD;gBAMA/J,oCAAAA;YALA,sEAAsE;YACtE,+CAA+C;YAC/C,qFAAqF;YACrF,uDAAuD;YACvD+J,+BAA+B;aAC/B/J,WAAAA,QAAQ,8BAARA,qCAAAA,SAAkBmK,yBAAyB,qBAA3CnK,wCAAAA,UACEtE,KAAKsM,IAAI,CAACD,SAAS,CAAC,kBAAkB,EAAEqC,KAAKC,GAAG,GAAG,KAAK,CAAC;QAE7D;QAEA,MAAMC,sBAAsB5J,QAAQC,GAAG,CAAC4J,kBAAkB;QAC1D,IAAIvD,YAAYsD,qBAAqB;gBAwB7BvF,2BAGAA;YA1BN,OAAO;gBACL7C,QAAQ;gBACRsI,SAAS;oBACPC,UAAUtD;oBACVuD,SAAS9F;oBACTe;oBACAM;oBACA0E,iBAAiB7F,OAAO4B;oBACxBkC,mBAAmBD;oBACnBzD;oBACA0F,aAAalP,KAAKsM,IAAI,CACpBpD,KACAC,CAAAA,0BAAAA,OAAQkD,OAAO,KAAI,SACnB,SACA;oBAEF8C,yBAAyBtF;oBAEzB,0BAA0B;oBAC1BuF,KAAK7D,QAAQvG,QAAQqK,QAAQ,CAACD,GAAG;oBACjCE,qBAAqB/D,QAAQpC,OAAO8C,YAAY,CAACqD,mBAAmB;oBACpEC,mBAAmBpG,OAAOoG,iBAAiB;oBAC3CC,YAAYjE,QACVlC,6BAAAA,4BAAAA,SAAUoG,eAAe,qBAAzBpG,0BAA2BqG,sBAAsB;oBAEnDC,uBAAuBpE,QACrBlC,6BAAAA,6BAAAA,SAAUoG,eAAe,qBAAzBpG,2BAA2BsG,qBAAqB;oBAElDC,wBAAwBtL,QAAQmC,OAAO,CACrC;oBAGF,GAAG8H,YAAY;gBACjB;YACF;QACF;QAEA,OAAO;YACL/H,QAAQ;YACRsI,SAAS;gBACPC,UAAUtD;gBACVuD,SAAS9F;gBACTe;gBACAM;gBACA0E,iBAAiB7F,OAAO4B;gBACxB6E,YAAY1G;gBACZE;gBACA6D,mBAAmBD;gBACnBzD;gBACA0F,aAAalP,KAAKsM,IAAI,CAACpD,KAAKC,CAAAA,0BAAAA,OAAQkD,OAAO,KAAI,SAAS,SAAS;gBACjE8C,yBAAyBtF;gBACzB,GAAG0E,YAAY;YACjB;QACF;IACF;IAEA,6CAA6C;IAC7C,MAAMuB,uBAAuBxB,aAAa;QACxCyB,kBAAkB;QAClBC,aAAa9P,eAAe+P,qBAAqB;QACjDC,KAAK;IACP;IACA,MAAMC,oBAAoB7B,aAAa;QACrCyB,kBAAkB;QAClBC,aAAa9P,eAAekQ,mBAAmB;QAC/CF,KAAK;IACP;IACA,MAAMG,wBAAwB/B,aAAa;QACzCyB,kBAAkB;QAClBC,aAAa9P,eAAeoQ,eAAe;QAC3CJ,KAAK;IACP;IACA,oDAAoD;IACpD,MAAMK,mBAAmBjC,aAAa;QACpCyB,kBAAkB;QAClBG,KAAK;IACP;IAEA,MAAMM,iBAAiB;QACrBC,OAAOlE,eAAegE,mBAAmBrC;IAC3C;IAEA,MAAMwC,wBAAwB5E,YAC1B;QACE,uDAAuD;QACvD,iDAAiD;QACjD,gDAAgD;QAChD,+CAA+C;QAC/CgE;QACA5B;QACAE;KACD,CAAC/I,MAAM,CAACkG,WACT,EAAE;IAEN,MAAMoF,yBAAyB;QAC7B;QACA,uDAAuD;QACvD,iDAAiD;QACjD,gDAAgD;QAChD,+CAA+C;QAC/Cb;QACA5B;KACD,CAAC7I,MAAM,CAACkG;IAET,MAAMqF,yBAAyB;QAC7B;QACA,mEAAmE;QACnE,wFAAwF;QACxF,gDAAgD;QAChD,+CAA+C;QAC/CtC,aAAa;YACXyB,kBAAkB;YAClBC,aAAa9P,eAAe2Q,UAAU;QACxC;QACA3C;KACD,CAAC7I,MAAM,CAACkG;IAET,MAAMuF,sBAAsB1H,OAAO4B,WAAW;QAAC1E;KAAwB,GAAG,EAAE;IAE5E,2CAA2C;IAC3C,MAAMyK,0BAA0B,CAAC,EAC/BC,cAAc,EACdC,YAAY,EAIb,GAAK;eACAA,eAAeH,sBAAsB,EAAE;YAC3C;gBACE,iDAAiD;gBACjD,uBAAuB;gBACvBtK,QAAQ;YACV;eACIsF,YACA;gBACE,uDAAuD;gBACvD,iDAAiD;gBACjD,gDAAgD;gBAChD,+CAA+C;gBAC/CkF,iBAAiBX,wBAAwBF;gBACzCjC;gBACAE;aACD,CAAC/I,MAAM,CAACkG,WACT,EAAE;SACP;IAED,MAAM2F,yBAAyBH,wBAAwB;QACrDC,gBAAgB;QAChB,8EAA8E;QAC9EC,cAAc;IAChB;IACA,MAAME,qBAAqBJ,wBAAwB;QACjDC,gBAAgB;QAChBC,cAAc;IAChB;IAEA,2EAA2E;IAC3E,8EAA8E;IAC9E,gBAAgB;IAChB,MAAMG,wBAAwB7E,eAC1B+B,aAAa;QACXyB,kBAAkB;QAClBC,aAAa9P,eAAemR,OAAO;IACrC,KACAb,eAAeC,KAAK;IAExB,MAAMa,iBAAiBnI,OAAOmI,cAAc;IAE5C,MAAMC,aAAa9F,0BACfzL,KAAKsM,IAAI,CAACD,SAASrL,oBACnBqL;IAEJ,MAAMmF,uBAAuB;QAC3B;WACItG,eAAe9H,qBAAqB,EAAE;QAC1C,kCAAkC;QAClC;KACD;IAED,MAAMqO,oBAAoBnG,WACtBpH,wBAAwBwN,KAAK,GAC7BpN,QAAQmC,OAAO,CACb,CAAC,yDAAyD,CAAC;IAGjE,MAAMkL,gBAAgB3G,WACjB;QACC,0BAA0B;QAC1B,WAAW,EAAE;QACb,GAAI5B,MACA;YACE,CAACxI,0CAA0C,EAAE6Q;YAC7C,CAACjR,gCAAgC,EAC/B,CAAC,EAAE,CAAC,GACJR,KACG+M,QAAQ,CACP7D,KACAlJ,KAAKsM,IAAI,CAACtI,+BAA+B,OAAO,YAEjD4N,OAAO,CAAC,OAAO;QACtB,IACA,CAAC,CAAC;QACN,CAACnR,iCAAiC,EAChC,CAAC,EAAE,CAAC,GACJT,KACG+M,QAAQ,CACP7D,KACAlJ,KAAKsM,IAAI,CACPtI,+BACAoF,MAAM,CAAC,WAAW,CAAC,GAAG,YAGzBwI,OAAO,CAAC,OAAO;QACpB,GAAI9F,YACA;YACE,CAACpL,qCAAqC,EAAE0I,MACpC;gBACEqI;gBACA,CAAC,EAAE,CAAC,GACFzR,KACG+M,QAAQ,CACP7D,KACAlJ,KAAKsM,IAAI,CACPtI,+BACA,oBAGH4N,OAAO,CAAC,OAAO;aACrB,GACD;gBACE,CAAC,EAAE,CAAC,GACF5R,KACG+M,QAAQ,CACP7D,KACAlJ,KAAKsM,IAAI,CACPtI,+BACA,gBAGH4N,OAAO,CAAC,OAAO;aACrB;QACP,IACA,CAAC,CAAC;IACR,IACAlF;IAEJ,MAAMmF,gBAAkD;QACtD,yCAAyC;QACzC3J,YAAY;YAAC;YAAO;YAAQ;YAAQ;YAAO;YAAQ;YAAS;SAAQ;QACpE4J,gBAAgB3I,OAAO8C,YAAY,CAAC6F,cAAc;QAClDlK,SAAS;YACP;eACG7C;SACJ;QACD8D,OAAOvF,qBAAqB;YAC1B+I;YACArB;YACAE;YACAE;YACAhC;YACAD;YACAc;YACAM;YACArB;YACAgB;YACAwB;QACF;QACA,GAAIV,WACA;YACEnD,UAAU;gBACR7C,SAASV,QAAQmC,OAAO,CAAC;YAC3B;QACF,IACAiG,SAAS;QACb,oFAAoF;QACpFrE,YAAYlF,aAAa2G,cAAc;QACvC,GAAIoB,gBAAgB;YAClBlD,gBAAgB5E;QAClB,CAAC;QACD2O,SAAS;YACP3G,eAAe,IAAI/H,yCAAyCqJ;SAC7D,CAACrH,MAAM,CAACkG;QACT,GAAKD,YAAYhC,eACb;YACE0I,UAAU;gBACRC,YAAY3I;YACd;QACF,IACA,CAAC,CAAC;IACR;IAEA,2DAA2D;IAC3D,gEAAgE;IAChE,mEAAmE;IACnE,MAAM4I,qBAA+B,EAAE;IACvC,MAAMC,yBAAmC,EAAE;IAC3C,MAAMC,2BAA2B,IAAIC;IACrC,iDAAiD;IACjD,MAAMC,iBAAiB,CACrBC,aACAC,gBACAC;QAEA,IAAI;YACF,IAAIL,yBAAyBM,GAAG,CAACH,cAAc;gBAC7C;YACF;YACAH,yBAAyBO,GAAG,CAACJ;YAE7B,MAAMK,kBAAkBtO,QAAQmC,OAAO,CAAC,GAAG8L,YAAY,aAAa,CAAC,EAAE;gBACrEE,OAAO;oBAACD;iBAAe;YACzB;YAEA,6FAA6F;YAC7F,0EAA0E;YAC1E,eAAe;YACf,0EAA0E;YAC1E,2EAA2E;YAC3E,MAAMK,YAAY7S,KAAKsM,IAAI,CAACsG,iBAAiB;YAE7C,yFAAyF;YACzF,IAAIH,MAAMnF,QAAQ,CAACuF,YAAY;YAC/BJ,MAAMlF,IAAI,CAACsF;YACX,MAAMC,eAAexO,QAAQsO,iBAAiBE,YAAY,IAAI,CAAC;YAC/D,KAAK,MAAMC,QAAQvN,OAAOwN,IAAI,CAACF,cAAe;gBAC5CR,eAAeS,MAAMF,WAAWJ;YAClC;QACF,EAAE,OAAOQ,GAAG;QACV,uDAAuD;QACzD;IACF;IAEA,KAAK,MAAMV,eAAe;QACxB;QACA;WACIzG,YACA;YACE,CAAC,wBAAwB,EAAEK,qBAAqB;YAChD,CAAC,4BAA4B,EAAEA,qBAAqB;SACrD,GACD,EAAE;KACP,CAAE;QACDmG,eAAeC,aAAarJ,KAAKiJ;IACnC;IACAG,eAAe,QAAQpJ,KAAKgJ;IAE5B,MAAMgB,cAAc/J,OAAO+J,WAAW;IAEtC,wDAAwD;IACxD,2BAA2B;IAC3B,IAAI/J,OAAOgK,sBAAsB,IAAIlG,wBAAwB;QAC3D,MAAMmG,2BAA2BnG,uBAAuB5H,MAAM,CAAC,CAAC+H;gBAC9DjE;oBAAAA,iCAAAA,OAAOgK,sBAAsB,qBAA7BhK,+BAA+BmE,QAAQ,CAACF;;QAE1C,IAAIgG,yBAAyBxH,MAAM,GAAG,GAAG;YACvC,MAAM,qBAIL,CAJK,IAAIlH,MACR,CAAC,8FAA8F,EAAE0O,yBAAyB9G,IAAI,CAC5H,OACC,GAHC,qBAAA;uBAAA;4BAAA;8BAAA;YAIN;QACF;IACF;IAEA,+CAA+C;IAC/C,MAAM+G,yBAAyBhP,kBAAkB8I,MAAM,IACjDhE,OAAOgK,sBAAsB,IAAI,EAAE,EACvC9N,MAAM,CAAC,CAAC+H,MAAQ,EAACH,0CAAAA,uBAAwBK,QAAQ,CAACF;IACpD,wEAAwE;IACxE,MAAMkG,6BAA6B,IAAIC,OACrC,CAAC,2BAA2B,EAAEF,uBAC3BG,GAAG,CAAC,CAAClO,IAAMA,EAAEsM,OAAO,CAAC,OAAO,YAC5BtF,IAAI,CAAC,KAAK,QAAQ,CAAC;IAGxB,MAAMmH,yBAAyB,IAAIF,OACjC,CAAC,2BAA2B,EAAEtG,0CAAAA,uBAC1BuG,GAAG,CAAC,CAAClO,IAAMA,EAAEsM,OAAO,CAAC,OAAO,YAC7BtF,IAAI,CAAC,KAAK,QAAQ,CAAC;IAGxB,MAAMoH,kBAAkBxQ,oBAAoB;QAC1CiG;QACAmK;QACAK,oBAAoB1G;QACpB/D;IACF;IAEA,MAAM0K,sBAAsB,IAAIL,OAAO,CAAC,IAAI,EAAEjC,eAAehF,IAAI,CAAC,KAAK,EAAE,CAAC;IAE1E,MAAMuH,yBAAyB;QAAClG,cAAcC,IAAI;QAAEgG;KAAoB;IAExE,MAAME,iBAAiBxP,QAAQ,UAAUwP,cAAc;IAEvD,MAAMC,kCACJ,CAAC,CAAC5K,OAAO8C,YAAY,CAAC+H,mBAAmB,IAAI5K;IAE/C,MAAM6K,iBAAiB;QACrB,MAAMC,WAAWC,OAAOnP,QAAQC,GAAG,CAACmP,wBAAwB;QAC5D,IAAIL,iCAAiC;YACnC,IAAIG,UAAU;gBACZjO,QAAQC,IAAI,CACV;YAEJ;YACA,OAAO;QACT;QACA,OAAOgO,YAAYxH;IACrB;IAEA,MAAM2H,kBACJ,CAACjL,OACD4B,YACA,IAAI,AACF1G,CAAAA,QAAQ,sDAAqD,EAC7DgQ,eAAe,CACf,IAAIC,IACF;QACE;YAAC;YAAahI;SAAa;QAC3B;YAAC;YAAY,CAAC,GAACpD,mBAAAA,OAAOqE,QAAQ,qBAAfrE,iBAAiBqL,KAAK;SAAC;QACtC;YAAC;YAAuB,CAAC,GAACrL,oBAAAA,OAAOqE,QAAQ,qBAAfrE,kBAAiBsL,gBAAgB;SAAC;QAC5D;YACE;YACA,CAAC,GAACtL,oBAAAA,OAAOqE,QAAQ,qBAAfrE,kBAAiBuL,qBAAqB;SACzC;QACD;YACE;YACA,CAAC,EAACrL,6BAAAA,4BAAAA,SAAUoG,eAAe,qBAAzBpG,0BAA2BqG,sBAAsB;SACpD;QACD;YAAC;YAAoB,CAAC,GAACvG,oBAAAA,OAAOqE,QAAQ,qBAAfrE,kBAAiBwL,aAAa;SAAC;QACtD;YAAC;YAAmB,CAAC,EAACtL,6BAAAA,6BAAAA,SAAUoG,eAAe,qBAAzBpG,2BAA2BuL,eAAe;SAAC;QACjE;YAAC;YAAc,CAAC,GAACzL,oBAAAA,OAAOqE,QAAQ,qBAAfrE,kBAAiB0L,OAAO;SAAC;QAC1C;YAAC;YAAqB,CAAC,CAAC1L,OAAO+D,iBAAiB;SAAC;QACjD;YAAC;YAA8B,CAAC,CAAC/D,OAAO2L,0BAA0B;SAAC;QACnE;YAAC;YAA6B,CAAC,CAAC3L,OAAO4L,yBAAyB;SAAC;QACjE;YAAC;YAAqB,CAAC,CAAC5L,OAAOoG,iBAAiB;SAAC;QACjD,+EAA+E;QAC/E;YAAC;YAAgBpG,OAAO8C,YAAY,CAAC+I,YAAY,KAAK;SAAK;QAC3DvI;KACD,CAACpH,MAAM,CAAqBkG;IAInC,IAAI5E,gBAAuC;QACzCsO,aAAahB;QACb,GAAI7I,eAAe;YAAE8J,kBAAkB;gBAAEC,MAAM;YAAK;QAAE,IAAI,CAAC,CAAC;QAC5D,aAAa;QACbC,WACEpK,YAAYE,eAER,8DAA8D;QAC9D,+CAA+C;QAC/C;YACE;eACIA,eACA;gBACE;oBACE,yBAAyB;oBACzB,2BAA2B;gBAC7B;gBACA3J;gBACAC;aACD,GACD,EAAE;SACP,GACD;eACKsS;YACH,CAAC,EACCuB,OAAO,EACPC,OAAO,EACP3N,cAAc,EACd4N,WAAW,EACXC,UAAU,EAqBX,GACC9B,gBACE2B,SACAC,SACA3N,gBACA4N,YAAYE,WAAW,EACvB,CAAC3G;oBACC,MAAM4G,kBAAkBF,WAAW1G;oBACnC,OAAO,CAAC6G,gBAAwBC,mBAC9B,IAAIC,QAAQ,CAACpP,SAASqP;4BACpBJ,gBACEC,gBACAC,kBACA,CAACG,KAAKC,QAAQC;oCAIRA;gCAHJ,IAAIF,KAAK,OAAOD,OAAOC;gCACvB,IAAI,CAACC,QAAQ,OAAOvP,QAAQ;oCAAC;oCAAM;iCAAM;gCACzC,MAAMyP,QAAQ,SAAStI,IAAI,CAACoI,UACxBC,CAAAA,gCAAAA,mCAAAA,YAAaE,mBAAmB,qBAAhCF,iCAAkCnQ,IAAI,MACtC,WACA,UAAU8H,IAAI,CAACoI;gCACnBvP,QAAQ;oCAACuP;oCAAQE;iCAAM;4BACzB;wBAEJ;gBACJ;SAEL;QAEPE,cAAc;YACZC,cAAc,CAACjN;YACfkN,gBAAgB;YAChBC,SAAS;YAETC,aAAa,AAAC,CAAA;gBAGZ,kBAAkB;gBAClB,IAAIpN,KAAK;oBACP,IAAIgC,cAAc;wBAChB;;;;;YAKA,GACA,MAAMqL,wBAAwB,CAACC;4BAC7B,8FAA8F;4BAC9F,4EAA4E;4BAC5E,MAAMC,QACJ;4BACF,MAAMC,QAAQF,WAAWE,KAAK,CAACD;4BAC/B,OAAOC,QAAQA,KAAK,CAAC,EAAE,GAAG;wBAC5B;wBACA,OAAO;4BACLC,aAAa;gCACX,+FAA+F;gCAC/F,yDAAyD;gCACzDC,QAAQ;oCACNC,QAAQ;oCACRC,oBAAoB;oCACpBpJ,MAAM;oCACNqJ,SAAS;oCACTC,WAAW;oCACXC,kBAAkB;oCAClBC,oBAAoB;oCACpBrE,MAAM,CAAClN;wCACL,MAAMwR,WAAWxR,OAAOyR,gBAAgB;wCACxC,MAAMC,aAAad,sBAAsBY;wCACzC,IAAIE,YAAY;4CACd,OAAO,CAAC,cAAc,EAAEA,YAAY;wCACtC,OAAO;4CACL,MAAMC,OAAO1X,OAAO2X,UAAU,CAAC,QAAQC,MAAM,CAACL;4CAC9CG,KAAKE,MAAM,CAACL;4CACZ,OAAO,CAAC,cAAc,EAAEG,KAAKG,MAAM,CAAC,QAAQ;wCAC9C;oCACF;gCACF;gCACA,mCAAmC;gCACnCC,SAAS;gCACTC,gBAAgB;4BAClB;wBACF;oBACF;oBAEA,OAAO;gBACT;gBAEA,IAAIzM,gBAAgBF,cAAc;oBAChC,OAAO;wBACL4M,UAAU,GAAG5M,eAAe,CAAC,YAAY,CAAC,GAAG,GAAG,SAAS,CAAC;wBAC1D6L,QAAQ;wBACRG,WAAW;oBACb;gBACF;gBAEA,MAAMa,sBAAsB;oBAC1BhB,QAAQ;oBACRhE,MAAM;oBACN,6DAA6D;oBAC7DiF,OAAO1X;oBACPsN,MAAK/H,MAAW;wBACd,MAAMoS,WAAWpS,OAAOyR,gBAAgB,oBAAvBzR,OAAOyR,gBAAgB,MAAvBzR;wBACjB,OAAOoS,WACH9F,uBAAuB9K,IAAI,CAAC,CAAC6Q,UAC3BD,SAASE,UAAU,CAACD,YAEtB;oBACN;oBACAE,UAAU;oBACV,mEAAmE;oBACnE,wCAAwC;oBACxCC,SAAS;gBACX;gBAEA,MAAMC,gBAAgB;oBACpB1K,MAAK/H,MAIJ;4BAEIA;wBADH,OACE,GAACA,eAAAA,OAAOC,IAAI,qBAAXD,aAAasS,UAAU,CAAC,WACzBtS,OAAO0S,IAAI,KAAK,UAChB,oBAAoB3K,IAAI,CAAC/H,OAAOyR,gBAAgB,MAAM;oBAE1D;oBACAvE,MAAKlN,MAKJ;wBACC,MAAM2R,OAAO1X,OAAO2X,UAAU,CAAC;wBAC/B,IAAI7R,YAAYC,SAAS;4BACvBA,OAAO2S,UAAU,CAAChB;wBACpB,OAAO;4BACL,IAAI,CAAC3R,OAAO4S,QAAQ,EAAE;gCACpB,MAAM,qBAEL,CAFK,IAAI/T,MACR,CAAC,iCAAiC,EAAEmB,OAAOC,IAAI,CAAC,uBAAuB,CAAC,GADpE,qBAAA;2CAAA;gDAAA;kDAAA;gCAEN;4BACF;4BACA0R,KAAKE,MAAM,CAAC7R,OAAO4S,QAAQ,CAAC;gCAAEpD,SAASnM;4BAAI;wBAC7C;wBAEA,wFAAwF;wBACxF,yHAAyH;wBACzH,0CAA0C;wBAC1C,IAAIrD,OAAOmS,KAAK,EAAE;4BAChBR,KAAKE,MAAM,CAAC7R,OAAOmS,KAAK;wBAC1B;wBAEA,OAAOR,KAAKG,MAAM,CAAC,OAAOe,SAAS,CAAC,GAAG;oBACzC;oBACAN,UAAU;oBACVlB,WAAW;oBACXF,oBAAoB;gBACtB;gBAEA,kBAAkB;gBAClB,OAAO;oBACL,oDAAoD;oBACpD,qDAAqD;oBACrD,oDAAoD;oBACpD,0CAA0C;oBAC1CD,QAAQzL,WAEJ,YAAY;oBACZ,mCACA,CAACqN,QACC,CAAC,iCAAiC/K,IAAI,CAAC+K,MAAM5F,IAAI;oBAEvD,mDAAmD;oBACnD8D,aAAavL,WACT,CAAC,IACD;wBACEsN,WAAWb;wBACXc,KAAKP;oBACP;oBACJlB,oBAAoB;oBACpBH,SAAS;gBACX;YACF,CAAA;YACA6B,cAAc9N,WACV;gBAAE+H,MAAMlS;YAAoC,IAC5C6L;YAEJqM,UACE,CAAC3P,OACA4B,CAAAA,YACCE,gBACCE,gBAAgBjC,OAAO8C,YAAY,CAAC+M,kBAAkB;YAC3DC,WAAW3N,WACP;gBACE,IAAKrH,CAAAA,eAAc,EAAEiV,0BAA0B,CAAE;oBAC/C,6BAA6B;oBAC7B,iEAAiE;oBACjEC,kBAAkB;wBAChBC,UAAU;4BACRC,QAAQ;4BACRC,aAAa;gCACX,mDAAmD;4BACrD;wBACF;wBACAC,QAAQ,CAAC9O,cAAc;4BACrB+O,UAAU;gCAAC;6BAAc;4BACzBC,iBAAiB,CAACzO;wBACpB;oBACF;gBACF;gBACA,IAAK/G,CAAAA,eAAc,EAAEyV,iCAAiC,CAAE;oBACtD,8BAA8B;oBAC9BP,kBAAkB;wBAChBQ,SAASnQ;oBACX;gBACF;aACD,GACD;gBACE,oBAAoB;gBACpB,CAACgE;oBACC,4BAA4B;oBAC5B,MAAM,EAAEoM,YAAY,EAAE,GACpBtV,QAAQ;oBACV,IAAIsV,aAAa;wBAAEnP;oBAAW,GAAGoP,KAAK,CAACrM;gBACzC;gBACA,aAAa;gBACb,CAACA;oBACC,MAAM,EACJsM,kBAAkB,EACnB,GAAGxV,QAAQ;oBACZ,IAAIwV,mBAAmB;wBACrBC,gBAAgB;4BACdvG,KAAK;gCACH,+DAA+D;gCAC/D,+CAA+C;gCAC/C6F,QAAQ;gCACR,6DAA6D;gCAC7D,4DAA4D;gCAC5DW,YAAY;4BACd;wBACF;oBACF,GAAGH,KAAK,CAACrM;gBACX;aACD;QACP;QACA6H,SAASnM;QACT,8CAA8C;QAC9CwI,OAAO;YACL,OAAO;gBACL,GAAIC,gBAAgBA,gBAAgB,CAAC,CAAC;gBACtC,GAAG5H,WAAW;YAChB;QACF;QACAkQ,cAAczU,OAAOC,MAAM,CAAC;YAC1B,GAAGF,gBAAgB;YACnB2U,IAAI,GAAE/Q,uBAAAA,OAAO8Q,YAAY,qBAAnB9Q,qBAAqBgR,cAAc;QAC3C;QACAC,QAAQ;YACN,sEAAsE;YACtE,kCAAkC;YAClCC,YAAY,GACVlR,OAAOmR,WAAW,GACdnR,OAAOmR,WAAW,CAACC,QAAQ,CAAC,OAC1BpR,OAAOmR,WAAW,CAACE,KAAK,CAAC,GAAG,CAAC,KAC7BrR,OAAOmR,WAAW,GACpB,GACL,OAAO,CAAC;YACTta,MAAM,CAACoJ,OAAOgC,eAAepL,KAAKsM,IAAI,CAACiF,YAAY,YAAYA;YAC/D,oCAAoC;YACpCuG,UAAUrM,0BACNrC,OAAO8B,eACL,CAAC,SAAS,CAAC,GACX,CAAC,YAAY,CAAC,GAChB,CAAC,cAAc,EAAElB,gBAAgB,cAAc,GAAG,MAAM,EACtDZ,MAAM,KAAKmB,SAAS,iBAAiB,iBACtC,GAAG,CAAC;YACTkQ,SAASzP,YAAYE,eAAe,SAASwB;YAC7CgO,eAAe1P,YAAYE,eAAe,WAAW;YACrDyP,wBAAwB;YACxBC,uBACE;YACF,uDAAuD;YACvDC,eAAepP,0BACX,cACA,CAAC,cAAc,EAAEzB,gBAAgB,cAAc,KAC7CZ,MAAM,WAAW,uBAClB,GAAG,CAAC;YACT0R,+BAA+B;YAC/BC,oBAAoB7H;YACpB,iEAAiE;YACjE,mGAAmG;YACnG,iEAAiE;YACjE,oGAAoG;YACpG,2FAA2F;YAC3F8H,+BAA+B5R,MAC3B,6BACAsD;YACJuO,2BAA2B;YAC3BC,cAAc;YACdC,kBAAkB;QACpB;QACAC,aAAa;QACb3U,SAASoL;QACTwJ,eAAe;YACb,+BAA+B;YAC/BxS,OAAO;gBACL;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;aACD,CAACyS,MAAM,CACN,CAACzS,OAAOrC;gBACN,4DAA4D;gBAC5DqC,KAAK,CAACrC,OAAO,GAAGxG,KAAKsM,IAAI,CAACiP,WAAW,WAAW,WAAW/U;gBAE3D,OAAOqC;YACT,GACA,CAAC;YAEHjB,SAAS;gBACP;mBACG7C;aACJ;YACDgN,SAAS,EAAE;QACb;QACAlM,QAAQ;YACNiB,OAAO;gBACL,+EAA+E;gBAC/E;oBACE2O,aAAa;wBACX5H,IAAI;+BACC3N,eAAesb,KAAK,CAACC,UAAU;+BAC/Bvb,eAAesb,KAAK,CAACE,aAAa;yBACtC;oBACH;oBACAjV,SAAS;wBACP,6CAA6C;wBAC7CoC,OAAOtF,kCAAkC;oBAC3C;gBACF;gBACA;oBACEkS,aAAa;wBACXkG,KAAK;+BACAzb,eAAesb,KAAK,CAACC,UAAU;+BAC/Bvb,eAAesb,KAAK,CAACE,aAAa;yBACtC;oBACH;oBACAjV,SAAS;wBACP,6CAA6C;wBAC7CoC,OAAOtF,kCAAkC;oBAC3C;gBACF;gBACA,mEAAmE;gBACnE;oBACEqK,MAAM;wBACJ;wBACA;qBACD;oBACDpH,QAAQ;oBACRiP,aAAa;wBACX5H,IAAI3N,eAAesb,KAAK,CAACC,UAAU;oBACrC;oBACA3M,SAAS;wBACP8M,SACE;oBACJ;gBACF;gBACA;oBACEhO,MAAM;wBACJ;wBACA;qBACD;oBACDpH,QAAQ;oBACRiP,aAAa;wBACXkG,KAAK;+BACAzb,eAAesb,KAAK,CAACC,UAAU;+BAC/Bvb,eAAesb,KAAK,CAACE,aAAa;yBACtC;oBACH;oBACA5M,SAAS;wBACP8M,SACE;oBACJ;gBACF;gBACA,yFAAyF;gBACzF,iFAAiF;gBACjF,oCAAoC;gBACpC;oBACEhO,MAAM;wBACJ;wBACA;qBACD;oBACDpH,QAAQ;oBACRiP,aAAa;wBACX5H,IAAI3N,eAAesb,KAAK,CAACE,aAAa;oBACxC;gBACF;mBACItQ,eACA,EAAE,GACF;oBACE;wBACEwC,MAAM;wBACNpH,QAAQ;oBACV;iBACD;mBACDsF,YACA;oBACE;wBACE,uFAAuF;wBACvF,UAAU;wBACVkM,OAAO9X,eAAe2b,MAAM;wBAC5BjO,MAAM9I;oBACR;oBACA,4CAA4C;oBAC5C;wBACEgX,eAAe,IAAIvI,OACjBpT,yBAAyB4b,aAAa;wBAExC/D,OAAO9X,eAAe+P,qBAAqB;oBAC7C;oBACA;wBACE,gEAAgE;wBAChE,2CAA2C;wBAC3C+H,OAAO9X,eAAekQ,mBAAmB;wBACzCxC,MAAM;oBACR;oBACA;wBACE6H,aAAarV;wBACbqG,SAAS;4BACPoC,OAAOpF;wBACT;oBACF;oBACA;wBACEgS,aAAalV;wBACbkG,SAAS;4BACPoC,OAAOnF,0BAA0B;wBACnC;oBACF;oBACA;wBACE+R,aAAapV;wBACboG,SAAS;4BACPoC,OAAOnF,0BAA0B;wBACnC;oBACF;iBACD,GACD,EAAE;mBACFoI,aAAa,CAACd,WACd;oBACE;wBACEyK,aAAalV;wBACbqN,MAAM;4BACJ,8DAA8D;4BAC9D,yBAAyB;4BACzBoO,KAAK;gCACHnI;gCACA;oCACE8H,KAAK;wCAACrI;wCAA4BxO;qCAAmB;gCACvD;6BACD;wBACH;wBACAgX,eAAe;4BACb,8DAA8D;4BAC9D,8DAA8D;4BAC9D,6DAA6D;4BAC7D,8DAA8D;4BAC9D,WAAW;4BACXH,KAAK;gCACH,IAAIpI,OAAOpT,yBAAyB8b,QAAQ;gCAC5C,IAAI1I,OAAOpT,yBAAyB+b,iBAAiB;6BACtD;wBACH;wBACAzV,SAAS;4BACP4B,YAAYlF,aAAa2G,cAAc;4BACvC9B,gBAAgBwJ;4BAChB,mFAAmF;4BACnF,kFAAkF;4BAClF,8BAA8B;4BAC9B3I,OAAOrF,iBAAiB2I,qBAAqB;gCAC3C,iCAAiC;gCACjCjC;gCACA8N,OAAO9X,eAAe+P,qBAAqB;gCAC3C/E;4BACF;wBACF;wBACAhE,KAAK;oBACP;iBACD,GACD,EAAE;gBACN,kDAAkD;gBAClD,yDAAyD;mBACrD,CAACiC,OAAO8C,YAAY,CAACzD,cAAc,GACnC;oBACE;wBACEoF,MAAM;wBACNnH,SAAS;4BACP+B,gBAAgB;wBAClB;oBACF;iBACD,GACD,EAAE;mBACFsD,aAAaZ,eACb;oBACE,sEAAsE;oBACtE,mEAAmE;oBACnE,oCAAoC;oBACpC;wBACE4Q,eAAe,IAAIvI,OACjBpT,yBAAyBgc,YAAY;wBAEvCnE,OAAO9X,eAAe+P,qBAAqB;oBAC7C;iBACD,GACD,EAAE;mBACFnE,YACA;oBACE;wBACE,8CAA8C;wBAC9C,kEAAkE;wBAClEsQ,OAAO;4BACL;gCACE3G,aAAalV;gCACbqN,MAAM;oCACJ,8DAA8D;oCAC9D,yBAAyB;oCACzBoO,KAAK;wCACHnI;wCACA;4CACE8H,KAAK;gDAACrI;gDAA4BxO;6CAAmB;wCACvD;qCACD;gCACH;gCACA2B,SAAS;oCACP,8DAA8D;oCAC9D,4DAA4D;oCAC5DoC,OAAOrF,iBAAiB2I,qBAAqB;wCAC3CjC;wCACA8N,OAAO9X,eAAe+P,qBAAqB;wCAC3C/E;oCACF;gCACF;4BACF;4BACA;gCACE0C,MAAMiG;gCACN4B,aAAavV,eAAekQ,mBAAmB;gCAC/C3J,SAAS;oCACPoC,OAAOrF,iBAAiB2I,qBAAqB;wCAC3CjC;wCACA8N,OAAO9X,eAAekQ,mBAAmB;wCACzClF;oCACF;gCACF;4BACF;yBACD;oBACH;oBACA;wBACE0C,MAAMiG;wBACN4B,aAAavV,eAAeoQ,eAAe;wBAC3C7J,SAAS;4BACPoC,OAAOrF,iBAAiB2I,qBAAqB;gCAC3CjC;gCACA8N,OAAO9X,eAAeoQ,eAAe;gCACrCpF;4BACF;wBACF;oBACF;iBACD,GACD,EAAE;gBACN,iFAAiF;mBAC7EY,aAAa1C,OAAO4B,WACpB;oBACE;wBACE4C,MAAMD,cAAcC,IAAI;wBACxBG,SAAS;4BACP,+CAA+C;4BAC/CJ,cAAcI,OAAO;4BACrB0F;4BACA5O;yBACD;wBACD4Q,aAAavV,eAAeoQ,eAAe;wBAC3CpJ,KAAK4J;wBACLrK,SAAS;4BACP4B,YAAYlF,aAAa2G,cAAc;wBACzC;oBACF;iBACD,GACD,EAAE;gBACN;oBACEsS,OAAO;wBACL;4BACE,GAAGzO,aAAa;4BAChB8H,aAAavV,eAAemR,OAAO;4BACnCnK,KAAKkK;4BACL,kDAAkD;4BAClD,8DAA8D;4BAC9DiL,QAAQ;gCACNC,KAAK;4BACP;wBACF;wBACA;4BACE,GAAG3O,aAAa;4BAChB8H,aAAavV,eAAeqc,OAAO;4BACnCrV,KAAKkK;wBAGP;wBACA;4BACExD,MAAMD,cAAcC,IAAI;4BACxB6H,aAAavV,eAAe2Q,UAAU;4BACtC3J,KAAK0J;4BACLnK,SAAS;gCACP4B,YAAYlF,aAAa2G,cAAc;gCACvC9B,gBAAgBwJ;gCAChB3I,OAAOrF,iBAAiB2I,qBAAqB;oCAC3CjC;oCACA8N,OAAO9X,eAAe2Q,UAAU;oCAChC3F;gCACF;4BACF;wBACF;wBACA;4BACE0C,MAAMD,cAAcC,IAAI;4BACxB6H,aAAavV,eAAesc,UAAU;4BACtCtV,KAAKyJ;4BACLlK,SAAS;gCACP4B,YAAYlF,aAAa2G,cAAc;gCACvC9B,gBAAgBwJ;gCAChB3I,OAAOrF,iBAAiB2I,qBAAqB;oCAC3CjC;oCACA8N,OAAO9X,eAAesc,UAAU;oCAChCtR;gCACF;4BACF;wBACF;2BACIY,YACA;4BACE;gCACE8B,MAAMD,cAAcC,IAAI;gCACxB6H,aAAalV;gCACbwN,SAASjJ;gCACToC,KAAKwJ;4BACP;4BACA;gCACE9C,MAAMD,cAAcC,IAAI;gCACxBkO,eAAe,IAAIvI,OACjBpT,yBAAyBgc,YAAY;gCAEvCjV,KAAKwJ;4BACP;4BACA;gCACE9C,MAAMD,cAAcC,IAAI;gCACxB6H,aAAavV,eAAeoQ,eAAe;gCAC3C,uEAAuE;gCACvEvC,SAASnJ;gCACTsC,KAAKgK;gCACLzK,SAAS;oCACP4B,YAAYlF,aAAa2G,cAAc;gCACzC;4BACF;4BACA;gCACE8D,MAAMD,cAAcC,IAAI;gCACxB6H,aAAavV,eAAekQ,mBAAmB;gCAC/CrC,SAASjJ;gCACToC,KAAKiK;gCACL1K,SAAS;oCACP4B,YAAYlF,aAAa2G,cAAc;gCACzC;4BACF;yBACD,GACD,EAAE;wBACN;4BACE,GAAG6D,aAAa;4BAChBzG,KAAK;mCACA4J;gCACHN,eAAeC,KAAK;gCACpBrC;6BACD,CAAC/I,MAAM,CAACkG;wBACX;qBACD;gBACH;mBAEI,CAACpC,OAAOsT,MAAM,CAACC,mBAAmB,GAClC;oBACE;wBACE9O,MAAM5E;wBACNxC,QAAQ;wBACRmW,QAAQ;4BAAEhB,KAAK3Z;wBAAa;wBAC5B4a,YAAY;4BAAEjB,KAAK;gCAAC;6BAAM;wBAAC;wBAC3BG,eAAe;4BACbH,KAAK;gCACH,IAAIpI,OAAOpT,yBAAyB8b,QAAQ;gCAC5C,IAAI1I,OAAOpT,yBAAyB4b,aAAa;gCACjD,IAAIxI,OAAOpT,yBAAyB+b,iBAAiB;6BACtD;wBACH;wBACApN,SAAS;4BACP+N,OAAOzT;4BACPU;4BACAgT,UAAU3T,OAAO2T,QAAQ;4BACzBxC,aAAanR,OAAOmR,WAAW;wBACjC;oBACF;iBACD,GACD,EAAE;mBACFpP,eACA;oBACE;wBACEzE,SAAS;4BACPoB,UAAU;gCACR7C,SAASV,QAAQmC,OAAO,CAAC;4BAC3B;wBACF;oBACF;iBACD,GACDuE,WACE;oBACE;wBACEvE,SAAS;4BACPoB,UACEsB,OAAO8C,YAAY,CAAC8Q,qBAAqB,KAAK,QAC1C;gCACEC,QAAQ;gCACRC,QAAQ;gCACRC,WAAW;gCACXpd,QAAQ;gCACRqd,QAAQ;gCACRC,MAAM;gCACNC,OAAO;gCACPC,IAAI;gCACJtd,MAAM;gCACNud,UAAU;gCACVvY,SAAS;gCACTwY,aAAa;gCACbC,QAAQ;gCACRC,gBAAgB;gCAChBC,KAAK;gCACLC,QAAQ;gCACRC,KAAK;gCACLC,MAAM;gCACNC,IAAI;gCACJC,MAAM;gCACNC,QAAQ;gCACRC,cAAc;4BAChB,IACA;gCACElB,QAAQ1Y,QAAQmC,OAAO,CACrB;gCAEFwW,QAAQ3Y,QAAQmC,OAAO,CACrB;gCAEFyW,WAAW5Y,QAAQmC,OAAO,CACxB;gCAEF3G,QAAQwE,QAAQmC,OAAO,CACrB;gCAEF0W,QAAQ7Y,QAAQmC,OAAO,CACrB;gCAEF2W,MAAM9Y,QAAQmC,OAAO,CACnB;gCAEF4W,OAAO/Y,QAAQmC,OAAO,CACpB;gCAEF6W,IAAIhZ,QAAQmC,OAAO,CACjB;gCAEFzG,MAAMsE,QAAQmC,OAAO,CACnB;gCAEF8W,UAAUjZ,QAAQmC,OAAO,CACvB;gCAEFzB,SAASV,QAAQmC,OAAO,CAAC;gCACzB,4BAA4B;gCAC5B+W,aAAalZ,QAAQmC,OAAO,CAC1B;gCAEFgX,QAAQnZ,QAAQmC,OAAO,CACrB;gCAEFiX,gBAAgBpZ,QAAQmC,OAAO,CAC7B;gCAEFkX,KAAKrZ,QAAQmC,OAAO,CAAC;gCACrBmX,QAAQtZ,QAAQmC,OAAO,CACrB;gCAEFoX,KAAKvZ,QAAQmC,OAAO,CAClB;gCAEF,4BAA4B;gCAC5B,+BAA+B;gCAC/BqX,MAAMxZ,QAAQmC,OAAO,CAAC;gCACtBsX,IAAIzZ,QAAQmC,OAAO,CACjB;gCAEFuX,MAAM1Z,QAAQmC,OAAO,CACnB;gCAEFwX,QAAQ3Z,QAAQmC,OAAO,CACrB;gCAEFyX,cAAc5Z,QAAQmC,OAAO,CAC3B;4BAEJ;wBACR;oBACF;iBACD,GACD,EAAE;gBACR;oBACE,oEAAoE;oBACpE,6BAA6B;oBAC7BmH,MAAM;oBACNuQ,aAAa;gBACf;gBACA,0EAA0E;gBAC1E,yEAAyE;gBACzE,4GAA4G;gBAC5G,gEAAgE;gBAChE,0DAA0D;gBAC1D,iFAAiF;gBACjF,iFAAiF;gBACjF;oBACEvQ,MAAM;oBACNuQ,aAAa;gBACf;gBACA;oBACE,uEAAuE;oBACvE,uEAAuE;oBACvE,mDAAmD;oBACnD,iEAAiE;oBACjE,mEAAmE;oBACnE,qEAAqE;oBACrE,4DAA4D;oBAC5DvQ,MAAM;oBACN1G,KAAK,CAAC,EAAE4U,aAAa,EAA6B;4BAE9CA;wBADF,MAAMsC,QAAQ,AACZtC,CAAAA,EAAAA,uBAAAA,cAAclF,KAAK,CAAC,uCAApBkF,oBAAwC,CAAC,EAAE,KAAI,EAAC,EAChD3W,KAAK,CAAC;wBAER,OAAO;4BACL;gCACEqB,QAAQ;gCACRsI,SAAS;oCACPsP;oCACAlP,aAAalP,KAAKsM,IAAI,CACpBpD,KACAC,CAAAA,0BAAAA,OAAQkD,OAAO,KAAI,SACnB,SACA;gCAEJ;gCACA,gEAAgE;gCAChE,2DAA2D;gCAC3D,gBAAgB;gCAChBgS,OAAO,wBAAwBvC;4BACjC;yBACD;oBACH;gBACF;gBACA;oBACErV,SAAS;wBACPoC,OAAO;4BACLyV,MAAMva;wBACR;oBACF;gBACF;aACD;QACH;QACAgO,SAAS;YACP3G,gBACE,IAAIL,QAAQwT,6BAA6B,CACvC,6BACA,SAAUtG,QAAQ;gBAChB,MAAMuG,aAAaxe,KAAKye,QAAQ,CAC9BxG,SAAS3C,OAAO,EAChB;gBAEF,MAAM0C,QAAQC,SAAS1C,WAAW,CAACE,WAAW;gBAC9C,IAAIiJ;gBAEJ,OAAQ1G;oBACN,KAAK9X,eAAekQ,mBAAmB;oBACvC,KAAKlQ,eAAe+P,qBAAqB;oBACzC,KAAK/P,eAAeoQ,eAAe;oBACnC,KAAKpQ,eAAeye,aAAa;wBAC/BD,UAAU;wBACV;oBACF;wBACEA,UAAU;gBACd;gBACAzG,SAAS3C,OAAO,GAAG,CAAC,+BAA+B,EAAEoJ,QAAQ,mBAAmB,EAAEF,YAAY;YAChG;YAEJpV,OAAO,IAAIvG,wBAAwB;gBAAE+b,gBAAgB;YAAE;YACvDxV,OACE4B,YACCM,CAAAA,WAEG,IAAKpH,CAAAA,uBAAsB,EAAU;gBACnC2a,cAAc;gBACdC,aAAa;gBACbC,SAAS;YACX,KACA,IAAIpf,0BAA0BI,QAAO;YAC3C,6GAA6G;YAC5GiL,CAAAA,YAAYE,YAAW,KACtB,IAAIH,QAAQiU,aAAa,CAAC;gBACxB,0CAA0C;gBAC1CC,QAAQ;oBAAC3a,QAAQmC,OAAO,CAAC;oBAAW;iBAAS;gBAC7C,sDAAsD;gBACtD,GAAIuE,YAAY;oBAAEhG,SAAS;wBAACV,QAAQmC,OAAO,CAAC;qBAAW;gBAAC,CAAC;YAC3D;YACFzD,mBAAmB;gBACjBkc,aAAa;gBACb/V;gBACAC;gBACAiD;gBACA1B;gBACAe;gBACAV;gBACAE;gBACAO;gBACAL;gBACAZ;gBACA2U,sBAAsBtU;YACxB;YACAG,YACE,IAAIlJ,oBAAoB;gBACtBgW,UAAU/W;gBACVkJ;gBACAM;gBACA6U,cAAc,CAAC,OAAO,EAAEte,mCAAmC,GAAG,CAAC;gBAC/DsI;YACF;YACF,oDAAoD;YACpD,CAACkC,YAAaN,CAAAA,YAAYE,YAAW,KAAM,IAAIvJ;YAC/CyJ,gBACE,CAAChC,OACD,IAAK9E,CAAAA,QAAQ,kDAAiD,EAC3D+a,sBAAsB,CACvB;gBACErQ,SAAS9F;gBACTqB,QAAQA;gBACRN,UAAUA;gBACV+K,cAAc7L,OAAO8C,YAAY,CAAC+I,YAAY;gBAC9CsK,uBAAuBnW,OAAOmW,qBAAqB;gBACnDC,eAAezT;gBACf0T,cAAc,EAAE;gBAChB1V;YACF;YAEJ,4EAA4E;YAC5E,yEAAyE;YACzE,0EAA0E;YAC1E,kEAAkE;YAClEX,OAAOsW,2BAA2B,IAChC,IAAI1U,QAAQ2U,YAAY,CAAC;gBACvBC,gBAAgB;gBAChBC,eAAe;YACjB;eACExW,MACA,AAAC,CAAA;gBACC,0FAA0F;gBAC1F,qGAAqG;gBACrG,MAAM,EAAEyW,6BAA6B,EAAE,GACrCvb,QAAQ;gBACV,MAAMwb,aAAoB;oBACxB,IAAID,8BAA8B;wBAChC9P,kBAAkBjE;oBACpB;iBACD;gBAED,IAAId,YAAYE,cAAc;oBAC5B4U,WAAWvS,IAAI,CAAC,IAAIxC,QAAQgV,0BAA0B;gBACxD;gBAEA,OAAOD;YACT,CAAA,MACA,EAAE;YACN,CAAC1W,OACC,IAAI2B,QAAQ2U,YAAY,CAAC;gBACvBC,gBAAgB;gBAChBC,eAAe;YACjB;YACFnU,2BACE,IAAI7J,oBAAoB;gBACtBwH;gBACAmW,eAAezT;gBACfkU,eAAe9U;gBACfmB,SAAS,CAACjD,MAAMiD,UAAUK;YAC5B;YACF,iEAAiE;YACjE,wDAAwD;YACxDxB,gBACE,IAAI5J,iBAAiB;gBACnB8H;gBACA6W,YAAY,CAAC7W,OAAO,CAAC,GAACD,2BAAAA,OAAO8C,YAAY,CAACiU,GAAG,qBAAvB/W,yBAAyBgX,SAAS;gBACxDhW;gBACAiW,kBAAkB;oBAChBC,iBAAiBzW;oBACjB0W,oCAAoCzW;oBACpC,GAAGe,gBAAgB;gBACrB;YACF;YACFI,YACE,IAAIvJ,oBAAoB;gBACtBmI;gBACAO;gBACAH;gBACAuV,eAAezT;gBACfpB;YACF;YACFY,WACI,IAAInH,sBAAsB;gBAAEmG;YAAe,KAC3C,IAAIzI,gBAAgB;gBAAEyI;gBAAgB0E,SAAS9F;YAAI;YACvD,IAAInH;YACJiJ,YACE,IAAI/I,eAAe;gBACjB,yDAAyD;gBACzDse,UAAUjc,QAAQmC,OAAO,CAAC;gBAC1B+Z,UAAUxb,QAAQC,GAAG,CAACwb,cAAc;gBACpC1N,MAAM,CAAC,uBAAuB,EAAE3J,MAAM,KAAK,UAAU,GAAG,CAAC;gBACzD2P,UAAU;gBACVjM,MAAM;oBACJ,CAACnM,6CAA6C,EAAE;oBAChD,gCAAgC;oBAChC+f,WAAW;gBACb;YACF;YACF5U,aAAad,YAAY,IAAIvI,uBAAuB;gBAAE2G;YAAI;YAC1D0C,aACGd,CAAAA,WACG,IAAI9I,8BAA8B;gBAChCkH;gBACAmB;gBACAoW,uBAAuB,CAAC,CAACxX,OAAO8C,YAAY,CAAC2U,SAAS;YACxD,KACA,IAAIze,wBAAwB;gBAC1BoI;gBACAnB;gBACA8B;gBACArB;YACF,EAAC;YACPiC,aACE,CAACd,YACD,IAAI1I,gBAAgB;gBAClB4G;gBACAmD,SAASlD,OAAOkD,OAAO;gBACvB9B;gBACAnB;gBACA8B;gBACAoG,gBAAgBnI,OAAOmI,cAAc;gBACrCpF,aAAaF;gBACb6U,iBAAiB1X,OAAO8C,YAAY,CAAC6U,SAAS;gBAC9C1W;gBACAC;YACF;YACF,CAACjB,OACC4B,YACA,CAAC,GAAC7B,4BAAAA,OAAO8C,YAAY,CAACiU,GAAG,qBAAvB/W,0BAAyBgX,SAAS,KACpC,IAAIzd,2BAA2ByG,OAAO8C,YAAY,CAACiU,GAAG,CAACC,SAAS;YAClEnV,YACE,IAAIrI,uBAAuB;gBACzB4H;YACF;YACF,CAACe,YACC,CAAClC,OACD4B,YACA7B,OAAO8C,YAAY,CAAC8U,WAAW,IAC/B,IAAInd,kBAAkBuF,OAAO8C,YAAY,CAAC8U,WAAW,KAAK;YAC5D1M;YACA,CAACjL,OACCgC,gBACA,IAAI,AACF9G,CAAAA,QAAQ,sDAAqD,EAC7DgQ,eAAe,CAAC,IAAIC;YACxBR,mCACE,IAAI,AACFzP,CAAAA,QAAQ,iDAAgD,EACxDsT,OAAO,CAAC;gBACR9N;gBACA,GAAGX,OAAO8C,YAAY,CAAC+H,mBAAmB;YAC5C;SACH,CAAC3O,MAAM,CAACkG;IACX;IAEA,wCAAwC;IACxC,mEAAmE;IACnE,IAAIhC,mBAAmB,CAACA,gBAAgByX,UAAU,EAAE;YAClDra,gCAAAA;SAAAA,0BAAAA,cAAcF,OAAO,sBAArBE,iCAAAA,wBAAuBiB,OAAO,qBAA9BjB,+BAAgC4G,IAAI,CAAChE,gBAAgB0X,OAAO;IAC9D;KAIAta,yBAAAA,cAAcF,OAAO,sBAArBE,iCAAAA,uBAAuBoL,OAAO,qBAA9BpL,+BAAgCua,OAAO,CACrC,IAAIxf,oBACF2H,CAAAA,6BAAAA,6BAAAA,SAAUoG,eAAe,qBAAzBpG,2BAA2BoJ,KAAK,KAAI,CAAC,GACrClJ;IAIJ,MAAMuB,iBAAiBnE;IAEvB,IAAIuE,cAAc;YAChBJ,8BAAAA,wBAMAA,+BAAAA,yBAMAA,+BAAAA;SAZAA,yBAAAA,eAAejF,MAAM,sBAArBiF,+BAAAA,uBAAuBhE,KAAK,qBAA5BgE,6BAA8BoW,OAAO,CAAC;YACpCtT,MAAM;YACNpH,QAAQ;YACRV,MAAM;YACNgW,eAAe;QACjB;SACAhR,0BAAAA,eAAejF,MAAM,sBAArBiF,gCAAAA,wBAAuBhE,KAAK,qBAA5BgE,8BAA8BoW,OAAO,CAAC;YACpCtE,YAAY;YACZpW,QAAQ;YACRV,MAAM;YACNkS,OAAO9X,eAAeihB,SAAS;QACjC;SACArW,0BAAAA,eAAejF,MAAM,sBAArBiF,gCAAAA,wBAAuBhE,KAAK,qBAA5BgE,8BAA8BoW,OAAO,CAAC;YACpCzL,aAAavV,eAAeihB,SAAS;YACrCrb,MAAM;QACR;IACF;IAEAgF,eAAesW,WAAW,GAAG;QAC3BC,QAAQ;QACRC,iBAAiB;QACjBC,WAAWpa,MAAMC,OAAO,CAAC+B,OAAO8C,YAAY,CAACuV,UAAU,IACnD;YACEC,aAAatY,OAAO8C,YAAY,CAACuV,UAAU;YAC3CE,eAAe1hB,KAAKsM,IAAI,CAACpD,KAAK;YAC9ByY,kBAAkB3hB,KAAKsM,IAAI,CAACpD,KAAK;QACnC,IACAC,OAAO8C,YAAY,CAACuV,UAAU,GAC5B;YACEE,eAAe1hB,KAAKsM,IAAI,CAACpD,KAAK;YAC9ByY,kBAAkB3hB,KAAKsM,IAAI,CAACpD,KAAK;YACjC,GAAGC,OAAO8C,YAAY,CAACuV,UAAU;QACnC,IACA9U;IACR;IAEA5B,eAAejF,MAAM,CAAEwW,MAAM,GAAG;QAC9BuF,YAAY;YACVtF,KAAK;QACP;IACF;IACAxR,eAAejF,MAAM,CAAEgc,SAAS,GAAG;QACjCC,OAAO;YACLhK,UAAU;QACZ;IACF;IAEA,IAAI,CAAChN,eAAesP,MAAM,EAAE;QAC1BtP,eAAesP,MAAM,GAAG,CAAC;IAC3B;IACA,IAAIpP,UAAU;QACZF,eAAesP,MAAM,CAAC2H,YAAY,GAAG;IACvC;IAEA,IAAI/W,YAAYE,cAAc;QAC5BJ,eAAesP,MAAM,CAAC4H,mBAAmB,GAAG;YAAC;SAAS;IACxD;IAEA,iDAAiD;IACjD,wDAAwD;IACxD,oDAAoD;IACpDlX,eAAemX,QAAQ,GAAG,CAAC;IAC3B,IAAIjd,QAAQqK,QAAQ,CAACD,GAAG,KAAK,KAAK;QAChCtE,eAAemX,QAAQ,CAACC,YAAY,GAAG;YACrC;SACD;IACH,OAAO;QACLpX,eAAemX,QAAQ,CAACC,YAAY,GAAG;YAAC;SAA+B;IACzE;IACA,IAAIld,QAAQqK,QAAQ,CAACD,GAAG,KAAK,KAAK;QAChCtE,eAAemX,QAAQ,CAACE,cAAc,GAAG;YACvC;SACD;IACH;IAEA,IAAI/Y,KAAK;QACP,IAAI,CAAC0B,eAAesL,YAAY,EAAE;YAChCtL,eAAesL,YAAY,GAAG,CAAC;QACjC;QAEA,2EAA2E;QAC3E,2CAA2C;QAC3C,IAAI,CAACtK,WAAW;YACdhB,eAAesL,YAAY,CAACgM,eAAe,GAAG;QAChD;QACAtX,eAAesL,YAAY,CAACiM,WAAW,GAAG;IAC5C;IAEA,MAAMC,aAAaC,KAAKC,SAAS,CAAC;QAChCnV,sBAAsB,EAAElE,2BAAAA,wBAAAA,OAAQ8C,YAAY,qBAApB9C,sBAAsBkE,sBAAsB;QACpE6F,aAAa/J,OAAO+J,WAAW;QAC/B5B,gBAAgBA;QAChBmR,eAAetZ,OAAOsZ,aAAa;QACnCC,uBACEvZ,OAAOwZ,aAAa,KAAK,QACrBjW,YACAvD,OAAOwZ,aAAa,CAACC,QAAQ;QACnCC,6BAA6B,CAAC,CAAC1Z,OAAO0Z,2BAA2B;QACjEC,iBAAiB3Z,OAAO2Z,eAAe;QACvCC,aAAa5Z,OAAO8C,YAAY,CAAC8W,WAAW;QAC5CC,mBAAmB7Z,OAAO8C,YAAY,CAAC+W,iBAAiB;QACxDC,mBAAmB9Z,OAAO8C,YAAY,CAACgX,iBAAiB;QACxD/W,aAAa/C,OAAO8C,YAAY,CAACC,WAAW;QAC5C4Q,UAAU3T,OAAO2T,QAAQ;QACzB2C,6BAA6BtW,OAAOsW,2BAA2B;QAC/DnF,aAAanR,OAAOmR,WAAW;QAC/BvO;QACAiU,eAAe9U;QACfhB;QACAnK,SAAS,CAAC,CAACoJ,OAAOpJ,OAAO;QACzB2L;QACAwX,WAAW3W;QACXoI,aAAa,GAAExL,oBAAAA,OAAOqE,QAAQ,qBAAfrE,kBAAiBwL,aAAa;QAC7CD,qBAAqB,GAAEvL,oBAAAA,OAAOqE,QAAQ,qBAAfrE,kBAAiBuL,qBAAqB;QAC7DD,gBAAgB,GAAEtL,oBAAAA,OAAOqE,QAAQ,qBAAfrE,kBAAiBsL,gBAAgB;QACnDD,KAAK,GAAErL,oBAAAA,OAAOqE,QAAQ,qBAAfrE,kBAAiBqL,KAAK;QAC7BK,OAAO,GAAE1L,oBAAAA,OAAOqE,QAAQ,qBAAfrE,kBAAiB0L,OAAO;QACjCtF,mBAAmBpG,OAAOoG,iBAAiB;QAC3C4T,iBAAiBha,OAAOsT,MAAM,CAAC2G,UAAU;QACzCC,qBAAqBla,OAAO8C,YAAY,CAACoX,mBAAmB;QAC5DC,kBAAkBna,OAAO8C,YAAY,CAACqX,gBAAgB;QACtDnU,yBAAyBtF;IAC3B;IAEA,MAAM0Z,QAAa;QACjBzd,MAAM;QACN,mFAAmF;QACnF0d,sBAAsBpa,MAAM,IAAIqa;QAChC,YAAY;QACZ,kHAAkH;QAClH,qBAAqB;QACrB,iDAAiD;QACjDhf,SAAS,GAAG8W,UAAU,CAAC,EAAEvW,QAAQC,GAAG,CAACwb,cAAc,CAAC,CAAC,EAAE6B,YAAY;QACnEoB,gBAAgB1jB,KAAKsM,IAAI,CAACD,SAAS,SAAS;QAC5C,gIAAgI;QAChI,8GAA8G;QAC9G,yGAAyG;QACzG,kEAAkE;QAClEsX,aAAava,MAAM,SAAS;IAC9B;IAEA,oFAAoF;IACpF,IAAID,OAAOpJ,OAAO,IAAIoJ,OAAO8I,UAAU,EAAE;QACvCsR,MAAMK,iBAAiB,GAAG;YACxBza,QAAQ;gBAACA,OAAO8I,UAAU;aAAC;YAC3B,uGAAuG;YACvG4R,gBAAgB,EAAE;QACpB;IACF,OAAO;QACLN,MAAMK,iBAAiB,GAAG;YACxB,uGAAuG;YACvGC,gBAAgB,EAAE;QACpB;IACF;KACA/Y,0BAAAA,eAAeiH,OAAO,qBAAtBjH,wBAAwByC,IAAI,CAAC,CAACC;QAC5BA,SAASsW,KAAK,CAACC,IAAI,CAACC,GAAG,CAAC,2BAA2B,CAACC;YAClD,MAAML,oBAAoBK,MAAMC,WAAW,CAACN,iBAAiB;YAC7D,MAAMO,cAAcnkB,KAAKokB,OAAO,CAAC9f,QAAQmC,OAAO,CAAC;YACjD,sFAAsF;YACtF,2EAA2E;YAC3E,KAAK,MAAM4d,OAAOT,kBAAmB;gBACnC,IAAIS,IAAIlM,UAAU,CAACgM,cAAc;oBAC/BP,kBAAkBU,MAAM,CAACD;gBAC3B;YACF;QACF;IACF;IAEAvZ,eAAeyY,KAAK,GAAGA;IAEvB,IAAIve,QAAQC,GAAG,CAACsf,oBAAoB,EAAE;QACpC,MAAMC,QAAQxf,QAAQC,GAAG,CAACsf,oBAAoB,CAACjX,QAAQ,CAAC;QACxD,MAAMmX,gBACJzf,QAAQC,GAAG,CAACsf,oBAAoB,CAACjX,QAAQ,CAAC;QAC5C,MAAMoX,gBACJ1f,QAAQC,GAAG,CAACsf,oBAAoB,CAACjX,QAAQ,CAAC;QAC5C,MAAMqX,gBACJ3f,QAAQC,GAAG,CAACsf,oBAAoB,CAACjX,QAAQ,CAAC;QAC5C,MAAMsX,gBACJ5f,QAAQC,GAAG,CAACsf,oBAAoB,CAACjX,QAAQ,CAAC;QAE5C,MAAMuX,UACJ,AAACJ,iBAAiBzZ,YAAc0Z,iBAAiBjZ;QACnD,MAAMqZ,UACJ,AAACH,iBAAiB3Z,YAAc4Z,iBAAiBnZ;QAEnD,MAAMsZ,aAAa,CAACP,SAAS,CAACK,WAAW,CAACC;QAE1C,IAAIC,cAAcP,OAAO;YACvB1Z,eAAeka,qBAAqB,GAAG;gBACrCC,OAAO;gBACPC,OAAO;YACT;QACF;QAEA,IAAIH,cAAcF,SAAS;YACzB/Z,eAAeiH,OAAO,CAAExE,IAAI,CAAC,CAACC;gBAC5BA,SAASsW,KAAK,CAACC,IAAI,CAACC,GAAG,CAAC,wBAAwB,CAACC;oBAC/Che,QAAQkf,GAAG,CACTlB,MAAMmB,QAAQ,CAAC;wBACbC,QAAQ;wBACRC,SAASP,aAAa,QAAQ;oBAChC;gBAEJ;YACF;QACF,OAAO,IAAID,SAAS;YAClBha,eAAeiH,OAAO,CAAExE,IAAI,CAAC,CAACC;gBAC5BA,SAASsW,KAAK,CAACC,IAAI,CAACC,GAAG,CAAC,wBAAwB,CAACC;oBAC/Che,QAAQkf,GAAG,CACTlB,MAAMmB,QAAQ,CAAC;wBACbG,QAAQ;wBACRF,QAAQ;wBACRG,SAAS;oBACX;gBAEJ;YACF;QACF;QAEA,IAAIX,SAAS;YACX,MAAMY,iBACJ1lB,QAAQ0lB,cAAc;YACxB3a,eAAeiH,OAAO,CAAExE,IAAI,CAC1B,IAAIkY,eAAe;gBACjBZ,SAAS;YACX;YAEF/Z,eAAe+Z,OAAO,GAAG;QAC3B;IACF;IAEAle,gBAAgB,MAAMtF,mBAAmBsF,eAAe;QACtD6C;QACAkc,eAAexc;QACfyc,eAAe1b,WACX,IAAIsJ,OAAOtT,mBAAmBD,KAAKsM,IAAI,CAACrC,UAAU,CAAC,IAAI,CAAC,MACxDyC;QACJZ;QACA8Z,eAAexc;QACf2F,UAAUtD;QACVuU,eAAe9U;QACf2a,WAAW7a,YAAYE;QACvBoP,aAAanR,OAAOmR,WAAW,IAAI;QACnCwL,aAAa3c,OAAO2c,WAAW;QAC/BjD,6BAA6B1Z,OAAO0Z,2BAA2B;QAC/DkD,QAAQ5c,OAAO4c,MAAM;QACrB9Z,cAAc9C,OAAO8C,YAAY;QACjCyQ,qBAAqBvT,OAAOsT,MAAM,CAACC,mBAAmB;QACtDxP,mBAAmB/D,OAAO+D,iBAAiB;QAC3CoW,kBAAkBna,OAAO8C,YAAY,CAACqX,gBAAgB;IACxD;IAEA,0BAA0B;IAC1B3c,cAAc4c,KAAK,CAACxQ,IAAI,GAAG,GAAGpM,cAAcoM,IAAI,CAAC,CAAC,EAAEpM,cAAcqf,IAAI,GACpEhc,gBAAgB,cAAc,IAC9B;IAEF,IAAIZ,KAAK;QACP,IAAIzC,cAAcd,MAAM,EAAE;YACxBc,cAAcd,MAAM,CAACogB,WAAW,GAAG,CAACpgB,SAClC,CAAC6D,mBAAmBkE,IAAI,CAAC/H,OAAOoS,QAAQ;QAC5C,OAAO;YACLtR,cAAcd,MAAM,GAAG;gBACrBogB,aAAa,CAACpgB,SAAgB,CAAC6D,mBAAmBkE,IAAI,CAAC/H,OAAOoS,QAAQ;YACxE;QACF;IACF;IAEA,IAAIiO,kBAAkBvf,cAAcX,OAAO;IAC3C,IAAI,OAAOmD,OAAOpJ,OAAO,KAAK,YAAY;YACd4G,wBA0CtBmE,6BAKKA;QA/CT,MAAMqb,qBAAoBxf,yBAAAA,cAAcoL,OAAO,qBAArBpL,uBAAuBiF,MAAM;QAEvDjF,gBAAgBwC,OAAOpJ,OAAO,CAAC4G,eAAe;YAC5CuC;YACAE;YACA2F,UAAUtD;YACV7B;YACAT;YACAqH;YACA4V,YAAY5gB,OAAOwN,IAAI,CAACjJ,aAAa6B,MAAM;YAC3C7L;YACA,GAAI0L,0BACA;gBACE4a,aAAanb,eAAe,SAAS;YACvC,IACA,CAAC,CAAC;QACR;QAEA,IAAImJ,mBAAmB8R,mBAAmB;gBACfxf;YAAzB,MAAM2f,oBAAmB3f,0BAAAA,cAAcoL,OAAO,qBAArBpL,wBAAuBiF,MAAM;YACtD,IAAI0a,kBAAkB;gBACpB,MAAMC,iBAAiBD,qBAAqBH;gBAC5C9R,gBAAgBmS,QAAQ,CAAC,kBAAkBD,iBAAiB,IAAI;YAClE;QACF;QAEA,IAAI,CAAC5f,eAAe;YAClB,MAAM,qBAGL,CAHK,IAAIjC,MACR,CAAC,6GAA6G,EAAEyE,OAAOsd,cAAc,CAAC,GAAG,CAAC,GACxI,iFAFE,qBAAA;uBAAA;4BAAA;8BAAA;YAGN;QACF;QAEA,IAAIrd,OAAO8c,oBAAoBvf,cAAcX,OAAO,EAAE;YACpDW,cAAcX,OAAO,GAAGkgB;YACxBngB,qBAAqBmgB;QACvB;QAEA,wDAAwD;QACxD,MAAMpb,iBAAiBnE;QAEvB,0EAA0E;QAC1E,IAAImE,EAAAA,8BAAAA,eAAesW,WAAW,qBAA1BtW,4BAA4B4b,eAAe,MAAK,MAAM;YACxD5b,eAAesW,WAAW,CAACsF,eAAe,GAAG;gBAC3CC,SAAS;YACX;QACF,OAAO,IACL,SAAO7b,+BAAAA,eAAesW,WAAW,qBAA1BtW,6BAA4B4b,eAAe,MAAK,YACvD5b,eAAesW,WAAW,CAACsF,eAAe,CAACC,OAAO,KAAK,OACvD;YACA7b,eAAesW,WAAW,CAACsF,eAAe,CAACC,OAAO,GAAG;QACvD;QAEA,IAAI,OAAO,AAAChgB,cAAsBigB,IAAI,KAAK,YAAY;YACrD3gB,QAAQC,IAAI,CACV;QAEJ;IACF;IACA,MAAMY,QAAQH,EAAAA,wBAAAA,cAAcd,MAAM,qBAApBc,sBAAsBG,KAAK,KAAI,EAAE;IAE/C,MAAM+f,gBAAgB/f,MAAMggB,IAAI,CAC9B,CAAC9f,OACC,AAACA,QACC,OAAOA,SAAS,YAChBA,KAAKR,MAAM,KAAK,uBAChB,UAAUQ,QACVA,KAAK4G,IAAI,YAAY2F,UACrBvM,KAAK4G,IAAI,CAACA,IAAI,CAAC,WACjB;IAGJ,IAAIiZ,iBAAiB/a,WAAW;QAC9B,wEAAwE;QACxE,qEAAqE;QACrE,0CAA0C;QAC1ChF,MAAMyG,IAAI,CAAC;YACTK,MAAMiZ,cAAcjZ,IAAI;YACxBwO,OAAO;gBACLlc,eAAe+P,qBAAqB;gBACpC/P,eAAekQ,mBAAmB;gBAClClQ,eAAeoQ,eAAe;aAC/B,CAACkD,GAAG,CAAC,CAACwE,QAAW,CAAA;oBAChBvC,aAAauC;oBACbvR,SAAS;wBACPoC,OAAOrF,iBAAiB2I,qBAAqB;4BAC3CjC;4BACA8N;4BACA9M;wBACF;oBACF;gBACF,CAAA;QACF;IACF;IAEA,IAAI,CAAC/B,OAAOsT,MAAM,CAACC,mBAAmB,EAAE;QACtC,MAAMqK,gBAAgBjgB,MAAMggB,IAAI,CAC9B,CAAC9f,OACCA,QAAQ,OAAOA,SAAS,YAAYA,KAAKR,MAAM,KAAK;QAExD,IAAIqgB,iBAAiBE,iBAAiB,OAAOA,kBAAkB,UAAU;YACvE,uDAAuD;YACvD,wDAAwD;YACxD,8CAA8C;YAC9CA,cAAcnZ,IAAI,GAAG;QACvB;IACF;IAEA,IACEzE,OAAO8C,YAAY,CAAC+a,SAAS,MAC7BrgB,yBAAAA,cAAcd,MAAM,qBAApBc,uBAAsBG,KAAK,KAC3BH,cAAcoL,OAAO,EACrB;QACA,kEAAkE;QAClE,iEAAiE;QACjE,kJAAkJ;QAClJ,MAAMkV,oBAAoB;YAAC;SAA8B;QACzD,MAAMC,aAAa;YACjBnZ,SAASkZ;YACTtK,QAAQsK;YACRnhB,MAAM;QACR;QAEA,MAAMqhB,WAAW,EAAE;QACnB,MAAMC,aAAa,EAAE;QAErB,KAAK,MAAMpgB,QAAQL,cAAcd,MAAM,CAACiB,KAAK,CAAE;YAC7C,IAAI,CAACE,QAAQ,OAAOA,SAAS,UAAU;YACvC,IAAIA,KAAKP,OAAO,EAAE;gBAChB0gB,SAAS5Z,IAAI,CAACvG;YAChB,OAAO;gBACL,IACEA,KAAKoV,KAAK,IACV,CAAEpV,CAAAA,KAAK4G,IAAI,IAAI5G,KAAK+G,OAAO,IAAI/G,KAAKiR,QAAQ,IAAIjR,KAAK2V,MAAM,AAAD,GAC1D;oBACA3V,KAAKoV,KAAK,CAACrV,OAAO,CAAC,CAACO,IAAM8f,WAAW7Z,IAAI,CAACjG;gBAC5C,OAAO;oBACL8f,WAAW7Z,IAAI,CAACvG;gBAClB;YACF;QACF;QAEAL,cAAcd,MAAM,CAACiB,KAAK,GAAG;eACvBqgB;YACJ;gBACE/K,OAAO;uBAAIgL;oBAAYF;iBAAW;YACpC;SACD;IACH;IAEA,8DAA8D;IAC9D,IAAI,OAAO/d,OAAOke,oBAAoB,KAAK,YAAY;QACrD,MAAMvY,UAAU3F,OAAOke,oBAAoB,CAAC;YAC1CpN,cAActT,cAAcsT,YAAY;QAC1C;QACA,IAAInL,QAAQmL,YAAY,EAAE;YACxBtT,cAAcsT,YAAY,GAAGnL,QAAQmL,YAAY;QACnD;IACF;IAEA,SAASqN,YAAYtgB,IAA0C;QAC7D,IAAI,CAACA,MAAM;YACT,OAAO;QACT;QAEA,MAAMugB,YAAY;YAChB;YACA;YACA;YACA;YACA;SACD;QAED,IAAIvgB,gBAAgBuM,UAAUgU,UAAUlgB,IAAI,CAAC,CAACmgB,QAAUxgB,KAAK4G,IAAI,CAAC4Z,SAAS;YACzE,OAAO;QACT;QAEA,IAAI,OAAOxgB,SAAS,YAAY;YAC9B,IACEugB,UAAUlgB,IAAI,CAAC,CAACmgB;gBACd,IAAI;oBACF,IAAIxgB,KAAKwgB,QAAQ;wBACf,OAAO;oBACT;gBACF,EAAE,OAAM,CAAC;gBACT,OAAO;YACT,IACA;gBACA,OAAO;YACT;QACF;QAEA,IAAIrgB,MAAMC,OAAO,CAACJ,SAASA,KAAKK,IAAI,CAACigB,cAAc;YACjD,OAAO;QACT;QAEA,OAAO;IACT;IAEA,MAAMG,mBACJ9gB,EAAAA,yBAAAA,cAAcd,MAAM,sBAApBc,8BAAAA,uBAAsBG,KAAK,qBAA3BH,4BAA6BU,IAAI,CAC/B,CAACL,OAAcsgB,YAAYtgB,KAAK4G,IAAI,KAAK0Z,YAAYtgB,KAAK8G,OAAO,OAC9D;IAEP,IAAI2Z,kBAAkB;YAYhB9gB,8BAAAA,wBAWAA,yBAMAA,uCAAAA;QA5BJ,kCAAkC;QAClC,IAAI8E,yBAAyB;YAC3BxF,QAAQC,IAAI,CACVtG,OAAOC,KAAK,gBACVA,KACE,8FAEF;QAEN;QAEA,KAAI8G,yBAAAA,cAAcd,MAAM,sBAApBc,+BAAAA,uBAAsBG,KAAK,qBAA3BH,6BAA6BiF,MAAM,EAAE;YACvC,6BAA6B;YAC7BjF,cAAcd,MAAM,CAACiB,KAAK,CAACC,OAAO,CAAC,CAACO;gBAClC,IAAI,CAACA,KAAK,OAAOA,MAAM,UAAU;gBACjC,IAAIH,MAAMC,OAAO,CAACE,EAAE8U,KAAK,GAAG;oBAC1B9U,EAAE8U,KAAK,GAAG9U,EAAE8U,KAAK,CAAC/W,MAAM,CACtB,CAACqiB,IAAM,AAACA,CAAS,CAACC,OAAOC,GAAG,CAAC,qBAAqB,KAAK;gBAE3D;YACF;QACF;QACA,KAAIjhB,0BAAAA,cAAcoL,OAAO,qBAArBpL,wBAAuBiF,MAAM,EAAE;YACjC,gCAAgC;YAChCjF,cAAcoL,OAAO,GAAGpL,cAAcoL,OAAO,CAAC1M,MAAM,CAClD,CAACC,IAAM,AAACA,EAAUuiB,iBAAiB,KAAK;QAE5C;QACA,KAAIlhB,8BAAAA,cAAcyP,YAAY,sBAA1BzP,wCAAAA,4BAA4BsS,SAAS,qBAArCtS,sCAAuCiF,MAAM,EAAE;YACjD,uBAAuB;YACvBjF,cAAcyP,YAAY,CAAC6C,SAAS,GAClCtS,cAAcyP,YAAY,CAAC6C,SAAS,CAAC5T,MAAM,CACzC,CAACyiB,IAAM,AAACA,EAAUD,iBAAiB,KAAK;QAE9C;IACF;IAEA,yEAAyE;IACzE,IAAIze,OAAO4B,UAAU;QACnBtE,mBAAmBC,eAAe6J,eAAeC,KAAK;IACxD;IAEA,2CAA2C;IAC3C,4CAA4C;IAC5C,4CAA4C;IAC5C,0BAA0B;IAC1B,MAAMsX,gBAAqBphB,cAAc+K,KAAK;IAC9C,IAAI,OAAOqW,kBAAkB,aAAa;QACxC,MAAMC,eAAe;YACnB,MAAMtW,QACJ,OAAOqW,kBAAkB,aACrB,MAAMA,kBACNA;YACN,0CAA0C;YAC1C,IACEpW,iBACAxK,MAAMC,OAAO,CAACsK,KAAK,CAAC,UAAU,KAC9BA,KAAK,CAAC,UAAU,CAAC9F,MAAM,GAAG,GAC1B;gBACA,MAAMqc,eAAetW,aAAa,CAChClR,iCACD;gBACDiR,KAAK,CAACjR,iCAAiC,GAAG;uBACrCiR,KAAK,CAAC,UAAU;oBACnBuW;iBACD;YACH;YACA,OAAOvW,KAAK,CAAC,UAAU;YAEvB,KAAK,MAAMqB,QAAQvN,OAAOwN,IAAI,CAACtB,OAAQ;gBACrCA,KAAK,CAACqB,KAAK,GAAG5R,mBAAmB;oBAC/B+mB,OAAOxW,KAAK,CAACqB,KAAK;oBAClBjJ;oBACAiJ;oBACAjH;gBACF;YACF;YAEA,OAAO4F;QACT;QACA,sCAAsC;QACtC/K,cAAc+K,KAAK,GAAGsW;IACxB;IAEA,IAAI,CAAC5e,OAAO,OAAOzC,cAAc+K,KAAK,KAAK,YAAY;QACrD,6BAA6B;QAC7B/K,cAAc+K,KAAK,GAAG,MAAM/K,cAAc+K,KAAK;IACjD;IAEA,OAAO/K;AACT"}