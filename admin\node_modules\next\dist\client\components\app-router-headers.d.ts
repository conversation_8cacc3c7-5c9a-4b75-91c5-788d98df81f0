export declare const RSC_HEADER: "RSC";
export declare const ACTION_HEADER: "Next-Action";
export declare const NEXT_ROUTER_STATE_TREE_HEADER: "Next-Router-State-Tree";
export declare const NEXT_ROUTER_PREFETCH_HEADER: "Next-Router-Prefetch";
export declare const NEXT_ROUTER_SEGMENT_PREFETCH_HEADER: "Next-Router-Segment-Prefetch";
export declare const NEXT_HMR_REFRESH_HEADER: "Next-HMR-Refresh";
export declare const NEXT_HMR_REFRESH_HASH_COOKIE: "__next_hmr_refresh_hash__";
export declare const NEXT_URL: "Next-Url";
export declare const RSC_CONTENT_TYPE_HEADER: "text/x-component";
export declare const FLIGHT_HEADERS: readonly ["RSC", "Next-Router-State-Tree", "Next-Router-Prefetch", "Next-HMR-Refresh", "Next-Router-Segment-Prefetch"];
export declare const NEXT_RSC_UNION_QUERY: "_rsc";
export declare const NEXT_ROUTER_STALE_TIME_HEADER: "x-nextjs-stale-time";
export declare const NEXT_DID_POSTPONE_HEADER: "x-nextjs-postponed";
export declare const NEXT_REWRITTEN_PATH_HEADER: "x-nextjs-rewritten-path";
export declare const NEXT_REWRITTEN_QUERY_HEADER: "x-nextjs-rewritten-query";
export declare const NEXT_IS_PRERENDER_HEADER: "x-nextjs-prerender";
