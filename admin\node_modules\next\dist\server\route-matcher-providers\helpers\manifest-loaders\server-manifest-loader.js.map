{"version": 3, "sources": ["../../../../../src/server/route-matcher-providers/helpers/manifest-loaders/server-manifest-loader.ts"], "sourcesContent": ["import type { <PERSON><PERSON><PERSON>, <PERSON>ifestLoader } from './manifest-loader'\n\nexport class ServerManifestLoader implements ManifestLoader {\n  constructor(private readonly getter: (name: string) => Manifest | null) {}\n\n  public load(name: string): Manifest | null {\n    return this.getter(name)\n  }\n}\n"], "names": ["ServerManifestLoader", "constructor", "getter", "load", "name"], "mappings": ";;;;+BAEaA;;;eAAAA;;;AAAN,MAAMA;IACXC,YAAY,AAAiBC,MAAyC,CAAE;aAA3CA,SAAAA;IAA4C;IAElEC,KAAKC,IAAY,EAAmB;QACzC,OAAO,IAAI,CAACF,MAAM,CAACE;IACrB;AACF"}