{"version": 3, "sources": ["../../../../../src/client/components/router-reducer/reducers/server-action-reducer.ts"], "sourcesContent": ["import type {\n  ActionFlightResponse,\n  ActionResult,\n} from '../../../../server/app-render/types'\nimport { callServer } from '../../../app-call-server'\nimport { findSourceMapURL } from '../../../app-find-source-map-url'\nimport {\n  ACTION_HEADER,\n  NEXT_IS_PRERENDER_HEADER,\n  NEXT_ROUTER_STATE_TREE_HEADER,\n  NEXT_URL,\n  RSC_CONTENT_TYPE_HEADER,\n} from '../../app-router-headers'\n\n// // eslint-disable-next-line import/no-extraneous-dependencies\n// import { createFromFetch } from 'react-server-dom-webpack/client'\n// // eslint-disable-next-line import/no-extraneous-dependencies\n// import { encodeReply } from 'react-server-dom-webpack/client'\nconst { createFromFetch, createTemporaryReferenceSet, encodeReply } = (\n  !!process.env.NEXT_RUNTIME\n    ? // eslint-disable-next-line import/no-extraneous-dependencies\n      require('react-server-dom-webpack/client.edge')\n    : // eslint-disable-next-line import/no-extraneous-dependencies\n      require('react-server-dom-webpack/client')\n) as typeof import('react-server-dom-webpack/client')\n\nimport {\n  PrefetchKind,\n  type ReadonlyReducerState,\n  type ReducerState,\n  type ServerActionAction,\n  type ServerActionMutable,\n} from '../router-reducer-types'\nimport { assignLocation } from '../../../assign-location'\nimport { createHrefFromUrl } from '../create-href-from-url'\nimport { handleExternalUrl } from './navigate-reducer'\nimport { applyRouterStatePatchToTree } from '../apply-router-state-patch-to-tree'\nimport { isNavigatingToNewRootLayout } from '../is-navigating-to-new-root-layout'\nimport type { CacheNode } from '../../../../shared/lib/app-router-context.shared-runtime'\nimport { handleMutable } from '../handle-mutable'\nimport { fillLazyItemsTillLeafWithHead } from '../fill-lazy-items-till-leaf-with-head'\nimport { createEmptyCacheNode } from '../../app-router'\nimport { hasInterceptionRouteInCurrentTree } from './has-interception-route-in-current-tree'\nimport { handleSegmentMismatch } from '../handle-segment-mismatch'\nimport { refreshInactiveParallelSegments } from '../refetch-inactive-parallel-segments'\nimport {\n  normalizeFlightData,\n  prepareFlightRouterStateForRequest,\n  type NormalizedFlightData,\n} from '../../../flight-data-helpers'\nimport { getRedirectError } from '../../redirect'\nimport { RedirectType } from '../../redirect-error'\nimport { createSeededPrefetchCacheEntry } from '../prefetch-cache-utils'\nimport { removeBasePath } from '../../../remove-base-path'\nimport { hasBasePath } from '../../../has-base-path'\nimport {\n  extractInfoFromServerReferenceId,\n  omitUnusedArgs,\n} from '../../../../shared/lib/server-reference-info'\nimport { revalidateEntireCache } from '../../segment-cache'\n\ntype FetchServerActionResult = {\n  redirectLocation: URL | undefined\n  redirectType: RedirectType | undefined\n  actionResult?: ActionResult\n  actionFlightData?: NormalizedFlightData[] | string\n  isPrerender: boolean\n  revalidatedParts: {\n    tag: boolean\n    cookie: boolean\n    paths: string[]\n  }\n}\n\nasync function fetchServerAction(\n  state: ReadonlyReducerState,\n  nextUrl: ReadonlyReducerState['nextUrl'],\n  { actionId, actionArgs }: ServerActionAction\n): Promise<FetchServerActionResult> {\n  const temporaryReferences = createTemporaryReferenceSet()\n  const info = extractInfoFromServerReferenceId(actionId)\n\n  // TODO: Currently, we're only omitting unused args for the experimental \"use\n  // cache\" functions. Once the server reference info byte feature is stable, we\n  // should apply this to server actions as well.\n  const usedArgs =\n    info.type === 'use-cache' ? omitUnusedArgs(actionArgs, info) : actionArgs\n\n  const body = await encodeReply(usedArgs, { temporaryReferences })\n\n  const res = await fetch('', {\n    method: 'POST',\n    headers: {\n      Accept: RSC_CONTENT_TYPE_HEADER,\n      [ACTION_HEADER]: actionId,\n      [NEXT_ROUTER_STATE_TREE_HEADER]: prepareFlightRouterStateForRequest(\n        state.tree\n      ),\n      ...(process.env.NEXT_DEPLOYMENT_ID\n        ? {\n            'x-deployment-id': process.env.NEXT_DEPLOYMENT_ID,\n          }\n        : {}),\n      ...(nextUrl\n        ? {\n            [NEXT_URL]: nextUrl,\n          }\n        : {}),\n    },\n    body,\n  })\n\n  const redirectHeader = res.headers.get('x-action-redirect')\n  const [location, _redirectType] = redirectHeader?.split(';') || []\n  let redirectType: RedirectType | undefined\n  switch (_redirectType) {\n    case 'push':\n      redirectType = RedirectType.push\n      break\n    case 'replace':\n      redirectType = RedirectType.replace\n      break\n    default:\n      redirectType = undefined\n  }\n\n  const isPrerender = !!res.headers.get(NEXT_IS_PRERENDER_HEADER)\n  let revalidatedParts: FetchServerActionResult['revalidatedParts']\n  try {\n    const revalidatedHeader = JSON.parse(\n      res.headers.get('x-action-revalidated') || '[[],0,0]'\n    )\n    revalidatedParts = {\n      paths: revalidatedHeader[0] || [],\n      tag: !!revalidatedHeader[1],\n      cookie: revalidatedHeader[2],\n    }\n  } catch (e) {\n    revalidatedParts = {\n      paths: [],\n      tag: false,\n      cookie: false,\n    }\n  }\n\n  const redirectLocation = location\n    ? assignLocation(\n        location,\n        new URL(state.canonicalUrl, window.location.href)\n      )\n    : undefined\n\n  const contentType = res.headers.get('content-type')\n\n  if (contentType?.startsWith(RSC_CONTENT_TYPE_HEADER)) {\n    const response: ActionFlightResponse = await createFromFetch(\n      Promise.resolve(res),\n      { callServer, findSourceMapURL, temporaryReferences }\n    )\n\n    if (location) {\n      // if it was a redirection, then result is just a regular RSC payload\n      return {\n        actionFlightData: normalizeFlightData(response.f),\n        redirectLocation,\n        redirectType,\n        revalidatedParts,\n        isPrerender,\n      }\n    }\n\n    return {\n      actionResult: response.a,\n      actionFlightData: normalizeFlightData(response.f),\n      redirectLocation,\n      redirectType,\n      revalidatedParts,\n      isPrerender,\n    }\n  }\n\n  // Handle invalid server action responses\n  if (res.status >= 400) {\n    // The server can respond with a text/plain error message, but we'll fallback to something generic\n    // if there isn't one.\n    const error =\n      contentType === 'text/plain'\n        ? await res.text()\n        : 'An unexpected response was received from the server.'\n\n    throw new Error(error)\n  }\n\n  return {\n    redirectLocation,\n    redirectType,\n    revalidatedParts,\n    isPrerender,\n  }\n}\n\n/*\n * This reducer is responsible for calling the server action and processing any side-effects from the server action.\n * It does not mutate the state by itself but rather delegates to other reducers to do the actual mutation.\n */\nexport function serverActionReducer(\n  state: ReadonlyReducerState,\n  action: ServerActionAction\n): ReducerState {\n  const { resolve, reject } = action\n  const mutable: ServerActionMutable = {}\n\n  let currentTree = state.tree\n\n  mutable.preserveCustomHistoryState = false\n\n  // only pass along the `nextUrl` param (used for interception routes) if the current route was intercepted.\n  // If the route has been intercepted, the action should be as well.\n  // Otherwise the server action might be intercepted with the wrong action id\n  // (ie, one that corresponds with the intercepted route)\n  const nextUrl =\n    state.nextUrl && hasInterceptionRouteInCurrentTree(state.tree)\n      ? state.nextUrl\n      : null\n\n  const navigatedAt = Date.now()\n\n  return fetchServerAction(state, nextUrl, action).then(\n    async ({\n      actionResult,\n      actionFlightData: flightData,\n      redirectLocation,\n      redirectType,\n      isPrerender,\n      revalidatedParts,\n    }) => {\n      let redirectHref: string | undefined\n\n      // honor the redirect type instead of defaulting to push in case of server actions.\n      if (redirectLocation) {\n        if (redirectType === RedirectType.replace) {\n          state.pushRef.pendingPush = false\n          mutable.pendingPush = false\n        } else {\n          state.pushRef.pendingPush = true\n          mutable.pendingPush = true\n        }\n\n        redirectHref = createHrefFromUrl(redirectLocation, false)\n        mutable.canonicalUrl = redirectHref\n      }\n\n      if (!flightData) {\n        resolve(actionResult)\n\n        // If there is a redirect but no flight data we need to do a mpaNavigation.\n        if (redirectLocation) {\n          return handleExternalUrl(\n            state,\n            mutable,\n            redirectLocation.href,\n            state.pushRef.pendingPush\n          )\n        }\n        return state\n      }\n\n      if (typeof flightData === 'string') {\n        // Handle case when navigating to page in `pages` from `app`\n        resolve(actionResult)\n\n        return handleExternalUrl(\n          state,\n          mutable,\n          flightData,\n          state.pushRef.pendingPush\n        )\n      }\n\n      const actionRevalidated =\n        revalidatedParts.paths.length > 0 ||\n        revalidatedParts.tag ||\n        revalidatedParts.cookie\n\n      for (const normalizedFlightData of flightData) {\n        const {\n          tree: treePatch,\n          seedData: cacheNodeSeedData,\n          head,\n          isRootRender,\n        } = normalizedFlightData\n\n        if (!isRootRender) {\n          // TODO-APP: handle this case better\n          console.log('SERVER ACTION APPLY FAILED')\n          resolve(actionResult)\n\n          return state\n        }\n\n        // Given the path can only have two items the items are only the router state and rsc for the root.\n        const newTree = applyRouterStatePatchToTree(\n          // TODO-APP: remove ''\n          [''],\n          currentTree,\n          treePatch,\n          redirectHref ? redirectHref : state.canonicalUrl\n        )\n\n        if (newTree === null) {\n          resolve(actionResult)\n\n          return handleSegmentMismatch(state, action, treePatch)\n        }\n\n        if (isNavigatingToNewRootLayout(currentTree, newTree)) {\n          resolve(actionResult)\n\n          return handleExternalUrl(\n            state,\n            mutable,\n            redirectHref || state.canonicalUrl,\n            state.pushRef.pendingPush\n          )\n        }\n\n        // The server sent back RSC data for the server action, so we need to apply it to the cache.\n        if (cacheNodeSeedData !== null) {\n          const rsc = cacheNodeSeedData[1]\n          const cache: CacheNode = createEmptyCacheNode()\n          cache.rsc = rsc\n          cache.prefetchRsc = null\n          cache.loading = cacheNodeSeedData[3]\n          fillLazyItemsTillLeafWithHead(\n            navigatedAt,\n            cache,\n            // Existing cache is not passed in as server actions have to invalidate the entire cache.\n            undefined,\n            treePatch,\n            cacheNodeSeedData,\n            head,\n            undefined\n          )\n\n          mutable.cache = cache\n          if (process.env.__NEXT_CLIENT_SEGMENT_CACHE) {\n            revalidateEntireCache(state.nextUrl, newTree)\n          } else {\n            mutable.prefetchCache = new Map()\n          }\n          if (actionRevalidated) {\n            await refreshInactiveParallelSegments({\n              navigatedAt,\n              state,\n              updatedTree: newTree,\n              updatedCache: cache,\n              includeNextUrl: Boolean(nextUrl),\n              canonicalUrl: mutable.canonicalUrl || state.canonicalUrl,\n            })\n          }\n        }\n\n        mutable.patchedTree = newTree\n        currentTree = newTree\n      }\n\n      if (redirectLocation && redirectHref) {\n        if (!process.env.__NEXT_CLIENT_SEGMENT_CACHE && !actionRevalidated) {\n          // Because the RedirectBoundary will trigger a navigation, we need to seed the prefetch cache\n          // with the FlightData that we got from the server action for the target page, so that it's\n          // available when the page is navigated to and doesn't need to be re-fetched.\n          // We only do this if the server action didn't revalidate any data, as in that case the\n          // client cache will be cleared and the data will be re-fetched anyway.\n          // NOTE: We don't do this in the Segment Cache implementation.\n          // Dynamic data should never be placed into the cache, unless it's\n          // \"converted\" to static data using <Link prefetch={true}>. What we\n          // do instead is re-prefetch links and forms whenever the cache is\n          // invalidated.\n          createSeededPrefetchCacheEntry({\n            url: redirectLocation,\n            data: {\n              flightData,\n              canonicalUrl: undefined,\n              couldBeIntercepted: false,\n              prerendered: false,\n              postponed: false,\n              // TODO: We should be able to set this if the server action\n              // returned a fully static response.\n              staleTime: -1,\n            },\n            tree: state.tree,\n            prefetchCache: state.prefetchCache,\n            nextUrl: state.nextUrl,\n            kind: isPrerender ? PrefetchKind.FULL : PrefetchKind.AUTO,\n          })\n          mutable.prefetchCache = state.prefetchCache\n        }\n\n        // If the action triggered a redirect, the action promise will be rejected with\n        // a redirect so that it's handled by RedirectBoundary as we won't have a valid\n        // action result to resolve the promise with. This will effectively reset the state of\n        // the component that called the action as the error boundary will remount the tree.\n        // The status code doesn't matter here as the action handler will have already sent\n        // a response with the correct status code.\n        reject(\n          getRedirectError(\n            hasBasePath(redirectHref)\n              ? removeBasePath(redirectHref)\n              : redirectHref,\n            redirectType || RedirectType.push\n          )\n        )\n      } else {\n        resolve(actionResult)\n      }\n\n      return handleMutable(state, mutable)\n    },\n    (e: any) => {\n      // When the server action is rejected we don't update the state and instead call the reject handler of the promise.\n      reject(e)\n\n      return state\n    }\n  )\n}\n"], "names": ["callServer", "findSourceMapURL", "ACTION_HEADER", "NEXT_IS_PRERENDER_HEADER", "NEXT_ROUTER_STATE_TREE_HEADER", "NEXT_URL", "RSC_CONTENT_TYPE_HEADER", "createFromFetch", "createTemporaryReferenceSet", "encodeReply", "process", "env", "NEXT_RUNTIME", "require", "PrefetchKind", "assignLocation", "createHrefFromUrl", "handleExternalUrl", "applyRouterStatePatchToTree", "isNavigatingToNewRootLayout", "handleMutable", "fillLazyItemsTillLeafWithHead", "createEmptyCacheNode", "hasInterceptionRouteInCurrentTree", "handleSegmentMismatch", "refreshInactiveParallelSegments", "normalizeFlightData", "prepareFlightRouterStateForRequest", "getRedirectError", "RedirectType", "createSeededPrefetchCacheEntry", "removeBasePath", "has<PERSON>ase<PERSON><PERSON>", "extractInfoFromServerReferenceId", "omit<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "revalidateEntireCache", "fetchServerAction", "state", "nextUrl", "actionId", "actionArgs", "temporaryReferences", "info", "usedArgs", "type", "body", "res", "fetch", "method", "headers", "Accept", "tree", "NEXT_DEPLOYMENT_ID", "redirectHeader", "get", "location", "_redirectType", "split", "redirectType", "push", "replace", "undefined", "<PERSON><PERSON><PERSON><PERSON>", "revalidatedParts", "revalidatedHeader", "JSON", "parse", "paths", "tag", "cookie", "e", "redirectLocation", "URL", "canonicalUrl", "window", "href", "contentType", "startsWith", "response", "Promise", "resolve", "actionFlightData", "f", "actionResult", "a", "status", "error", "text", "Error", "serverActionReducer", "action", "reject", "mutable", "currentTree", "preserveCustomHistoryState", "navigatedAt", "Date", "now", "then", "flightData", "redirectHref", "pushRef", "pendingPush", "actionRevalidated", "length", "normalizedFlightData", "treePatch", "seedData", "cacheNodeSeedData", "head", "isRootRender", "console", "log", "newTree", "rsc", "cache", "prefetchRsc", "loading", "__NEXT_CLIENT_SEGMENT_CACHE", "prefetchCache", "Map", "updatedTree", "updatedCache", "includeNextUrl", "Boolean", "patchedTree", "url", "data", "couldBeIntercepted", "prerendered", "postponed", "staleTime", "kind", "FULL", "AUTO"], "mappings": "AAIA,SAASA,UAAU,QAAQ,2BAA0B;AACrD,SAASC,gBAAgB,QAAQ,mCAAkC;AACnE,SACEC,aAAa,EACbC,wBAAwB,EACxBC,6BAA6B,EAC7BC,QAAQ,EACRC,uBAAuB,QAClB,2BAA0B;AAEjC,gEAAgE;AAChE,oEAAoE;AACpE,gEAAgE;AAChE,gEAAgE;AAChE,MAAM,EAAEC,eAAe,EAAEC,2BAA2B,EAAEC,WAAW,EAAE,GACjE,CAAC,CAACC,QAAQC,GAAG,CAACC,YAAY,GAEtBC,QAAQ,0CAERA,QAAQ;AAGd,SACEC,YAAY,QAKP,0BAAyB;AAChC,SAASC,cAAc,QAAQ,2BAA0B;AACzD,SAASC,iBAAiB,QAAQ,0BAAyB;AAC3D,SAASC,iBAAiB,QAAQ,qBAAoB;AACtD,SAASC,2BAA2B,QAAQ,sCAAqC;AACjF,SAASC,2BAA2B,QAAQ,sCAAqC;AAEjF,SAASC,aAAa,QAAQ,oBAAmB;AACjD,SAASC,6BAA6B,QAAQ,yCAAwC;AACtF,SAASC,oBAAoB,QAAQ,mBAAkB;AACvD,SAASC,iCAAiC,QAAQ,2CAA0C;AAC5F,SAASC,qBAAqB,QAAQ,6BAA4B;AAClE,SAASC,+BAA+B,QAAQ,wCAAuC;AACvF,SACEC,mBAAmB,EACnBC,kCAAkC,QAE7B,+BAA8B;AACrC,SAASC,gBAAgB,QAAQ,iBAAgB;AACjD,SAASC,YAAY,QAAQ,uBAAsB;AACnD,SAASC,8BAA8B,QAAQ,0BAAyB;AACxE,SAASC,cAAc,QAAQ,4BAA2B;AAC1D,SAASC,WAAW,QAAQ,yBAAwB;AACpD,SACEC,gCAAgC,EAChCC,cAAc,QACT,+CAA8C;AACrD,SAASC,qBAAqB,QAAQ,sBAAqB;AAe3D,eAAeC,kBACbC,KAA2B,EAC3BC,OAAwC,EACxC,KAA4C;IAA5C,IAAA,EAAEC,QAAQ,EAAEC,UAAU,EAAsB,GAA5C;IAEA,MAAMC,sBAAsBjC;IAC5B,MAAMkC,OAAOT,iCAAiCM;IAE9C,6EAA6E;IAC7E,8EAA8E;IAC9E,+CAA+C;IAC/C,MAAMI,WACJD,KAAKE,IAAI,KAAK,cAAcV,eAAeM,YAAYE,QAAQF;IAEjE,MAAMK,OAAO,MAAMpC,YAAYkC,UAAU;QAAEF;IAAoB;IAE/D,MAAMK,MAAM,MAAMC,MAAM,IAAI;QAC1BC,QAAQ;QACRC,SAAS;YACPC,QAAQ5C;YACR,CAACJ,cAAc,EAAEqC;YACjB,CAACnC,8BAA8B,EAAEuB,mCAC/BU,MAAMc,IAAI;YAEZ,GAAIzC,QAAQC,GAAG,CAACyC,kBAAkB,GAC9B;gBACE,mBAAmB1C,QAAQC,GAAG,CAACyC,kBAAkB;YACnD,IACA,CAAC,CAAC;YACN,GAAId,UACA;gBACE,CAACjC,SAAS,EAAEiC;YACd,IACA,CAAC,CAAC;QACR;QACAO;IACF;IAEA,MAAMQ,iBAAiBP,IAAIG,OAAO,CAACK,GAAG,CAAC;IACvC,MAAM,CAACC,UAAUC,cAAc,GAAGH,CAAAA,kCAAAA,eAAgBI,KAAK,CAAC,SAAQ,EAAE;IAClE,IAAIC;IACJ,OAAQF;QACN,KAAK;YACHE,eAAe7B,aAAa8B,IAAI;YAChC;QACF,KAAK;YACHD,eAAe7B,aAAa+B,OAAO;YACnC;QACF;YACEF,eAAeG;IACnB;IAEA,MAAMC,cAAc,CAAC,CAAChB,IAAIG,OAAO,CAACK,GAAG,CAACnD;IACtC,IAAI4D;IACJ,IAAI;QACF,MAAMC,oBAAoBC,KAAKC,KAAK,CAClCpB,IAAIG,OAAO,CAACK,GAAG,CAAC,2BAA2B;QAE7CS,mBAAmB;YACjBI,OAAOH,iBAAiB,CAAC,EAAE,IAAI,EAAE;YACjCI,KAAK,CAAC,CAACJ,iBAAiB,CAAC,EAAE;YAC3BK,QAAQL,iBAAiB,CAAC,EAAE;QAC9B;IACF,EAAE,OAAOM,GAAG;QACVP,mBAAmB;YACjBI,OAAO,EAAE;YACTC,KAAK;YACLC,QAAQ;QACV;IACF;IAEA,MAAME,mBAAmBhB,WACrBxC,eACEwC,UACA,IAAIiB,IAAInC,MAAMoC,YAAY,EAAEC,OAAOnB,QAAQ,CAACoB,IAAI,KAElDd;IAEJ,MAAMe,cAAc9B,IAAIG,OAAO,CAACK,GAAG,CAAC;IAEpC,IAAIsB,+BAAAA,YAAaC,UAAU,CAACvE,0BAA0B;QACpD,MAAMwE,WAAiC,MAAMvE,gBAC3CwE,QAAQC,OAAO,CAAClC,MAChB;YAAE9C;YAAYC;YAAkBwC;QAAoB;QAGtD,IAAIc,UAAU;YACZ,qEAAqE;YACrE,OAAO;gBACL0B,kBAAkBvD,oBAAoBoD,SAASI,CAAC;gBAChDX;gBACAb;gBACAK;gBACAD;YACF;QACF;QAEA,OAAO;YACLqB,cAAcL,SAASM,CAAC;YACxBH,kBAAkBvD,oBAAoBoD,SAASI,CAAC;YAChDX;YACAb;YACAK;YACAD;QACF;IACF;IAEA,yCAAyC;IACzC,IAAIhB,IAAIuC,MAAM,IAAI,KAAK;QACrB,kGAAkG;QAClG,sBAAsB;QACtB,MAAMC,QACJV,gBAAgB,eACZ,MAAM9B,IAAIyC,IAAI,KACd;QAEN,MAAM,qBAAgB,CAAhB,IAAIC,MAAMF,QAAV,qBAAA;mBAAA;wBAAA;0BAAA;QAAe;IACvB;IAEA,OAAO;QACLf;QACAb;QACAK;QACAD;IACF;AACF;AAEA;;;CAGC,GACD,OAAO,SAAS2B,oBACdpD,KAA2B,EAC3BqD,MAA0B;IAE1B,MAAM,EAAEV,OAAO,EAAEW,MAAM,EAAE,GAAGD;IAC5B,MAAME,UAA+B,CAAC;IAEtC,IAAIC,cAAcxD,MAAMc,IAAI;IAE5ByC,QAAQE,0BAA0B,GAAG;IAErC,2GAA2G;IAC3G,mEAAmE;IACnE,4EAA4E;IAC5E,wDAAwD;IACxD,MAAMxD,UACJD,MAAMC,OAAO,IAAIf,kCAAkCc,MAAMc,IAAI,IACzDd,MAAMC,OAAO,GACb;IAEN,MAAMyD,cAAcC,KAAKC,GAAG;IAE5B,OAAO7D,kBAAkBC,OAAOC,SAASoD,QAAQQ,IAAI,CACnD;YAAO,EACLf,YAAY,EACZF,kBAAkBkB,UAAU,EAC5B5B,gBAAgB,EAChBb,YAAY,EACZI,WAAW,EACXC,gBAAgB,EACjB;QACC,IAAIqC;QAEJ,mFAAmF;QACnF,IAAI7B,kBAAkB;YACpB,IAAIb,iBAAiB7B,aAAa+B,OAAO,EAAE;gBACzCvB,MAAMgE,OAAO,CAACC,WAAW,GAAG;gBAC5BV,QAAQU,WAAW,GAAG;YACxB,OAAO;gBACLjE,MAAMgE,OAAO,CAACC,WAAW,GAAG;gBAC5BV,QAAQU,WAAW,GAAG;YACxB;YAEAF,eAAepF,kBAAkBuD,kBAAkB;YACnDqB,QAAQnB,YAAY,GAAG2B;QACzB;QAEA,IAAI,CAACD,YAAY;YACfnB,QAAQG;YAER,2EAA2E;YAC3E,IAAIZ,kBAAkB;gBACpB,OAAOtD,kBACLoB,OACAuD,SACArB,iBAAiBI,IAAI,EACrBtC,MAAMgE,OAAO,CAACC,WAAW;YAE7B;YACA,OAAOjE;QACT;QAEA,IAAI,OAAO8D,eAAe,UAAU;YAClC,4DAA4D;YAC5DnB,QAAQG;YAER,OAAOlE,kBACLoB,OACAuD,SACAO,YACA9D,MAAMgE,OAAO,CAACC,WAAW;QAE7B;QAEA,MAAMC,oBACJxC,iBAAiBI,KAAK,CAACqC,MAAM,GAAG,KAChCzC,iBAAiBK,GAAG,IACpBL,iBAAiBM,MAAM;QAEzB,KAAK,MAAMoC,wBAAwBN,WAAY;YAC7C,MAAM,EACJhD,MAAMuD,SAAS,EACfC,UAAUC,iBAAiB,EAC3BC,IAAI,EACJC,YAAY,EACb,GAAGL;YAEJ,IAAI,CAACK,cAAc;gBACjB,oCAAoC;gBACpCC,QAAQC,GAAG,CAAC;gBACZhC,QAAQG;gBAER,OAAO9C;YACT;YAEA,mGAAmG;YACnG,MAAM4E,UAAU/F,4BACd,sBAAsB;YACtB;gBAAC;aAAG,EACJ2E,aACAa,WACAN,eAAeA,eAAe/D,MAAMoC,YAAY;YAGlD,IAAIwC,YAAY,MAAM;gBACpBjC,QAAQG;gBAER,OAAO3D,sBAAsBa,OAAOqD,QAAQgB;YAC9C;YAEA,IAAIvF,4BAA4B0E,aAAaoB,UAAU;gBACrDjC,QAAQG;gBAER,OAAOlE,kBACLoB,OACAuD,SACAQ,gBAAgB/D,MAAMoC,YAAY,EAClCpC,MAAMgE,OAAO,CAACC,WAAW;YAE7B;YAEA,4FAA4F;YAC5F,IAAIM,sBAAsB,MAAM;gBAC9B,MAAMM,MAAMN,iBAAiB,CAAC,EAAE;gBAChC,MAAMO,QAAmB7F;gBACzB6F,MAAMD,GAAG,GAAGA;gBACZC,MAAMC,WAAW,GAAG;gBACpBD,MAAME,OAAO,GAAGT,iBAAiB,CAAC,EAAE;gBACpCvF,8BACE0E,aACAoB,OACA,yFAAyF;gBACzFtD,WACA6C,WACAE,mBACAC,MACAhD;gBAGF+B,QAAQuB,KAAK,GAAGA;gBAChB,IAAIzG,QAAQC,GAAG,CAAC2G,2BAA2B,EAAE;oBAC3CnF,sBAAsBE,MAAMC,OAAO,EAAE2E;gBACvC,OAAO;oBACLrB,QAAQ2B,aAAa,GAAG,IAAIC;gBAC9B;gBACA,IAAIjB,mBAAmB;oBACrB,MAAM9E,gCAAgC;wBACpCsE;wBACA1D;wBACAoF,aAAaR;wBACbS,cAAcP;wBACdQ,gBAAgBC,QAAQtF;wBACxBmC,cAAcmB,QAAQnB,YAAY,IAAIpC,MAAMoC,YAAY;oBAC1D;gBACF;YACF;YAEAmB,QAAQiC,WAAW,GAAGZ;YACtBpB,cAAcoB;QAChB;QAEA,IAAI1C,oBAAoB6B,cAAc;YACpC,IAAI,CAAC1F,QAAQC,GAAG,CAAC2G,2BAA2B,IAAI,CAACf,mBAAmB;gBAClE,6FAA6F;gBAC7F,2FAA2F;gBAC3F,6EAA6E;gBAC7E,uFAAuF;gBACvF,uEAAuE;gBACvE,8DAA8D;gBAC9D,kEAAkE;gBAClE,mEAAmE;gBACnE,kEAAkE;gBAClE,eAAe;gBACfzE,+BAA+B;oBAC7BgG,KAAKvD;oBACLwD,MAAM;wBACJ5B;wBACA1B,cAAcZ;wBACdmE,oBAAoB;wBACpBC,aAAa;wBACbC,WAAW;wBACX,2DAA2D;wBAC3D,oCAAoC;wBACpCC,WAAW,CAAC;oBACd;oBACAhF,MAAMd,MAAMc,IAAI;oBAChBoE,eAAelF,MAAMkF,aAAa;oBAClCjF,SAASD,MAAMC,OAAO;oBACtB8F,MAAMtE,cAAchD,aAAauH,IAAI,GAAGvH,aAAawH,IAAI;gBAC3D;gBACA1C,QAAQ2B,aAAa,GAAGlF,MAAMkF,aAAa;YAC7C;YAEA,+EAA+E;YAC/E,+EAA+E;YAC/E,sFAAsF;YACtF,oFAAoF;YACpF,mFAAmF;YACnF,2CAA2C;YAC3C5B,OACE/D,iBACEI,YAAYoE,gBACRrE,eAAeqE,gBACfA,cACJ1C,gBAAgB7B,aAAa8B,IAAI;QAGvC,OAAO;YACLqB,QAAQG;QACV;QAEA,OAAO/D,cAAciB,OAAOuD;IAC9B,GACA,CAACtB;QACC,mHAAmH;QACnHqB,OAAOrB;QAEP,OAAOjC;IACT;AAEJ"}