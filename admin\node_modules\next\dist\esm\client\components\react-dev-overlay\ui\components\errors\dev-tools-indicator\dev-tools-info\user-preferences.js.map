{"version": 3, "sources": ["../../../../../../../../../src/client/components/react-dev-overlay/ui/components/errors/dev-tools-indicator/dev-tools-info/user-preferences.tsx"], "sourcesContent": ["import { useState, type HTMLProps } from 'react'\nimport { css } from '../../../../../utils/css'\nimport EyeIcon from '../../../../icons/eye-icon'\nimport { STORAGE_KEY_POSITION, STORAGE_KEY_THEME } from '../../../../../shared'\nimport LightIcon from '../../../../icons/light-icon'\nimport DarkIcon from '../../../../icons/dark-icon'\nimport SystemIcon from '../../../../icons/system-icon'\nimport type { DevToolsInfoPropsCore } from './dev-tools-info'\nimport { DevToolsInfo } from './dev-tools-info'\nimport {\n  getInitialTheme,\n  NEXT_DEV_TOOLS_SCALE,\n  type DevToolsIndicatorPosition,\n  type DevToolsScale,\n} from './preferences'\n\nexport function UserPreferences({\n  setPosition,\n  position,\n  hide,\n  scale,\n  setScale,\n  ...props\n}: {\n  setPosition: (position: DevToolsIndicatorPosition) => void\n  position: DevToolsIndicatorPosition\n  scale: DevToolsScale\n  setScale: (value: DevToolsScale) => void\n  hide: () => void\n} & DevToolsInfoPropsCore &\n  Omit<HTMLProps<HTMLDivElement>, 'size'>) {\n  // derive initial theme from system preference\n  const [theme, setTheme] = useState(getInitialTheme())\n\n  const handleThemeChange = (e: React.ChangeEvent<HTMLSelectElement>) => {\n    const portal = document.querySelector('nextjs-portal')\n    if (!portal) return\n\n    setTheme(e.target.value)\n\n    if (e.target.value === 'system') {\n      portal.classList.remove('dark')\n      portal.classList.remove('light')\n      localStorage.removeItem(STORAGE_KEY_THEME)\n      return\n    }\n\n    if (e.target.value === 'dark') {\n      portal.classList.add('dark')\n      portal.classList.remove('light')\n      localStorage.setItem(STORAGE_KEY_THEME, 'dark')\n    } else {\n      portal.classList.remove('dark')\n      portal.classList.add('light')\n      localStorage.setItem(STORAGE_KEY_THEME, 'light')\n    }\n  }\n\n  function handlePositionChange(e: React.ChangeEvent<HTMLSelectElement>) {\n    setPosition(e.target.value as DevToolsIndicatorPosition)\n    localStorage.setItem(STORAGE_KEY_POSITION, e.target.value)\n  }\n\n  function handleSizeChange({ target }: React.ChangeEvent<HTMLSelectElement>) {\n    const value = Number(target.value) as DevToolsScale\n    setScale(value)\n  }\n\n  return (\n    <DevToolsInfo title=\"Preferences\" {...props}>\n      <div className=\"preferences-container\">\n        <div className=\"preference-section\">\n          <div className=\"preference-header\">\n            <label htmlFor=\"theme\">Theme</label>\n            <p className=\"preference-description\">\n              Select your theme preference.\n            </p>\n          </div>\n          <Select\n            id=\"theme\"\n            name=\"theme\"\n            prefix={<ThemeIcon theme={theme as 'dark' | 'light' | 'system'} />}\n            value={theme}\n            onChange={handleThemeChange}\n          >\n            <option value=\"system\">System</option>\n            <option value=\"light\">Light</option>\n            <option value=\"dark\">Dark</option>\n          </Select>\n        </div>\n\n        <div className=\"preference-section\">\n          <div className=\"preference-header\">\n            <label htmlFor=\"position\">Position</label>\n            <p className=\"preference-description\">\n              Adjust the placement of your dev tools.\n            </p>\n          </div>\n          <Select\n            id=\"position\"\n            name=\"position\"\n            value={position}\n            onChange={handlePositionChange}\n          >\n            <option value=\"bottom-left\">Bottom Left</option>\n            <option value=\"bottom-right\">Bottom Right</option>\n            <option value=\"top-left\">Top Left</option>\n            <option value=\"top-right\">Top Right</option>\n          </Select>\n        </div>\n\n        <div className=\"preference-section\">\n          <div className=\"preference-header\">\n            <label htmlFor=\"size\">Size</label>\n            <p className=\"preference-description\">\n              Adjust the size of your dev tools.\n            </p>\n          </div>\n          <Select\n            id=\"size\"\n            name=\"size\"\n            value={scale}\n            onChange={handleSizeChange}\n          >\n            {Object.entries(NEXT_DEV_TOOLS_SCALE).map(([key, value]) => {\n              return (\n                <option value={value} key={key}>\n                  {key}\n                </option>\n              )\n            })}\n          </Select>\n        </div>\n\n        <div className=\"preference-section\">\n          <div className=\"preference-header\">\n            <label id=\"hide-dev-tools\">Hide Dev Tools for this session</label>\n            <p className=\"preference-description\">\n              Hide Dev Tools until you restart your dev server, or 1 day.\n            </p>\n          </div>\n          <div className=\"preference-control\">\n            <button\n              aria-describedby=\"hide-dev-tools\"\n              name=\"hide-dev-tools\"\n              data-hide-dev-tools\n              className=\"action-button\"\n              onClick={hide}\n            >\n              <EyeIcon />\n              <span>Hide</span>\n            </button>\n          </div>\n        </div>\n\n        <div className=\"preference-section\">\n          <div className=\"preference-header\">\n            <label>Disable Dev Tools for this project</label>\n            <p className=\"preference-description\">\n              To disable this UI completely, set{' '}\n              <code className=\"dev-tools-info-code\">devIndicators: false</code>{' '}\n              in your <code className=\"dev-tools-info-code\">next.config</code>{' '}\n              file.\n            </p>\n          </div>\n        </div>\n      </div>\n    </DevToolsInfo>\n  )\n}\n\nfunction Select({\n  children,\n  prefix,\n  ...props\n}: {\n  prefix?: React.ReactNode\n} & Omit<React.HTMLProps<HTMLSelectElement>, 'prefix'>) {\n  return (\n    <div className=\"select-button\">\n      {prefix}\n      <select {...props}>{children}</select>\n      <ChevronDownIcon />\n    </div>\n  )\n}\n\nfunction ThemeIcon({ theme }: { theme: 'dark' | 'light' | 'system' }) {\n  switch (theme) {\n    case 'system':\n      return <SystemIcon />\n    case 'dark':\n      return <DarkIcon />\n    case 'light':\n      return <LightIcon />\n    default:\n      return null\n  }\n}\n\nexport const DEV_TOOLS_INFO_USER_PREFERENCES_STYLES = css`\n  .preferences-container {\n    padding: 8px 6px;\n    width: 100%;\n  }\n\n  @media (min-width: 576px) {\n    .preferences-container {\n      width: 480px;\n    }\n  }\n\n  .preference-section:first-child {\n    padding-top: 0;\n  }\n\n  .preference-section {\n    padding: 12px 0;\n    border-bottom: 1px solid var(--color-gray-400);\n    display: flex;\n    justify-content: space-between;\n    align-items: center;\n    gap: 24px;\n  }\n\n  .preference-section:last-child {\n    border-bottom: none;\n  }\n\n  .preference-header {\n    margin-bottom: 0;\n    flex: 1;\n  }\n\n  .preference-header label {\n    font-size: var(--size-14);\n    font-weight: 500;\n    color: var(--color-gray-1000);\n    margin: 0;\n  }\n\n  .preference-description {\n    color: var(--color-gray-900);\n    font-size: var(--size-14);\n    margin: 0;\n  }\n\n  .select-button,\n  .action-button {\n    display: flex;\n    align-items: center;\n    gap: 8px;\n    background: var(--color-background-100);\n    border: 1px solid var(--color-gray-400);\n    border-radius: var(--rounded-lg);\n    font-weight: 400;\n    font-size: var(--size-14);\n    color: var(--color-gray-1000);\n    padding: 6px 8px;\n\n    &:hover {\n      background: var(--color-gray-100);\n    }\n  }\n\n  .select-button {\n    &:focus-within {\n      outline: var(--focus-ring);\n    }\n\n    select {\n      all: unset;\n    }\n  }\n\n  :global(.icon) {\n    width: 18px;\n    height: 18px;\n    color: #666;\n  }\n`\n\nfunction ChevronDownIcon() {\n  return (\n    <svg width=\"16\" height=\"16\" viewBox=\"0 0 16 16\" aria-hidden>\n      <path\n        fillRule=\"evenodd\"\n        clipRule=\"evenodd\"\n        d=\"M14.0607 5.49999L13.5303 6.03032L8.7071 10.8535C8.31658 11.2441 7.68341 11.2441 7.29289 10.8535L2.46966 6.03032L1.93933 5.49999L2.99999 4.43933L3.53032 4.96966L7.99999 9.43933L12.4697 4.96966L13 4.43933L14.0607 5.49999Z\"\n        fill=\"currentColor\"\n      />\n    </svg>\n  )\n}\n"], "names": ["useState", "css", "EyeIcon", "STORAGE_KEY_POSITION", "STORAGE_KEY_THEME", "LightIcon", "DarkIcon", "SystemIcon", "DevToolsInfo", "getInitialTheme", "NEXT_DEV_TOOLS_SCALE", "UserPreferences", "setPosition", "position", "hide", "scale", "setScale", "props", "theme", "setTheme", "handleThemeChange", "e", "portal", "document", "querySelector", "target", "value", "classList", "remove", "localStorage", "removeItem", "add", "setItem", "handlePositionChange", "handleSizeChange", "Number", "title", "div", "className", "label", "htmlFor", "p", "Select", "id", "name", "prefix", "ThemeIcon", "onChange", "option", "Object", "entries", "map", "key", "button", "aria-<PERSON><PERSON>", "data-hide-dev-tools", "onClick", "span", "code", "children", "select", "ChevronDownIcon", "DEV_TOOLS_INFO_USER_PREFERENCES_STYLES", "svg", "width", "height", "viewBox", "aria-hidden", "path", "fillRule", "clipRule", "d", "fill"], "mappings": ";;;;;;;;;;;AAAA,SAASA,QAAQ,QAAwB,QAAO;AAChD,SAASC,GAAG,QAAQ,2BAA0B;AAC9C,OAAOC,aAAa,6BAA4B;AAChD,SAASC,oBAAoB,EAAEC,iBAAiB,QAAQ,wBAAuB;AAC/E,OAAOC,eAAe,+BAA8B;AACpD,OAAOC,cAAc,8BAA6B;AAClD,OAAOC,gBAAgB,gCAA+B;AAEtD,SAASC,YAAY,QAAQ,mBAAkB;AAC/C,SACEC,eAAe,EACfC,oBAAoB,QAGf,gBAAe;AAEtB,OAAO,SAASC,gBAAgB,KAcS;IAdT,IAAA,EAC9BC,WAAW,EACXC,QAAQ,EACRC,IAAI,EACJC,KAAK,EACLC,QAAQ,EACR,GAAGC,OAQoC,GAdT;IAe9B,8CAA8C;IAC9C,MAAM,CAACC,OAAOC,SAAS,GAAGnB,SAASS;IAEnC,MAAMW,oBAAoB,CAACC;QACzB,MAAMC,SAASC,SAASC,aAAa,CAAC;QACtC,IAAI,CAACF,QAAQ;QAEbH,SAASE,EAAEI,MAAM,CAACC,KAAK;QAEvB,IAAIL,EAAEI,MAAM,CAACC,KAAK,KAAK,UAAU;YAC/BJ,OAAOK,SAAS,CAACC,MAAM,CAAC;YACxBN,OAAOK,SAAS,CAACC,MAAM,CAAC;YACxBC,aAAaC,UAAU,CAAC1B;YACxB;QACF;QAEA,IAAIiB,EAAEI,MAAM,CAACC,KAAK,KAAK,QAAQ;YAC7BJ,OAAOK,SAAS,CAACI,GAAG,CAAC;YACrBT,OAAOK,SAAS,CAACC,MAAM,CAAC;YACxBC,aAAaG,OAAO,CAAC5B,mBAAmB;QAC1C,OAAO;YACLkB,OAAOK,SAAS,CAACC,MAAM,CAAC;YACxBN,OAAOK,SAAS,CAACI,GAAG,CAAC;YACrBF,aAAaG,OAAO,CAAC5B,mBAAmB;QAC1C;IACF;IAEA,SAAS6B,qBAAqBZ,CAAuC;QACnET,YAAYS,EAAEI,MAAM,CAACC,KAAK;QAC1BG,aAAaG,OAAO,CAAC7B,sBAAsBkB,EAAEI,MAAM,CAACC,KAAK;IAC3D;IAEA,SAASQ,iBAAiB,KAAgD;QAAhD,IAAA,EAAET,MAAM,EAAwC,GAAhD;QACxB,MAAMC,QAAQS,OAAOV,OAAOC,KAAK;QACjCV,SAASU;IACX;IAEA,qBACE,KAAClB;QAAa4B,OAAM;QAAe,GAAGnB,KAAK;kBACzC,cAAA,MAACoB;YAAIC,WAAU;;8BACb,MAACD;oBAAIC,WAAU;;sCACb,MAACD;4BAAIC,WAAU;;8CACb,KAACC;oCAAMC,SAAQ;8CAAQ;;8CACvB,KAACC;oCAAEH,WAAU;8CAAyB;;;;sCAIxC,MAACI;4BACCC,IAAG;4BACHC,MAAK;4BACLC,sBAAQ,KAACC;gCAAU5B,OAAOA;;4BAC1BQ,OAAOR;4BACP6B,UAAU3B;;8CAEV,KAAC4B;oCAAOtB,OAAM;8CAAS;;8CACvB,KAACsB;oCAAOtB,OAAM;8CAAQ;;8CACtB,KAACsB;oCAAOtB,OAAM;8CAAO;;;;;;8BAIzB,MAACW;oBAAIC,WAAU;;sCACb,MAACD;4BAAIC,WAAU;;8CACb,KAACC;oCAAMC,SAAQ;8CAAW;;8CAC1B,KAACC;oCAAEH,WAAU;8CAAyB;;;;sCAIxC,MAACI;4BACCC,IAAG;4BACHC,MAAK;4BACLlB,OAAOb;4BACPkC,UAAUd;;8CAEV,KAACe;oCAAOtB,OAAM;8CAAc;;8CAC5B,KAACsB;oCAAOtB,OAAM;8CAAe;;8CAC7B,KAACsB;oCAAOtB,OAAM;8CAAW;;8CACzB,KAACsB;oCAAOtB,OAAM;8CAAY;;;;;;8BAI9B,MAACW;oBAAIC,WAAU;;sCACb,MAACD;4BAAIC,WAAU;;8CACb,KAACC;oCAAMC,SAAQ;8CAAO;;8CACtB,KAACC;oCAAEH,WAAU;8CAAyB;;;;sCAIxC,KAACI;4BACCC,IAAG;4BACHC,MAAK;4BACLlB,OAAOX;4BACPgC,UAAUb;sCAETe,OAAOC,OAAO,CAACxC,sBAAsByC,GAAG,CAAC;oCAAC,CAACC,KAAK1B,MAAM;gCACrD,qBACE,KAACsB;oCAAOtB,OAAOA;8CACZ0B;mCADwBA;4BAI/B;;;;8BAIJ,MAACf;oBAAIC,WAAU;;sCACb,MAACD;4BAAIC,WAAU;;8CACb,KAACC;oCAAMI,IAAG;8CAAiB;;8CAC3B,KAACF;oCAAEH,WAAU;8CAAyB;;;;sCAIxC,KAACD;4BAAIC,WAAU;sCACb,cAAA,MAACe;gCACCC,oBAAiB;gCACjBV,MAAK;gCACLW,qBAAmB;gCACnBjB,WAAU;gCACVkB,SAAS1C;;kDAET,KAACZ;kDACD,KAACuD;kDAAK;;;;;;;8BAKZ,KAACpB;oBAAIC,WAAU;8BACb,cAAA,MAACD;wBAAIC,WAAU;;0CACb,KAACC;0CAAM;;0CACP,MAACE;gCAAEH,WAAU;;oCAAyB;oCACD;kDACnC,KAACoB;wCAAKpB,WAAU;kDAAsB;;oCAA4B;oCAAI;kDAC9D,KAACoB;wCAAKpB,WAAU;kDAAsB;;oCAAmB;oCAAI;;;;;;;;;AAQnF;AAEA,SAASI,OAAO,KAMsC;IANtC,IAAA,EACdiB,QAAQ,EACRd,MAAM,EACN,GAAG5B,OAGiD,GANtC;IAOd,qBACE,MAACoB;QAAIC,WAAU;;YACZO;0BACD,KAACe;gBAAQ,GAAG3C,KAAK;0BAAG0C;;0BACpB,KAACE;;;AAGP;AAEA,SAASf,UAAU,KAAiD;IAAjD,IAAA,EAAE5B,KAAK,EAA0C,GAAjD;IACjB,OAAQA;QACN,KAAK;YACH,qBAAO,KAACX;QACV,KAAK;YACH,qBAAO,KAACD;QACV,KAAK;YACH,qBAAO,KAACD;QACV;YACE,OAAO;IACX;AACF;AAEA,OAAO,MAAMyD,yCAAyC7D,uBAgFrD;AAED,SAAS4D;IACP,qBACE,KAACE;QAAIC,OAAM;QAAKC,QAAO;QAAKC,SAAQ;QAAYC,aAAW;kBACzD,cAAA,KAACC;YACCC,UAAS;YACTC,UAAS;YACTC,GAAE;YACFC,MAAK;;;AAIb"}