{"version": 3, "sources": ["../../../src/server/async-storage/work-store.ts"], "sourcesContent": ["import type { WorkStore } from '../app-render/work-async-storage.external'\nimport type { IncrementalCache } from '../lib/incremental-cache'\nimport type { RenderOpts } from '../app-render/types'\nimport type { FetchMetric } from '../base-http'\nimport type { RequestLifecycleOpts } from '../base-server'\nimport type { FallbackRouteParams } from '../request/fallback-params'\nimport type { AppSegmentConfig } from '../../build/segment-config/app/app-segment-config'\nimport type { CacheLife } from '../use-cache/cache-life'\n\nimport { AfterContext } from '../after/after-context'\n\nimport { normalizeAppPath } from '../../shared/lib/router/utils/app-paths'\nimport { createLazyResult, type LazyResult } from '../lib/lazy-result'\nimport { getCacheHandlerEntries } from '../use-cache/handlers'\n\nexport type WorkStoreContext = {\n  /**\n   * The page that is being rendered. This relates to the path to the page file.\n   */\n  page: string\n\n  /**\n   * The route parameters that are currently unknown.\n   */\n  fallbackRouteParams: FallbackRouteParams | null\n\n  requestEndedState?: { ended?: boolean }\n  isPrefetchRequest?: boolean\n  renderOpts: {\n    cacheLifeProfiles?: { [profile: string]: CacheLife }\n    incrementalCache?: IncrementalCache\n    isOnDemandRevalidate?: boolean\n    fetchCache?: AppSegmentConfig['fetchCache']\n    isPossibleServerAction?: boolean\n    pendingWaitUntil?: Promise<any>\n    experimental: Pick<\n      RenderOpts['experimental'],\n      'isRoutePPREnabled' | 'dynamicIO' | 'authInterrupts'\n    >\n\n    /**\n     * Fetch metrics attached in patch-fetch.ts\n     **/\n    fetchMetrics?: FetchMetric[]\n\n    /**\n     * A hack around accessing the store value outside the context of the\n     * request.\n     *\n     * @internal\n     * @deprecated should only be used as a temporary workaround\n     */\n    // TODO: remove this when we resolve accessing the store outside the execution context\n    store?: WorkStore\n  } & Pick<\n    // Pull some properties from RenderOpts so that the docs are also\n    // mirrored.\n    RenderOpts,\n    | 'assetPrefix'\n    | 'supportsDynamicResponse'\n    | 'shouldWaitOnAllReady'\n    | 'isRevalidate'\n    | 'nextExport'\n    | 'isDraftMode'\n    | 'isDebugDynamicAccesses'\n    | 'dev'\n  > &\n    RequestLifecycleOpts &\n    Partial<Pick<RenderOpts, 'reactLoadableManifest'>>\n\n  /**\n   * The build ID of the current build.\n   */\n  buildId: string\n\n  // Tags that were previously revalidated (e.g. by a redirecting server action)\n  // and have already been sent to cache handlers.\n  previouslyRevalidatedTags: string[]\n}\n\nexport function createWorkStore({\n  page,\n  fallbackRouteParams,\n  renderOpts,\n  requestEndedState,\n  isPrefetchRequest,\n  buildId,\n  previouslyRevalidatedTags,\n}: WorkStoreContext): WorkStore {\n  /**\n   * Rules of Static & Dynamic HTML:\n   *\n   *    1.) We must generate static HTML unless the caller explicitly opts\n   *        in to dynamic HTML support.\n   *\n   *    2.) If dynamic HTML support is requested, we must honor that request\n   *        or throw an error. It is the sole responsibility of the caller to\n   *        ensure they aren't e.g. requesting dynamic HTML for an AMP page.\n   *\n   *    3.) If the request is in draft mode, we must generate dynamic HTML.\n   *\n   *    4.) If the request is a server action, we must generate dynamic HTML.\n   *\n   * These rules help ensure that other existing features like request caching,\n   * coalescing, and ISR continue working as intended.\n   */\n  const isStaticGeneration =\n    !renderOpts.shouldWaitOnAllReady &&\n    !renderOpts.supportsDynamicResponse &&\n    !renderOpts.isDraftMode &&\n    !renderOpts.isPossibleServerAction\n\n  const store: WorkStore = {\n    isStaticGeneration,\n    page,\n    fallbackRouteParams,\n    route: normalizeAppPath(page),\n    incrementalCache:\n      // we fallback to a global incremental cache for edge-runtime locally\n      // so that it can access the fs cache without mocks\n      renderOpts.incrementalCache || (globalThis as any).__incrementalCache,\n    cacheLifeProfiles: renderOpts.cacheLifeProfiles,\n    isRevalidate: renderOpts.isRevalidate,\n    isPrerendering: renderOpts.nextExport,\n    fetchCache: renderOpts.fetchCache,\n    isOnDemandRevalidate: renderOpts.isOnDemandRevalidate,\n\n    isDraftMode: renderOpts.isDraftMode,\n\n    requestEndedState,\n    isPrefetchRequest,\n    buildId,\n    reactLoadableManifest: renderOpts?.reactLoadableManifest || {},\n    assetPrefix: renderOpts?.assetPrefix || '',\n\n    afterContext: createAfterContext(renderOpts),\n    dynamicIOEnabled: renderOpts.experimental.dynamicIO,\n    dev: renderOpts.dev ?? false,\n    previouslyRevalidatedTags,\n    refreshTagsByCacheKind: createRefreshTagsByCacheKind(),\n  }\n\n  // TODO: remove this when we resolve accessing the store outside the execution context\n  renderOpts.store = store\n\n  return store\n}\n\nfunction createAfterContext(renderOpts: RequestLifecycleOpts): AfterContext {\n  const { waitUntil, onClose, onAfterTaskError } = renderOpts\n  return new AfterContext({\n    waitUntil,\n    onClose,\n    onTaskError: onAfterTaskError,\n  })\n}\n\n/**\n * Creates a map with lazy results that refresh tags for the respective cache\n * kind when they're awaited for the first time.\n */\nfunction createRefreshTagsByCacheKind(): Map<string, LazyResult<void>> {\n  const refreshTagsByCacheKind = new Map<string, LazyResult<void>>()\n  const cacheHandlers = getCacheHandlerEntries()\n\n  if (cacheHandlers) {\n    for (const [kind, cacheHandler] of cacheHandlers) {\n      if ('refreshTags' in cacheHandler) {\n        refreshTagsByCacheKind.set(\n          kind,\n          createLazyResult(async () => cacheHandler.refreshTags())\n        )\n      }\n    }\n  }\n\n  return refreshTagsByCacheKind\n}\n"], "names": ["createWorkStore", "page", "fallbackRouteParams", "renderOpts", "requestEndedState", "isPrefetchRequest", "buildId", "previouslyRevalidatedTags", "isStaticGeneration", "shouldWaitOnAllReady", "supportsDynamicResponse", "isDraftMode", "isPossibleServerAction", "store", "route", "normalizeAppPath", "incrementalCache", "globalThis", "__incrementalCache", "cacheLifeProfiles", "isRevalidate", "isPrerendering", "nextExport", "fetchCache", "isOnDemandRevalidate", "reactLoadableManifest", "assetPrefix", "afterContext", "createAfterContext", "dynamicIOEnabled", "experimental", "dynamicIO", "dev", "refreshTagsByCacheKind", "createRefreshTagsByCacheKind", "waitUntil", "onClose", "onAfterTaskError", "AfterContext", "onTaskError", "Map", "cacheHandlers", "getCacheHandlerEntries", "kind", "cache<PERSON><PERSON><PERSON>", "set", "createLazyResult", "refreshTags"], "mappings": ";;;;+BAgFgBA;;;eAAAA;;;8BAvEa;0BAEI;4BACiB;0BACX;AAmEhC,SAASA,gBAAgB,EAC9BC,IAAI,EACJC,mBAAmB,EACnBC,UAAU,EACVC,iBAAiB,EACjBC,iBAAiB,EACjBC,OAAO,EACPC,yBAAyB,EACR;IACjB;;;;;;;;;;;;;;;;GAgBC,GACD,MAAMC,qBACJ,CAACL,WAAWM,oBAAoB,IAChC,CAACN,WAAWO,uBAAuB,IACnC,CAACP,WAAWQ,WAAW,IACvB,CAACR,WAAWS,sBAAsB;IAEpC,MAAMC,QAAmB;QACvBL;QACAP;QACAC;QACAY,OAAOC,IAAAA,0BAAgB,EAACd;QACxBe,kBACE,qEAAqE;QACrE,mDAAmD;QACnDb,WAAWa,gBAAgB,IAAI,AAACC,WAAmBC,kBAAkB;QACvEC,mBAAmBhB,WAAWgB,iBAAiB;QAC/CC,cAAcjB,WAAWiB,YAAY;QACrCC,gBAAgBlB,WAAWmB,UAAU;QACrCC,YAAYpB,WAAWoB,UAAU;QACjCC,sBAAsBrB,WAAWqB,oBAAoB;QAErDb,aAAaR,WAAWQ,WAAW;QAEnCP;QACAC;QACAC;QACAmB,uBAAuBtB,CAAAA,8BAAAA,WAAYsB,qBAAqB,KAAI,CAAC;QAC7DC,aAAavB,CAAAA,8BAAAA,WAAYuB,WAAW,KAAI;QAExCC,cAAcC,mBAAmBzB;QACjC0B,kBAAkB1B,WAAW2B,YAAY,CAACC,SAAS;QACnDC,KAAK7B,WAAW6B,GAAG,IAAI;QACvBzB;QACA0B,wBAAwBC;IAC1B;IAEA,sFAAsF;IACtF/B,WAAWU,KAAK,GAAGA;IAEnB,OAAOA;AACT;AAEA,SAASe,mBAAmBzB,UAAgC;IAC1D,MAAM,EAAEgC,SAAS,EAAEC,OAAO,EAAEC,gBAAgB,EAAE,GAAGlC;IACjD,OAAO,IAAImC,0BAAY,CAAC;QACtBH;QACAC;QACAG,aAAaF;IACf;AACF;AAEA;;;CAGC,GACD,SAASH;IACP,MAAMD,yBAAyB,IAAIO;IACnC,MAAMC,gBAAgBC,IAAAA,gCAAsB;IAE5C,IAAID,eAAe;QACjB,KAAK,MAAM,CAACE,MAAMC,aAAa,IAAIH,cAAe;YAChD,IAAI,iBAAiBG,cAAc;gBACjCX,uBAAuBY,GAAG,CACxBF,MACAG,IAAAA,4BAAgB,EAAC,UAAYF,aAAaG,WAAW;YAEzD;QACF;IACF;IAEA,OAAOd;AACT"}