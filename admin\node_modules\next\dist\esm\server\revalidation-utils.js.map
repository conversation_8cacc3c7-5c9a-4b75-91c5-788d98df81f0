{"version": 3, "sources": ["../../src/server/revalidation-utils.ts"], "sourcesContent": ["import type { WorkStore } from './app-render/work-async-storage.external'\nimport type { IncrementalCache } from './lib/incremental-cache'\nimport { getCacheHandlers } from './use-cache/handlers'\n\n/** Run a callback, and execute any *new* revalidations added during its runtime. */\nexport async function withExecuteRevalidates<T>(\n  store: WorkStore | undefined,\n  callback: () => Promise<T>\n): Promise<T> {\n  if (!store) {\n    return callback()\n  }\n  // If we executed any revalidates during the request, then we don't want to execute them again.\n  // save the state so we can check if anything changed after we're done running callbacks.\n  const savedRevalidationState = cloneRevalidationState(store)\n  try {\n    return await callback()\n  } finally {\n    // Check if we have any new revalidates, and if so, wait until they are all resolved.\n    const newRevalidates = diffRevalidationState(\n      savedRevalidationState,\n      cloneRevalidationState(store)\n    )\n    await executeRevalidates(store, newRevalidates)\n  }\n}\n\ntype RevalidationState = Required<\n  Pick<\n    WorkStore,\n    'pendingRevalidatedTags' | 'pendingRevalidates' | 'pendingRevalidateWrites'\n  >\n>\n\nfunction cloneRevalidationState(store: WorkStore): RevalidationState {\n  return {\n    pendingRevalidatedTags: store.pendingRevalidatedTags\n      ? [...store.pendingRevalidatedTags]\n      : [],\n    pendingRevalidates: { ...store.pendingRevalidates },\n    pendingRevalidateWrites: store.pendingRevalidateWrites\n      ? [...store.pendingRevalidateWrites]\n      : [],\n  }\n}\n\nfunction diffRevalidationState(\n  prev: RevalidationState,\n  curr: RevalidationState\n): RevalidationState {\n  const prevTags = new Set(prev.pendingRevalidatedTags)\n  const prevRevalidateWrites = new Set(prev.pendingRevalidateWrites)\n  return {\n    pendingRevalidatedTags: curr.pendingRevalidatedTags.filter(\n      (tag) => !prevTags.has(tag)\n    ),\n    pendingRevalidates: Object.fromEntries(\n      Object.entries(curr.pendingRevalidates).filter(\n        ([key]) => !(key in prev.pendingRevalidates)\n      )\n    ),\n    pendingRevalidateWrites: curr.pendingRevalidateWrites.filter(\n      (promise) => !prevRevalidateWrites.has(promise)\n    ),\n  }\n}\n\nasync function revalidateTags(\n  tags: string[],\n  incrementalCache: IncrementalCache | undefined\n): Promise<void> {\n  if (tags.length === 0) {\n    return\n  }\n\n  const promises: Promise<void>[] = []\n\n  if (incrementalCache) {\n    promises.push(incrementalCache.revalidateTag(tags))\n  }\n\n  const handlers = getCacheHandlers()\n  if (handlers) {\n    for (const handler of handlers) {\n      promises.push(handler.expireTags(...tags))\n    }\n  }\n\n  await Promise.all(promises)\n}\n\nexport async function executeRevalidates(\n  workStore: WorkStore,\n  state?: RevalidationState\n) {\n  const pendingRevalidatedTags =\n    state?.pendingRevalidatedTags ?? workStore.pendingRevalidatedTags ?? []\n\n  const pendingRevalidates =\n    state?.pendingRevalidates ?? workStore.pendingRevalidates ?? {}\n\n  const pendingRevalidateWrites =\n    state?.pendingRevalidateWrites ?? workStore.pendingRevalidateWrites ?? []\n\n  return Promise.all([\n    revalidateTags(pendingRevalidatedTags, workStore.incrementalCache),\n    ...Object.values(pendingRevalidates),\n    ...pendingRevalidateWrites,\n  ])\n}\n"], "names": ["getCacheHandlers", "withExecuteRevalidates", "store", "callback", "savedRevalidationState", "cloneRevalidationState", "newRevalidates", "diffRevalidationState", "executeRevalidates", "pendingRevalidatedTags", "pendingRevalidates", "pendingRevalidateWrites", "prev", "curr", "prevTags", "Set", "prevRevalidateWrites", "filter", "tag", "has", "Object", "fromEntries", "entries", "key", "promise", "revalidateTags", "tags", "incrementalCache", "length", "promises", "push", "revalidateTag", "handlers", "handler", "expireTags", "Promise", "all", "workStore", "state", "values"], "mappings": "AAEA,SAASA,gBAAgB,QAAQ,uBAAsB;AAEvD,kFAAkF,GAClF,OAAO,eAAeC,uBACpBC,KAA4B,EAC5BC,QAA0B;IAE1B,IAAI,CAACD,OAAO;QACV,OAAOC;IACT;IACA,+FAA+F;IAC/F,yFAAyF;IACzF,MAAMC,yBAAyBC,uBAAuBH;IACtD,IAAI;QACF,OAAO,MAAMC;IACf,SAAU;QACR,qFAAqF;QACrF,MAAMG,iBAAiBC,sBACrBH,wBACAC,uBAAuBH;QAEzB,MAAMM,mBAAmBN,OAAOI;IAClC;AACF;AASA,SAASD,uBAAuBH,KAAgB;IAC9C,OAAO;QACLO,wBAAwBP,MAAMO,sBAAsB,GAChD;eAAIP,MAAMO,sBAAsB;SAAC,GACjC,EAAE;QACNC,oBAAoB;YAAE,GAAGR,MAAMQ,kBAAkB;QAAC;QAClDC,yBAAyBT,MAAMS,uBAAuB,GAClD;eAAIT,MAAMS,uBAAuB;SAAC,GAClC,EAAE;IACR;AACF;AAEA,SAASJ,sBACPK,IAAuB,EACvBC,IAAuB;IAEvB,MAAMC,WAAW,IAAIC,IAAIH,KAAKH,sBAAsB;IACpD,MAAMO,uBAAuB,IAAID,IAAIH,KAAKD,uBAAuB;IACjE,OAAO;QACLF,wBAAwBI,KAAKJ,sBAAsB,CAACQ,MAAM,CACxD,CAACC,MAAQ,CAACJ,SAASK,GAAG,CAACD;QAEzBR,oBAAoBU,OAAOC,WAAW,CACpCD,OAAOE,OAAO,CAACT,KAAKH,kBAAkB,EAAEO,MAAM,CAC5C,CAAC,CAACM,IAAI,GAAK,CAAEA,CAAAA,OAAOX,KAAKF,kBAAkB,AAAD;QAG9CC,yBAAyBE,KAAKF,uBAAuB,CAACM,MAAM,CAC1D,CAACO,UAAY,CAACR,qBAAqBG,GAAG,CAACK;IAE3C;AACF;AAEA,eAAeC,eACbC,IAAc,EACdC,gBAA8C;IAE9C,IAAID,KAAKE,MAAM,KAAK,GAAG;QACrB;IACF;IAEA,MAAMC,WAA4B,EAAE;IAEpC,IAAIF,kBAAkB;QACpBE,SAASC,IAAI,CAACH,iBAAiBI,aAAa,CAACL;IAC/C;IAEA,MAAMM,WAAWhC;IACjB,IAAIgC,UAAU;QACZ,KAAK,MAAMC,WAAWD,SAAU;YAC9BH,SAASC,IAAI,CAACG,QAAQC,UAAU,IAAIR;QACtC;IACF;IAEA,MAAMS,QAAQC,GAAG,CAACP;AACpB;AAEA,OAAO,eAAerB,mBACpB6B,SAAoB,EACpBC,KAAyB;IAEzB,MAAM7B,yBACJ6B,CAAAA,yBAAAA,MAAO7B,sBAAsB,KAAI4B,UAAU5B,sBAAsB,IAAI,EAAE;IAEzE,MAAMC,qBACJ4B,CAAAA,yBAAAA,MAAO5B,kBAAkB,KAAI2B,UAAU3B,kBAAkB,IAAI,CAAC;IAEhE,MAAMC,0BACJ2B,CAAAA,yBAAAA,MAAO3B,uBAAuB,KAAI0B,UAAU1B,uBAAuB,IAAI,EAAE;IAE3E,OAAOwB,QAAQC,GAAG,CAAC;QACjBX,eAAehB,wBAAwB4B,UAAUV,gBAAgB;WAC9DP,OAAOmB,MAAM,CAAC7B;WACdC;KACJ;AACH"}