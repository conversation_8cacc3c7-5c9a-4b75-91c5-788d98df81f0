{"version": 3, "sources": ["../../../src/server/route-definitions/route-definition.ts"], "sourcesContent": ["import type { RouteKind } from '../route-kind'\n\nexport interface RouteDefinition<K extends RouteKind = RouteKind> {\n  readonly kind: K\n  readonly bundlePath: string\n  readonly filename: string\n  /**\n   * Describes the pathname including all internal modifiers such as\n   * intercepting routes, parallel routes and route/page suffixes that are not\n   * part of the pathname.\n   */\n  readonly page: string\n\n  /**\n   * The pathname (including dynamic placeholders) for a route to resolve.\n   */\n  readonly pathname: string\n}\n"], "names": [], "mappings": "AAEA,WAeC"}