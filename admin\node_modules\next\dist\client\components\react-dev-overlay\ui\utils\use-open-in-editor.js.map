{"version": 3, "sources": ["../../../../../../src/client/components/react-dev-overlay/ui/utils/use-open-in-editor.ts"], "sourcesContent": ["import { useCallback } from 'react'\n\nexport function useOpenInEditor({\n  file,\n  lineNumber,\n  column,\n}: {\n  file?: string | null\n  lineNumber?: number | null\n  column?: number | null\n} = {}) {\n  const openInEditor = useCallback(() => {\n    if (file == null || lineNumber == null || column == null) return\n\n    const params = new URLSearchParams()\n    params.append('file', file)\n    params.append('lineNumber', String(lineNumber))\n    params.append('column', String(column))\n\n    self\n      .fetch(\n        `${\n          process.env.__NEXT_ROUTER_BASEPATH || ''\n        }/__nextjs_launch-editor?${params.toString()}`\n      )\n      .then(\n        () => {},\n        (cause) => {\n          console.error(\n            `Failed to open file \"${file} (${lineNumber}:${column})\" in your editor. Cause:`,\n            cause\n          )\n        }\n      )\n  }, [file, lineNumber, column])\n\n  return openInEditor\n}\n"], "names": ["useOpenInEditor", "file", "lineNumber", "column", "openInEditor", "useCallback", "params", "URLSearchParams", "append", "String", "self", "fetch", "process", "env", "__NEXT_ROUTER_BASEPATH", "toString", "then", "cause", "console", "error"], "mappings": ";;;;+BAEgBA;;;eAAAA;;;uBAFY;AAErB,SAASA,gBAAgB;IAAA,IAAA,EAC9BC,IAAI,EACJC,UAAU,EACVC,MAAM,EAKP,GAR+B,mBAQ5B,CAAC,IAR2B;IAS9B,MAAMC,eAAeC,IAAAA,kBAAW,EAAC;QAC/B,IAAIJ,QAAQ,QAAQC,cAAc,QAAQC,UAAU,MAAM;QAE1D,MAAMG,SAAS,IAAIC;QACnBD,OAAOE,MAAM,CAAC,QAAQP;QACtBK,OAAOE,MAAM,CAAC,cAAcC,OAAOP;QACnCI,OAAOE,MAAM,CAAC,UAAUC,OAAON;QAE/BO,KACGC,KAAK,CACJ,AACEC,CAAAA,QAAQC,GAAG,CAACC,sBAAsB,IAAI,EAAC,IACxC,6BAA0BR,OAAOS,QAAQ,IAE3CC,IAAI,CACH,KAAO,GACP,CAACC;YACCC,QAAQC,KAAK,CACX,AAAC,0BAAuBlB,OAAK,OAAIC,aAAW,MAAGC,SAAO,6BACtDc;QAEJ;IAEN,GAAG;QAAChB;QAAMC;QAAYC;KAAO;IAE7B,OAAOC;AACT"}