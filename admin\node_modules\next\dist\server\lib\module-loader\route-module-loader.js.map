{"version": 3, "sources": ["../../../../src/server/lib/module-loader/route-module-loader.ts"], "sourcesContent": ["import type { RouteModule } from '../../route-modules/route-module'\nimport type { ModuleLoader } from './module-loader'\n\nimport { NodeModuleLoader } from './node-module-loader'\n\nexport interface AppLoaderModule<M extends RouteModule = RouteModule> {\n  routeModule: M\n}\n\nexport class RouteModuleLoader {\n  static async load<M extends RouteModule>(\n    id: string,\n    loader: ModuleLoader = new NodeModuleLoader()\n  ): Promise<M> {\n    const module: AppLoaderModule<M> = await loader.load(id)\n    if ('routeModule' in module) {\n      return module.routeModule\n    }\n\n    throw new Error(`Module \"${id}\" does not export a routeModule.`)\n  }\n}\n"], "names": ["RouteModuleLoader", "load", "id", "loader", "NodeModuleLoader", "module", "routeModule", "Error"], "mappings": ";;;;+BASaA;;;eAAAA;;;kCANoB;AAM1B,MAAMA;IACX,aAAaC,KACXC,EAAU,EACVC,SAAuB,IAAIC,kCAAgB,EAAE,EACjC;QACZ,MAAMC,SAA6B,MAAMF,OAAOF,IAAI,CAACC;QACrD,IAAI,iBAAiBG,QAAQ;YAC3B,OAAOA,OAAOC,WAAW;QAC3B;QAEA,MAAM,qBAA0D,CAA1D,IAAIC,MAAM,CAAC,QAAQ,EAAEL,GAAG,gCAAgC,CAAC,GAAzD,qBAAA;mBAAA;wBAAA;0BAAA;QAAyD;IACjE;AACF"}