{"version": 3, "sources": ["../../../../src/experimental/testmode/proxy/types.ts"], "sourcesContent": ["export interface ProxyServer {\n  readonly port: number\n  fetchWith(\n    input: string | URL,\n    init?: RequestInit,\n    testData?: string\n  ): Promise<Response>\n  close(): void\n}\n\ninterface ProxyRequestBase {\n  testData: string\n  api: string\n}\n\ninterface ProxyResponseBase {\n  api: string\n}\n\nexport interface ProxyUnhandledResponse extends ProxyResponseBase {\n  api: 'unhandled'\n}\n\nexport interface ProxyAbortResponse extends ProxyResponseBase {\n  api: 'abort'\n}\n\nexport interface ProxyContinueResponse extends ProxyResponseBase {\n  api: 'continue'\n}\n\nexport interface ProxyFetchRequest extends ProxyRequestBase {\n  api: 'fetch'\n  request: {\n    url: string\n    headers: Array<[string, string]>\n    body: string | null\n  } & Omit<RequestInit, 'headers' | 'body'>\n}\n\nexport interface ProxyFetchResponse extends ProxyResponseBase {\n  api: 'fetch'\n  response: {\n    status: number\n    headers: Array<[string, string]>\n    body: string | null\n  }\n}\n\nexport type ProxyRequest = ProxyFetchRequest\n\nexport type ProxyResponse =\n  | ProxyUnhandledResponse\n  | ProxyAbortResponse\n  | ProxyContinueResponse\n  | ProxyFetchResponse\n\nexport const ABORT: ProxyResponse = { api: 'abort' }\nexport const CONTINUE: ProxyResponse = { api: 'continue' }\nexport const UNHANDLED: ProxyResponse = { api: 'unhandled' }\n"], "names": ["ABORT", "CONTINUE", "UNHANDLED", "api"], "mappings": ";;;;;;;;;;;;;;;;IAyDaA,KAAK;eAALA;;IACAC,QAAQ;eAARA;;IACAC,SAAS;eAATA;;;AAFN,MAAMF,QAAuB;IAAEG,KAAK;AAAQ;AAC5C,MAAMF,WAA0B;IAAEE,KAAK;AAAW;AAClD,MAAMD,YAA2B;IAAEC,KAAK;AAAY"}