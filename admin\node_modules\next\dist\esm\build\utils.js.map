{"version": 3, "sources": ["../../src/build/utils.ts"], "sourcesContent": ["import type { NextConfigComplete } from '../server/config-shared'\nimport type { ExperimentalPPRConfig } from '../server/lib/experimental/ppr'\nimport type { AppBuildManifest } from './webpack/plugins/app-build-manifest-plugin'\nimport type { AssetBinding } from './webpack/loaders/get-module-build-info'\nimport type { PageConfig, ServerRuntime } from '../types'\nimport type { BuildManifest } from '../server/get-page-files'\nimport type {\n  Redirect,\n  Rewrite,\n  Header,\n  CustomRoutes,\n} from '../lib/load-custom-routes'\nimport type {\n  EdgeFunctionDefinition,\n  MiddlewareManifest,\n} from './webpack/plugins/middleware-plugin'\nimport type { WebpackLayerName } from '../lib/constants'\nimport type { AppPageModule } from '../server/route-modules/app-page/module'\nimport type { RouteModule } from '../server/route-modules/route-module'\nimport type { NextComponentType } from '../shared/lib/utils'\n\nimport '../server/require-hook'\nimport '../server/node-polyfill-crypto'\nimport '../server/node-environment'\n\nimport {\n  green,\n  yellow,\n  red,\n  cyan,\n  white,\n  bold,\n  underline,\n} from '../lib/picocolors'\nimport getGzipSize from 'next/dist/compiled/gzip-size'\nimport textTable from 'next/dist/compiled/text-table'\nimport path from 'path'\nimport { promises as fs } from 'fs'\nimport { isValidElementType } from 'next/dist/compiled/react-is'\nimport stripAnsi from 'next/dist/compiled/strip-ansi'\nimport browserslist from 'next/dist/compiled/browserslist'\nimport {\n  SSG_GET_INITIAL_PROPS_CONFLICT,\n  SERVER_PROPS_GET_INIT_PROPS_CONFLICT,\n  SERVER_PROPS_SSG_CONFLICT,\n  MIDDLEWARE_FILENAME,\n  INSTRUMENTATION_HOOK_FILENAME,\n  WEBPACK_LAYERS,\n} from '../lib/constants'\nimport {\n  MODERN_BROWSERSLIST_TARGET,\n  UNDERSCORE_NOT_FOUND_ROUTE,\n} from '../shared/lib/constants'\nimport prettyBytes from '../lib/pretty-bytes'\nimport { isDynamicRoute } from '../shared/lib/router/utils/is-dynamic'\nimport { findPageFile } from '../server/lib/find-page-file'\nimport { isEdgeRuntime } from '../lib/is-edge-runtime'\nimport * as Log from './output/log'\nimport { loadComponents } from '../server/load-components'\nimport type { LoadComponentsReturnType } from '../server/load-components'\nimport { trace } from '../trace'\nimport { setHttpClientAndAgentOptions } from '../server/setup-http-agent-env'\nimport { Sema } from 'next/dist/compiled/async-sema'\nimport { denormalizePagePath } from '../shared/lib/page-path/denormalize-page-path'\nimport { normalizePagePath } from '../shared/lib/page-path/normalize-page-path'\nimport { getRuntimeContext } from '../server/web/sandbox'\nimport { isClientReference } from '../lib/client-and-server-references'\nimport { normalizeAppPath } from '../shared/lib/router/utils/app-paths'\nimport { denormalizeAppPagePath } from '../shared/lib/page-path/denormalize-app-path'\nimport { RouteKind } from '../server/route-kind'\nimport type { PageExtensions } from './page-extensions-type'\nimport { isInterceptionRouteAppPath } from '../shared/lib/router/utils/interception-routes'\nimport { checkIsRoutePPREnabled } from '../server/lib/experimental/ppr'\nimport type { FallbackMode } from '../lib/fallback'\nimport type { OutgoingHttpHeaders } from 'http'\nimport type { AppSegmentConfig } from './segment-config/app/app-segment-config'\nimport type { AppSegment } from './segment-config/app/app-segments'\nimport { collectSegments } from './segment-config/app/app-segments'\nimport { createIncrementalCache } from '../export/helpers/create-incremental-cache'\nimport { collectRootParamKeys } from './segment-config/app/collect-root-param-keys'\nimport { buildAppStaticPaths } from './static-paths/app'\nimport { buildPagesStaticPaths } from './static-paths/pages'\nimport type { PrerenderedRoute } from './static-paths/types'\nimport type { CacheControl } from '../server/lib/cache-control'\nimport { formatExpire, formatRevalidate } from './output/format'\n\nexport type ROUTER_TYPE = 'pages' | 'app'\n\n// Use `print()` for expected console output\nconst print = console.log\n\nconst RESERVED_PAGE = /^\\/(_app|_error|_document|api(\\/|$))/\nconst fileGzipStats: { [k: string]: Promise<number> | undefined } = {}\nconst fsStatGzip = (file: string) => {\n  const cached = fileGzipStats[file]\n  if (cached) return cached\n  return (fileGzipStats[file] = getGzipSize.file(file))\n}\n\nconst fileSize = async (file: string) => (await fs.stat(file)).size\n\nconst fileStats: { [k: string]: Promise<number> | undefined } = {}\nconst fsStat = (file: string) => {\n  const cached = fileStats[file]\n  if (cached) return cached\n  return (fileStats[file] = fileSize(file))\n}\n\nexport function unique<T>(main: ReadonlyArray<T>, sub: ReadonlyArray<T>): T[] {\n  return [...new Set([...main, ...sub])]\n}\n\nexport function difference<T>(\n  main: ReadonlyArray<T> | ReadonlySet<T>,\n  sub: ReadonlyArray<T> | ReadonlySet<T>\n): T[] {\n  const a = new Set(main)\n  const b = new Set(sub)\n  return [...a].filter((x) => !b.has(x))\n}\n\n/**\n * Return an array of the items shared by both arrays.\n */\nfunction intersect<T>(main: ReadonlyArray<T>, sub: ReadonlyArray<T>): T[] {\n  const a = new Set(main)\n  const b = new Set(sub)\n  return [...new Set([...a].filter((x) => b.has(x)))]\n}\n\nfunction sum(a: ReadonlyArray<number>): number {\n  return a.reduce((size, stat) => size + stat, 0)\n}\n\ntype ComputeFilesGroup = {\n  files: ReadonlyArray<string>\n  size: {\n    total: number\n  }\n}\n\ntype ComputeFilesManifest = {\n  unique: ComputeFilesGroup\n  common: ComputeFilesGroup\n}\n\ntype ComputeFilesManifestResult = {\n  router: {\n    pages: ComputeFilesManifest\n    app?: ComputeFilesManifest\n  }\n  sizes: Map<string, number>\n}\n\nlet cachedBuildManifest: BuildManifest | undefined\nlet cachedAppBuildManifest: AppBuildManifest | undefined\n\nlet lastCompute: ComputeFilesManifestResult | undefined\nlet lastComputePageInfo: boolean | undefined\n\nexport async function computeFromManifest(\n  manifests: {\n    build: BuildManifest\n    app?: AppBuildManifest\n  },\n  distPath: string,\n  gzipSize: boolean = true,\n  pageInfos?: Map<string, PageInfo>\n): Promise<ComputeFilesManifestResult> {\n  if (\n    Object.is(cachedBuildManifest, manifests.build) &&\n    lastComputePageInfo === !!pageInfos &&\n    Object.is(cachedAppBuildManifest, manifests.app)\n  ) {\n    return lastCompute!\n  }\n\n  // Determine the files that are in pages and app and count them, this will\n  // tell us if they are unique or common.\n\n  const countBuildFiles = (\n    map: Map<string, number>,\n    key: string,\n    manifest: Record<string, ReadonlyArray<string>>\n  ) => {\n    for (const file of manifest[key]) {\n      if (key === '/_app') {\n        map.set(file, Infinity)\n      } else if (map.has(file)) {\n        map.set(file, map.get(file)! + 1)\n      } else {\n        map.set(file, 1)\n      }\n    }\n  }\n\n  const files: {\n    pages: {\n      each: Map<string, number>\n      expected: number\n    }\n    app?: {\n      each: Map<string, number>\n      expected: number\n    }\n  } = {\n    pages: { each: new Map(), expected: 0 },\n  }\n\n  for (const key in manifests.build.pages) {\n    if (pageInfos) {\n      const pageInfo = pageInfos.get(key)\n      // don't include AMP pages since they don't rely on shared bundles\n      // AMP First pages are not under the pageInfos key\n      if (pageInfo?.isHybridAmp) {\n        continue\n      }\n    }\n\n    files.pages.expected++\n    countBuildFiles(files.pages.each, key, manifests.build.pages)\n  }\n\n  // Collect the build files form the app manifest.\n  if (manifests.app?.pages) {\n    files.app = { each: new Map<string, number>(), expected: 0 }\n\n    for (const key in manifests.app.pages) {\n      files.app.expected++\n      countBuildFiles(files.app.each, key, manifests.app.pages)\n    }\n  }\n\n  const getSize = gzipSize ? fsStatGzip : fsStat\n  const stats = new Map<string, number>()\n\n  // For all of the files in the pages and app manifests, compute the file size\n  // at once.\n\n  await Promise.all(\n    [\n      ...new Set<string>([\n        ...files.pages.each.keys(),\n        ...(files.app?.each.keys() ?? []),\n      ]),\n    ].map(async (f) => {\n      try {\n        // Add the file size to the stats.\n        stats.set(f, await getSize(path.join(distPath, f)))\n      } catch {}\n    })\n  )\n\n  const groupFiles = async (listing: {\n    each: Map<string, number>\n    expected: number\n  }): Promise<ComputeFilesManifest> => {\n    const entries = [...listing.each.entries()]\n\n    const shapeGroup = (group: [string, number][]): ComputeFilesGroup =>\n      group.reduce(\n        (acc, [f]) => {\n          acc.files.push(f)\n\n          const size = stats.get(f)\n          if (typeof size === 'number') {\n            acc.size.total += size\n          }\n\n          return acc\n        },\n        {\n          files: [] as string[],\n          size: {\n            total: 0,\n          },\n        }\n      )\n\n    return {\n      unique: shapeGroup(entries.filter(([, len]) => len === 1)),\n      common: shapeGroup(\n        entries.filter(\n          ([, len]) => len === listing.expected || len === Infinity\n        )\n      ),\n    }\n  }\n\n  lastCompute = {\n    router: {\n      pages: await groupFiles(files.pages),\n      app: files.app ? await groupFiles(files.app) : undefined,\n    },\n    sizes: stats,\n  }\n\n  cachedBuildManifest = manifests.build\n  cachedAppBuildManifest = manifests.app\n  lastComputePageInfo = !!pageInfos\n  return lastCompute!\n}\n\nexport function isMiddlewareFilename(file?: string | null) {\n  return file === MIDDLEWARE_FILENAME || file === `src/${MIDDLEWARE_FILENAME}`\n}\n\nexport function isInstrumentationHookFilename(file?: string | null) {\n  return (\n    file === INSTRUMENTATION_HOOK_FILENAME ||\n    file === `src/${INSTRUMENTATION_HOOK_FILENAME}`\n  )\n}\n\nconst filterAndSortList = (\n  list: ReadonlyArray<string>,\n  routeType: ROUTER_TYPE,\n  hasCustomApp: boolean\n) => {\n  let pages: string[]\n  if (routeType === 'app') {\n    // filter out static app route of /favicon.ico\n    pages = list.filter((e) => e !== '/favicon.ico')\n  } else {\n    // filter built-in pages\n    pages = list\n      .slice()\n      .filter(\n        (e) =>\n          !(\n            e === '/_document' ||\n            e === '/_error' ||\n            (!hasCustomApp && e === '/_app')\n          )\n      )\n  }\n  return pages.sort((a, b) => a.localeCompare(b))\n}\n\nexport interface PageInfo {\n  isHybridAmp?: boolean\n  size: number\n  totalSize: number\n  isStatic: boolean\n  isSSG: boolean\n  /**\n   * If true, it means that the route has partial prerendering enabled.\n   */\n  isRoutePPREnabled: boolean\n  ssgPageRoutes: string[] | null\n  initialCacheControl: CacheControl | undefined\n  pageDuration: number | undefined\n  ssgPageDurations: number[] | undefined\n  runtime: ServerRuntime\n  hasEmptyPrelude?: boolean\n  hasPostponed?: boolean\n  isDynamicAppRoute?: boolean\n}\n\nexport type PageInfos = Map<string, PageInfo>\n\nexport interface RoutesUsingEdgeRuntime {\n  [route: string]: 0\n}\n\nexport function collectRoutesUsingEdgeRuntime(\n  input: PageInfos\n): RoutesUsingEdgeRuntime {\n  const routesUsingEdgeRuntime: RoutesUsingEdgeRuntime = {}\n  for (const [route, info] of input.entries()) {\n    if (isEdgeRuntime(info.runtime)) {\n      routesUsingEdgeRuntime[route] = 0\n    }\n  }\n\n  return routesUsingEdgeRuntime\n}\n\nexport async function printTreeView(\n  lists: {\n    pages: ReadonlyArray<string>\n    app: ReadonlyArray<string> | undefined\n  },\n  pageInfos: Map<string, PageInfo>,\n  {\n    distPath,\n    buildId,\n    pagesDir,\n    pageExtensions,\n    buildManifest,\n    appBuildManifest,\n    middlewareManifest,\n    useStaticPages404,\n    gzipSize = true,\n  }: {\n    distPath: string\n    buildId: string\n    pagesDir?: string\n    pageExtensions: PageExtensions\n    buildManifest: BuildManifest\n    appBuildManifest?: AppBuildManifest\n    middlewareManifest: MiddlewareManifest\n    useStaticPages404: boolean\n    gzipSize?: boolean\n  }\n) {\n  const getPrettySize = (\n    _size: number,\n    { strong }: { strong?: boolean } = {}\n  ): string => {\n    const size = process.env.__NEXT_PRIVATE_DETERMINISTIC_BUILD_OUTPUT\n      ? 'N/A kB'\n      : prettyBytes(_size)\n\n    return strong ? white(bold(size)) : size\n  }\n\n  // Can be overridden for test purposes to omit the build duration output.\n  const MIN_DURATION = process.env.__NEXT_PRIVATE_DETERMINISTIC_BUILD_OUTPUT\n    ? Infinity // Don't ever log build durations.\n    : 300\n\n  const getPrettyDuration = (_duration: number): string => {\n    const duration = `${_duration} ms`\n    // green for 300-1000ms\n    if (_duration < 1000) return green(duration)\n    // yellow for 1000-2000ms\n    if (_duration < 2000) return yellow(duration)\n    // red for >= 2000ms\n    return red(bold(duration))\n  }\n\n  const getCleanName = (fileName: string) =>\n    fileName\n      // Trim off `static/`\n      .replace(/^static\\//, '')\n      // Re-add `static/` for root files\n      .replace(/^<buildId>/, 'static')\n      // Remove file hash\n      .replace(/(?:^|[.-])([0-9a-z]{6})[0-9a-z]{14}(?=\\.)/, '.$1')\n\n  // Check if we have a custom app.\n  const hasCustomApp = !!(\n    pagesDir && (await findPageFile(pagesDir, '/_app', pageExtensions, false))\n  )\n\n  // Collect all the symbols we use so we can print the icons out.\n  const usedSymbols = new Set()\n\n  const messages: [string, string, string, string, string][] = []\n\n  const stats = await computeFromManifest(\n    { build: buildManifest, app: appBuildManifest },\n    distPath,\n    gzipSize,\n    pageInfos\n  )\n\n  const printFileTree = async ({\n    list,\n    routerType,\n  }: {\n    list: ReadonlyArray<string>\n    routerType: ROUTER_TYPE\n  }) => {\n    const filteredPages = filterAndSortList(list, routerType, hasCustomApp)\n    if (filteredPages.length === 0) {\n      return\n    }\n\n    let showRevalidate = false\n    let showExpire = false\n\n    for (const page of filteredPages) {\n      const cacheControl = pageInfos.get(page)?.initialCacheControl\n\n      if (cacheControl?.revalidate) {\n        showRevalidate = true\n      }\n\n      if (cacheControl?.expire) {\n        showExpire = true\n      }\n\n      if (showRevalidate && showExpire) {\n        break\n      }\n    }\n\n    messages.push(\n      [\n        routerType === 'app' ? 'Route (app)' : 'Route (pages)',\n        'Size',\n        'First Load JS',\n        showRevalidate ? 'Revalidate' : '',\n        showExpire ? 'Expire' : '',\n      ].map((entry) => underline(entry)) as [\n        string,\n        string,\n        string,\n        string,\n        string,\n      ]\n    )\n\n    filteredPages.forEach((item, i, arr) => {\n      const border =\n        i === 0\n          ? arr.length === 1\n            ? '─'\n            : '┌'\n          : i === arr.length - 1\n            ? '└'\n            : '├'\n\n      const pageInfo = pageInfos.get(item)\n      const ampFirst = buildManifest.ampFirstPages.includes(item)\n      const totalDuration =\n        (pageInfo?.pageDuration || 0) +\n        (pageInfo?.ssgPageDurations?.reduce((a, b) => a + (b || 0), 0) || 0)\n\n      let symbol: string\n\n      if (item === '/_app' || item === '/_app.server') {\n        symbol = ' '\n      } else if (isEdgeRuntime(pageInfo?.runtime)) {\n        symbol = 'ƒ'\n      } else if (pageInfo?.isRoutePPREnabled) {\n        if (\n          // If the page has an empty prelude, then it's equivalent to a dynamic page\n          pageInfo?.hasEmptyPrelude ||\n          // ensure we don't mark dynamic paths that postponed as being dynamic\n          // since in this case we're able to partially prerender it\n          (pageInfo.isDynamicAppRoute && !pageInfo.hasPostponed)\n        ) {\n          symbol = 'ƒ'\n        } else if (!pageInfo?.hasPostponed) {\n          symbol = '○'\n        } else {\n          symbol = '◐'\n        }\n      } else if (pageInfo?.isStatic) {\n        symbol = '○'\n      } else if (pageInfo?.isSSG) {\n        symbol = '●'\n      } else {\n        symbol = 'ƒ'\n      }\n\n      usedSymbols.add(symbol)\n\n      messages.push([\n        `${border} ${symbol} ${item}${\n          totalDuration > MIN_DURATION\n            ? ` (${getPrettyDuration(totalDuration)})`\n            : ''\n        }`,\n        pageInfo\n          ? ampFirst\n            ? cyan('AMP')\n            : pageInfo.size >= 0\n              ? getPrettySize(pageInfo.size)\n              : ''\n          : '',\n        pageInfo\n          ? ampFirst\n            ? cyan('AMP')\n            : pageInfo.size >= 0\n              ? getPrettySize(pageInfo.totalSize, { strong: true })\n              : ''\n          : '',\n        showRevalidate && pageInfo?.initialCacheControl\n          ? formatRevalidate(pageInfo.initialCacheControl)\n          : '',\n        showExpire && pageInfo?.initialCacheControl\n          ? formatExpire(pageInfo.initialCacheControl)\n          : '',\n      ])\n\n      const uniqueCssFiles =\n        buildManifest.pages[item]?.filter(\n          (file) =>\n            file.endsWith('.css') &&\n            stats.router[routerType]?.unique.files.includes(file)\n        ) || []\n\n      if (uniqueCssFiles.length > 0) {\n        const contSymbol = i === arr.length - 1 ? ' ' : '├'\n\n        uniqueCssFiles.forEach((file, index, { length }) => {\n          const innerSymbol = index === length - 1 ? '└' : '├'\n          const size = stats.sizes.get(file)\n          messages.push([\n            `${contSymbol}   ${innerSymbol} ${getCleanName(file)}`,\n            typeof size === 'number' ? getPrettySize(size) : '',\n            '',\n            '',\n            '',\n          ])\n        })\n      }\n\n      if (pageInfo?.ssgPageRoutes?.length) {\n        const totalRoutes = pageInfo.ssgPageRoutes.length\n        const contSymbol = i === arr.length - 1 ? ' ' : '├'\n\n        let routes: { route: string; duration: number; avgDuration?: number }[]\n        if (\n          pageInfo.ssgPageDurations &&\n          pageInfo.ssgPageDurations.some((d) => d > MIN_DURATION)\n        ) {\n          const previewPages = totalRoutes === 8 ? 8 : Math.min(totalRoutes, 7)\n          const routesWithDuration = pageInfo.ssgPageRoutes\n            .map((route, idx) => ({\n              route,\n              duration: pageInfo.ssgPageDurations![idx] || 0,\n            }))\n            .sort(({ duration: a }, { duration: b }) =>\n              // Sort by duration\n              // keep too small durations in original order at the end\n              a <= MIN_DURATION && b <= MIN_DURATION ? 0 : b - a\n            )\n          routes = routesWithDuration.slice(0, previewPages)\n          const remainingRoutes = routesWithDuration.slice(previewPages)\n          if (remainingRoutes.length) {\n            const remaining = remainingRoutes.length\n            const avgDuration = Math.round(\n              remainingRoutes.reduce(\n                (total, { duration }) => total + duration,\n                0\n              ) / remainingRoutes.length\n            )\n            routes.push({\n              route: `[+${remaining} more paths]`,\n              duration: 0,\n              avgDuration,\n            })\n          }\n        } else {\n          const previewPages = totalRoutes === 4 ? 4 : Math.min(totalRoutes, 3)\n          routes = pageInfo.ssgPageRoutes\n            .slice(0, previewPages)\n            .map((route) => ({ route, duration: 0 }))\n          if (totalRoutes > previewPages) {\n            const remaining = totalRoutes - previewPages\n            routes.push({ route: `[+${remaining} more paths]`, duration: 0 })\n          }\n        }\n\n        routes.forEach(\n          ({ route, duration, avgDuration }, index, { length }) => {\n            const innerSymbol = index === length - 1 ? '└' : '├'\n\n            const initialCacheControl =\n              pageInfos.get(route)?.initialCacheControl\n\n            messages.push([\n              `${contSymbol}   ${innerSymbol} ${route}${\n                duration > MIN_DURATION\n                  ? ` (${getPrettyDuration(duration)})`\n                  : ''\n              }${\n                avgDuration && avgDuration > MIN_DURATION\n                  ? ` (avg ${getPrettyDuration(avgDuration)})`\n                  : ''\n              }`,\n              '',\n              '',\n              showRevalidate && initialCacheControl\n                ? formatRevalidate(initialCacheControl)\n                : '',\n              showExpire && initialCacheControl\n                ? formatExpire(initialCacheControl)\n                : '',\n            ])\n          }\n        )\n      }\n    })\n\n    const sharedFilesSize = stats.router[routerType]?.common.size.total\n\n    const sharedFiles = process.env.__NEXT_PRIVATE_DETERMINISTIC_BUILD_OUTPUT\n      ? []\n      : stats.router[routerType]?.common.files ?? []\n\n    messages.push([\n      '+ First Load JS shared by all',\n      typeof sharedFilesSize === 'number'\n        ? getPrettySize(sharedFilesSize, { strong: true })\n        : '',\n      '',\n      '',\n      '',\n    ])\n    const sharedCssFiles: string[] = []\n    const sharedJsChunks = [\n      ...sharedFiles\n        .filter((file) => {\n          if (file.endsWith('.css')) {\n            sharedCssFiles.push(file)\n            return false\n          }\n          return true\n        })\n        .map((e) => e.replace(buildId, '<buildId>'))\n        .sort(),\n      ...sharedCssFiles.map((e) => e.replace(buildId, '<buildId>')).sort(),\n    ]\n\n    // if the some chunk are less than 10kb or we don't know the size, we only show the total size of the rest\n    const tenKbLimit = 10 * 1000\n    let restChunkSize = 0\n    let restChunkCount = 0\n    sharedJsChunks.forEach((fileName, index, { length }) => {\n      const innerSymbol = index + restChunkCount === length - 1 ? '└' : '├'\n\n      const originalName = fileName.replace('<buildId>', buildId)\n      const cleanName = getCleanName(fileName)\n      const size = stats.sizes.get(originalName)\n\n      if (!size || size < tenKbLimit) {\n        restChunkCount++\n        restChunkSize += size || 0\n        return\n      }\n\n      messages.push([\n        `  ${innerSymbol} ${cleanName}`,\n        getPrettySize(size),\n        '',\n        '',\n        '',\n      ])\n    })\n\n    if (restChunkCount > 0) {\n      messages.push([\n        `  └ other shared chunks (total)`,\n        getPrettySize(restChunkSize),\n        '',\n        '',\n        '',\n      ])\n    }\n  }\n\n  // If enabled, then print the tree for the app directory.\n  if (lists.app && stats.router.app) {\n    await printFileTree({\n      routerType: 'app',\n      list: lists.app,\n    })\n\n    messages.push(['', '', '', '', ''])\n  }\n\n  pageInfos.set('/404', {\n    ...(pageInfos.get('/404') || pageInfos.get('/_error'))!,\n    isStatic: useStaticPages404,\n  })\n\n  // If there's no app /_notFound page present, then the 404 is still using the pages/404\n  if (\n    !lists.pages.includes('/404') &&\n    !lists.app?.includes(UNDERSCORE_NOT_FOUND_ROUTE)\n  ) {\n    lists.pages = [...lists.pages, '/404']\n  }\n\n  // Print the tree view for the pages directory.\n  await printFileTree({\n    routerType: 'pages',\n    list: lists.pages,\n  })\n\n  const middlewareInfo = middlewareManifest.middleware?.['/']\n  if (middlewareInfo?.files.length > 0) {\n    const middlewareSizes = await Promise.all(\n      middlewareInfo.files\n        .map((dep) => `${distPath}/${dep}`)\n        .map(gzipSize ? fsStatGzip : fsStat)\n    )\n\n    messages.push(['', '', '', '', ''])\n    messages.push([\n      'ƒ Middleware',\n      getPrettySize(sum(middlewareSizes), { strong: true }),\n      '',\n      '',\n      '',\n    ])\n  }\n\n  print(\n    textTable(messages, {\n      align: ['l', 'r', 'r', 'r', 'r'],\n      stringLength: (str) => stripAnsi(str).length,\n    })\n  )\n\n  const staticFunctionInfo =\n    lists.app && stats.router.app ? 'generateStaticParams' : 'getStaticProps'\n  print()\n  print(\n    textTable(\n      [\n        usedSymbols.has('○') && [\n          '○',\n          '(Static)',\n          'prerendered as static content',\n        ],\n        usedSymbols.has('●') && [\n          '●',\n          '(SSG)',\n          `prerendered as static HTML (uses ${cyan(staticFunctionInfo)})`,\n        ],\n        usedSymbols.has('◐') && [\n          '◐',\n          '(Partial Prerender)',\n          'prerendered as static HTML with dynamic server-streamed content',\n        ],\n        usedSymbols.has('ƒ') && ['ƒ', '(Dynamic)', `server-rendered on demand`],\n      ].filter((x) => x) as [string, string, string][],\n      {\n        align: ['l', 'l', 'l'],\n        stringLength: (str) => stripAnsi(str).length,\n      }\n    )\n  )\n\n  print()\n}\n\nexport function printCustomRoutes({\n  redirects,\n  rewrites,\n  headers,\n}: CustomRoutes) {\n  const printRoutes = (\n    routes: Redirect[] | Rewrite[] | Header[],\n    type: 'Redirects' | 'Rewrites' | 'Headers'\n  ) => {\n    const isRedirects = type === 'Redirects'\n    const isHeaders = type === 'Headers'\n    print(underline(type))\n\n    /*\n        ┌ source\n        ├ permanent/statusCode\n        └ destination\n     */\n    const routesStr = (routes as any[])\n      .map((route: { source: string }) => {\n        let routeStr = `┌ source: ${route.source}\\n`\n\n        if (!isHeaders) {\n          const r = route as Rewrite\n          routeStr += `${isRedirects ? '├' : '└'} destination: ${\n            r.destination\n          }\\n`\n        }\n        if (isRedirects) {\n          const r = route as Redirect\n          routeStr += `└ ${\n            r.statusCode\n              ? `status: ${r.statusCode}`\n              : `permanent: ${r.permanent}`\n          }\\n`\n        }\n\n        if (isHeaders) {\n          const r = route as Header\n          routeStr += `└ headers:\\n`\n\n          for (let i = 0; i < r.headers.length; i++) {\n            const header = r.headers[i]\n            const last = i === headers.length - 1\n\n            routeStr += `  ${last ? '└' : '├'} ${header.key}: ${header.value}\\n`\n          }\n        }\n\n        return routeStr\n      })\n      .join('\\n')\n\n    print(`${routesStr}\\n`)\n  }\n\n  print()\n  if (redirects.length) {\n    printRoutes(redirects, 'Redirects')\n  }\n  if (headers.length) {\n    printRoutes(headers, 'Headers')\n  }\n\n  const combinedRewrites = [\n    ...rewrites.beforeFiles,\n    ...rewrites.afterFiles,\n    ...rewrites.fallback,\n  ]\n  if (combinedRewrites.length) {\n    printRoutes(combinedRewrites, 'Rewrites')\n  }\n}\n\nexport async function getJsPageSizeInKb(\n  routerType: ROUTER_TYPE,\n  page: string,\n  distPath: string,\n  buildManifest: BuildManifest,\n  appBuildManifest?: AppBuildManifest,\n  gzipSize: boolean = true,\n  cachedStats?: ComputeFilesManifestResult\n): Promise<[number, number]> {\n  const pageManifest = routerType === 'pages' ? buildManifest : appBuildManifest\n  if (!pageManifest) {\n    throw new Error('expected appBuildManifest with an \"app\" pageType')\n  }\n\n  // Normalize appBuildManifest keys\n  if (routerType === 'app') {\n    pageManifest.pages = Object.entries(pageManifest.pages).reduce(\n      (acc: Record<string, string[]>, [key, value]) => {\n        const newKey = normalizeAppPath(key)\n        acc[newKey] = value as string[]\n        return acc\n      },\n      {}\n    )\n  }\n\n  // If stats was not provided, then compute it again.\n  const stats =\n    cachedStats ??\n    (await computeFromManifest(\n      { build: buildManifest, app: appBuildManifest },\n      distPath,\n      gzipSize\n    ))\n\n  const pageData = stats.router[routerType]\n  if (!pageData) {\n    // This error shouldn't happen and represents an error in Next.js.\n    throw new Error('expected \"app\" manifest data with an \"app\" pageType')\n  }\n\n  const pagePath =\n    routerType === 'pages'\n      ? denormalizePagePath(page)\n      : denormalizeAppPagePath(page)\n\n  const fnFilterJs = (entry: string) => entry.endsWith('.js')\n\n  const pageFiles = (pageManifest.pages[pagePath] ?? []).filter(fnFilterJs)\n  const appFiles = (pageManifest.pages['/_app'] ?? []).filter(fnFilterJs)\n\n  const fnMapRealPath = (dep: string) => `${distPath}/${dep}`\n\n  const allFilesReal = unique(pageFiles, appFiles).map(fnMapRealPath)\n  const selfFilesReal = difference(\n    // Find the files shared by the pages files and the unique files...\n    intersect(pageFiles, pageData.unique.files),\n    // but without the common files.\n    pageData.common.files\n  ).map(fnMapRealPath)\n\n  const getSize = gzipSize ? fsStatGzip : fsStat\n\n  // Try to get the file size from the page data if available, otherwise do a\n  // raw compute.\n  const getCachedSize = async (file: string) => {\n    const key = file.slice(distPath.length + 1)\n    const size: number | undefined = stats.sizes.get(key)\n\n    // If the size wasn't in the stats bundle, then get it from the file\n    // directly.\n    if (typeof size !== 'number') {\n      return getSize(file)\n    }\n\n    return size\n  }\n\n  try {\n    // Doesn't use `Promise.all`, as we'd double compute duplicate files. This\n    // function is memoized, so the second one will instantly resolve.\n    const allFilesSize = sum(await Promise.all(allFilesReal.map(getCachedSize)))\n    const selfFilesSize = sum(\n      await Promise.all(selfFilesReal.map(getCachedSize))\n    )\n\n    return [selfFilesSize, allFilesSize]\n  } catch {}\n  return [-1, -1]\n}\n\ntype PageIsStaticResult = {\n  isRoutePPREnabled?: boolean\n  isStatic?: boolean\n  isAmpOnly?: boolean\n  isHybridAmp?: boolean\n  hasServerProps?: boolean\n  hasStaticProps?: boolean\n  prerenderedRoutes: PrerenderedRoute[] | undefined\n  prerenderFallbackMode: FallbackMode | undefined\n  rootParamKeys: readonly string[] | undefined\n  isNextImageImported?: boolean\n  traceIncludes?: string[]\n  traceExcludes?: string[]\n  appConfig?: AppSegmentConfig\n}\n\nexport async function isPageStatic({\n  dir,\n  page,\n  distDir,\n  configFileName,\n  runtimeEnvConfig,\n  httpAgentOptions,\n  locales,\n  defaultLocale,\n  parentId,\n  pageRuntime,\n  edgeInfo,\n  pageType,\n  dynamicIO,\n  authInterrupts,\n  originalAppPath,\n  isrFlushToDisk,\n  maxMemoryCacheSize,\n  nextConfigOutput,\n  cacheHandler,\n  cacheHandlers,\n  cacheLifeProfiles,\n  pprConfig,\n  buildId,\n  sriEnabled,\n}: {\n  dir: string\n  page: string\n  distDir: string\n  dynamicIO: boolean\n  authInterrupts: boolean\n  configFileName: string\n  runtimeEnvConfig: any\n  httpAgentOptions: NextConfigComplete['httpAgentOptions']\n  locales?: readonly string[]\n  defaultLocale?: string\n  parentId?: any\n  edgeInfo?: any\n  pageType?: 'pages' | 'app'\n  pageRuntime?: ServerRuntime\n  originalAppPath?: string\n  isrFlushToDisk?: boolean\n  maxMemoryCacheSize?: number\n  cacheHandler?: string\n  cacheHandlers?: Record<string, string | undefined>\n  cacheLifeProfiles?: {\n    [profile: string]: import('../server/use-cache/cache-life').CacheLife\n  }\n  nextConfigOutput: 'standalone' | 'export' | undefined\n  pprConfig: ExperimentalPPRConfig | undefined\n  buildId: string\n  sriEnabled: boolean\n}): Promise<PageIsStaticResult> {\n  await createIncrementalCache({\n    cacheHandler,\n    cacheHandlers,\n    distDir,\n    dir,\n    flushToDisk: isrFlushToDisk,\n    cacheMaxMemorySize: maxMemoryCacheSize,\n  })\n\n  const isPageStaticSpan = trace('is-page-static-utils', parentId)\n  return isPageStaticSpan\n    .traceAsyncFn(async (): Promise<PageIsStaticResult> => {\n      require('../shared/lib/runtime-config.external').setConfig(\n        runtimeEnvConfig\n      )\n      setHttpClientAndAgentOptions({\n        httpAgentOptions,\n      })\n\n      let componentsResult: LoadComponentsReturnType\n      let prerenderedRoutes: PrerenderedRoute[] | undefined\n      let prerenderFallbackMode: FallbackMode | undefined\n      let appConfig: AppSegmentConfig = {}\n      let rootParamKeys: readonly string[] | undefined\n      let isClientComponent: boolean = false\n      const pathIsEdgeRuntime = isEdgeRuntime(pageRuntime)\n\n      if (pathIsEdgeRuntime) {\n        const runtime = await getRuntimeContext({\n          paths: edgeInfo.files.map((file: string) => path.join(distDir, file)),\n          edgeFunctionEntry: {\n            ...edgeInfo,\n            wasm: (edgeInfo.wasm ?? []).map((binding: AssetBinding) => ({\n              ...binding,\n              filePath: path.join(distDir, binding.filePath),\n            })),\n          },\n          name: edgeInfo.name,\n          useCache: true,\n          distDir,\n        })\n        const mod = (\n          await runtime.context._ENTRIES[`middleware_${edgeInfo.name}`]\n        ).ComponentMod\n\n        // This is not needed during require.\n        const buildManifest = {} as BuildManifest\n\n        isClientComponent = isClientReference(mod)\n        componentsResult = {\n          Component: mod.default,\n          Document: mod.Document,\n          App: mod.App,\n          routeModule: mod.routeModule,\n          page,\n          ComponentMod: mod,\n          pageConfig: mod.config || {},\n          buildManifest,\n          reactLoadableManifest: {},\n          getServerSideProps: mod.getServerSideProps,\n          getStaticPaths: mod.getStaticPaths,\n          getStaticProps: mod.getStaticProps,\n        }\n      } else {\n        componentsResult = await loadComponents({\n          distDir,\n          page: originalAppPath || page,\n          isAppPath: pageType === 'app',\n          isDev: false,\n          sriEnabled,\n        })\n      }\n      const Comp = componentsResult.Component as NextComponentType | undefined\n\n      const routeModule: RouteModule = componentsResult.routeModule\n\n      let isRoutePPREnabled: boolean = false\n\n      if (pageType === 'app') {\n        const ComponentMod: AppPageModule = componentsResult.ComponentMod\n\n        isClientComponent = isClientReference(componentsResult.ComponentMod)\n\n        let segments\n        try {\n          segments = await collectSegments(componentsResult)\n        } catch (err) {\n          throw new Error(`Failed to collect configuration for ${page}`, {\n            cause: err,\n          })\n        }\n\n        appConfig = reduceAppConfig(segments)\n\n        if (appConfig.dynamic === 'force-static' && pathIsEdgeRuntime) {\n          Log.warn(\n            `Page \"${page}\" is using runtime = 'edge' which is currently incompatible with dynamic = 'force-static'. Please remove either \"runtime\" or \"force-static\" for correct behavior`\n          )\n        }\n\n        rootParamKeys = collectRootParamKeys(componentsResult)\n\n        // A page supports partial prerendering if it is an app page and either\n        // the whole app has PPR enabled or this page has PPR enabled when we're\n        // in incremental mode.\n        isRoutePPREnabled =\n          routeModule.definition.kind === RouteKind.APP_PAGE &&\n          !isInterceptionRouteAppPath(page) &&\n          checkIsRoutePPREnabled(pprConfig, appConfig)\n\n        // If force dynamic was set and we don't have PPR enabled, then set the\n        // revalidate to 0.\n        // TODO: (PPR) remove this once PPR is enabled by default\n        if (appConfig.dynamic === 'force-dynamic' && !isRoutePPREnabled) {\n          appConfig.revalidate = 0\n        }\n\n        // If the page is dynamic and we're not in edge runtime, then we need to\n        // build the static paths. The edge runtime doesn't support static\n        // paths.\n        if (isDynamicRoute(page) && !pathIsEdgeRuntime) {\n          ;({ prerenderedRoutes, fallbackMode: prerenderFallbackMode } =\n            await buildAppStaticPaths({\n              dir,\n              page,\n              dynamicIO,\n              authInterrupts,\n              segments,\n              distDir,\n              requestHeaders: {},\n              isrFlushToDisk,\n              maxMemoryCacheSize,\n              cacheHandler,\n              cacheLifeProfiles,\n              ComponentMod,\n              nextConfigOutput,\n              isRoutePPREnabled,\n              buildId,\n              rootParamKeys,\n            }))\n        }\n      } else {\n        if (!Comp || !isValidElementType(Comp) || typeof Comp === 'string') {\n          throw new Error('INVALID_DEFAULT_EXPORT')\n        }\n      }\n\n      const hasGetInitialProps = !!Comp?.getInitialProps\n      const hasStaticProps = !!componentsResult.getStaticProps\n      const hasStaticPaths = !!componentsResult.getStaticPaths\n      const hasServerProps = !!componentsResult.getServerSideProps\n\n      // A page cannot be prerendered _and_ define a data requirement. That's\n      // contradictory!\n      if (hasGetInitialProps && hasStaticProps) {\n        throw new Error(SSG_GET_INITIAL_PROPS_CONFLICT)\n      }\n\n      if (hasGetInitialProps && hasServerProps) {\n        throw new Error(SERVER_PROPS_GET_INIT_PROPS_CONFLICT)\n      }\n\n      if (hasStaticProps && hasServerProps) {\n        throw new Error(SERVER_PROPS_SSG_CONFLICT)\n      }\n\n      const pageIsDynamic = isDynamicRoute(page)\n      // A page cannot have static parameters if it is not a dynamic page.\n      if (hasStaticProps && hasStaticPaths && !pageIsDynamic) {\n        throw new Error(\n          `getStaticPaths can only be used with dynamic pages, not '${page}'.` +\n            `\\nLearn more: https://nextjs.org/docs/routing/dynamic-routes`\n        )\n      }\n\n      if (hasStaticProps && pageIsDynamic && !hasStaticPaths) {\n        throw new Error(\n          `getStaticPaths is required for dynamic SSG pages and is missing for '${page}'.` +\n            `\\nRead more: https://nextjs.org/docs/messages/invalid-getstaticpaths-value`\n        )\n      }\n\n      if (hasStaticProps && hasStaticPaths) {\n        ;({ prerenderedRoutes, fallbackMode: prerenderFallbackMode } =\n          await buildPagesStaticPaths({\n            page,\n            locales,\n            defaultLocale,\n            configFileName,\n            getStaticPaths: componentsResult.getStaticPaths!,\n          }))\n      }\n\n      const isNextImageImported = (globalThis as any).__NEXT_IMAGE_IMPORTED\n      const config: PageConfig = isClientComponent\n        ? {}\n        : componentsResult.pageConfig\n\n      let isStatic = false\n      if (!hasStaticProps && !hasGetInitialProps && !hasServerProps) {\n        isStatic = true\n      }\n\n      // When PPR is enabled, any route may be completely static, so\n      // mark this route as static.\n      if (isRoutePPREnabled) {\n        isStatic = true\n      }\n\n      return {\n        isStatic,\n        isRoutePPREnabled,\n        isHybridAmp: config.amp === 'hybrid',\n        isAmpOnly: config.amp === true,\n        prerenderFallbackMode,\n        prerenderedRoutes,\n        rootParamKeys,\n        hasStaticProps,\n        hasServerProps,\n        isNextImageImported,\n        appConfig,\n      }\n    })\n    .catch((err) => {\n      if (err.message === 'INVALID_DEFAULT_EXPORT') {\n        throw err\n      }\n      console.error(err)\n      throw new Error(`Failed to collect page data for ${page}`)\n    })\n}\n\ntype ReducedAppConfig = Pick<\n  AppSegmentConfig,\n  | 'revalidate'\n  | 'dynamic'\n  | 'fetchCache'\n  | 'preferredRegion'\n  | 'experimental_ppr'\n  | 'runtime'\n  | 'maxDuration'\n>\n\n/**\n * Collect the app config from the generate param segments. This only gets a\n * subset of the config options.\n *\n * @param segments the generate param segments\n * @returns the reduced app config\n */\nexport function reduceAppConfig(\n  segments: Pick<AppSegment, 'config'>[]\n): ReducedAppConfig {\n  const config: ReducedAppConfig = {}\n\n  for (const segment of segments) {\n    const {\n      dynamic,\n      fetchCache,\n      preferredRegion,\n      revalidate,\n      experimental_ppr,\n      runtime,\n      maxDuration,\n    } = segment.config || {}\n\n    // TODO: should conflicting configs here throw an error\n    // e.g. if layout defines one region but page defines another\n\n    if (typeof preferredRegion !== 'undefined') {\n      config.preferredRegion = preferredRegion\n    }\n\n    if (typeof dynamic !== 'undefined') {\n      config.dynamic = dynamic\n    }\n\n    if (typeof fetchCache !== 'undefined') {\n      config.fetchCache = fetchCache\n    }\n\n    if (typeof revalidate !== 'undefined') {\n      config.revalidate = revalidate\n    }\n\n    // Any revalidate number overrides false, and shorter revalidate overrides\n    // longer (initially).\n    if (\n      typeof revalidate === 'number' &&\n      (typeof config.revalidate !== 'number' || revalidate < config.revalidate)\n    ) {\n      config.revalidate = revalidate\n    }\n\n    // If partial prerendering has been set, only override it if the current\n    // value is provided as it's resolved from root layout to leaf page.\n    if (typeof experimental_ppr !== 'undefined') {\n      config.experimental_ppr = experimental_ppr\n    }\n\n    if (typeof runtime !== 'undefined') {\n      config.runtime = runtime\n    }\n\n    if (typeof maxDuration !== 'undefined') {\n      config.maxDuration = maxDuration\n    }\n  }\n\n  return config\n}\n\nexport async function hasCustomGetInitialProps({\n  page,\n  distDir,\n  runtimeEnvConfig,\n  checkingApp,\n  sriEnabled,\n}: {\n  page: string\n  distDir: string\n  runtimeEnvConfig: any\n  checkingApp: boolean\n  sriEnabled: boolean\n}): Promise<boolean> {\n  require('../shared/lib/runtime-config.external').setConfig(runtimeEnvConfig)\n\n  const components = await loadComponents({\n    distDir,\n    page: page,\n    isAppPath: false,\n    isDev: false,\n    sriEnabled,\n  })\n  let mod = components.ComponentMod\n\n  if (checkingApp) {\n    mod = (await mod._app) || mod.default || mod\n  } else {\n    mod = mod.default || mod\n  }\n  mod = await mod\n  return mod.getInitialProps !== mod.origGetInitialProps\n}\n\nexport async function getDefinedNamedExports({\n  page,\n  distDir,\n  runtimeEnvConfig,\n  sriEnabled,\n}: {\n  page: string\n  distDir: string\n  runtimeEnvConfig: any\n  sriEnabled: boolean\n}): Promise<ReadonlyArray<string>> {\n  require('../shared/lib/runtime-config.external').setConfig(runtimeEnvConfig)\n  const components = await loadComponents({\n    distDir,\n    page: page,\n    isAppPath: false,\n    isDev: false,\n    sriEnabled,\n  })\n\n  return Object.keys(components.ComponentMod).filter((key) => {\n    return typeof components.ComponentMod[key] !== 'undefined'\n  })\n}\n\nexport function detectConflictingPaths(\n  combinedPages: string[],\n  ssgPages: Set<string>,\n  additionalGeneratedSSGPaths: Map<string, string[]>\n) {\n  const conflictingPaths = new Map<\n    string,\n    Array<{\n      path: string\n      page: string\n    }>\n  >()\n\n  const dynamicSsgPages = [...ssgPages].filter((page) => isDynamicRoute(page))\n  const additionalSsgPathsByPath: {\n    [page: string]: { [path: string]: string }\n  } = {}\n\n  additionalGeneratedSSGPaths.forEach((paths, pathsPage) => {\n    additionalSsgPathsByPath[pathsPage] ||= {}\n    paths.forEach((curPath) => {\n      const currentPath = curPath.toLowerCase()\n      additionalSsgPathsByPath[pathsPage][currentPath] = curPath\n    })\n  })\n\n  additionalGeneratedSSGPaths.forEach((paths, pathsPage) => {\n    paths.forEach((curPath) => {\n      const lowerPath = curPath.toLowerCase()\n      let conflictingPage = combinedPages.find(\n        (page) => page.toLowerCase() === lowerPath\n      )\n\n      if (conflictingPage) {\n        conflictingPaths.set(lowerPath, [\n          { path: curPath, page: pathsPage },\n          { path: conflictingPage, page: conflictingPage },\n        ])\n      } else {\n        let conflictingPath: string | undefined\n\n        conflictingPage = dynamicSsgPages.find((page) => {\n          if (page === pathsPage) return false\n\n          conflictingPath =\n            additionalGeneratedSSGPaths.get(page) == null\n              ? undefined\n              : additionalSsgPathsByPath[page][lowerPath]\n          return conflictingPath\n        })\n\n        if (conflictingPage && conflictingPath) {\n          conflictingPaths.set(lowerPath, [\n            { path: curPath, page: pathsPage },\n            { path: conflictingPath, page: conflictingPage },\n          ])\n        }\n      }\n    })\n  })\n\n  if (conflictingPaths.size > 0) {\n    let conflictingPathsOutput = ''\n\n    conflictingPaths.forEach((pathItems) => {\n      pathItems.forEach((pathItem, idx) => {\n        const isDynamic = pathItem.page !== pathItem.path\n\n        if (idx > 0) {\n          conflictingPathsOutput += 'conflicts with '\n        }\n\n        conflictingPathsOutput += `path: \"${pathItem.path}\"${\n          isDynamic ? ` from page: \"${pathItem.page}\" ` : ' '\n        }`\n      })\n      conflictingPathsOutput += '\\n'\n    })\n\n    Log.error(\n      'Conflicting paths returned from getStaticPaths, paths must be unique per page.\\n' +\n        'See more info here: https://nextjs.org/docs/messages/conflicting-ssg-paths\\n\\n' +\n        conflictingPathsOutput\n    )\n    process.exit(1)\n  }\n}\n\nexport async function copyTracedFiles(\n  dir: string,\n  distDir: string,\n  pageKeys: readonly string[],\n  appPageKeys: readonly string[] | undefined,\n  tracingRoot: string,\n  serverConfig: NextConfigComplete,\n  middlewareManifest: MiddlewareManifest,\n  hasNodeMiddleware: boolean,\n  hasInstrumentationHook: boolean,\n  staticPages: Set<string>\n) {\n  const outputPath = path.join(distDir, 'standalone')\n  let moduleType = false\n  const nextConfig = {\n    ...serverConfig,\n    distDir: `./${path.relative(dir, distDir)}`,\n  }\n  try {\n    const packageJsonPath = path.join(distDir, '../package.json')\n    const packageJsonContent = await fs.readFile(packageJsonPath, 'utf8')\n    const packageJson = JSON.parse(packageJsonContent)\n    moduleType = packageJson.type === 'module'\n\n    // we always copy the package.json to the standalone\n    // folder to ensure any resolving logic is maintained\n    const packageJsonOutputPath = path.join(\n      outputPath,\n      path.relative(tracingRoot, dir),\n      'package.json'\n    )\n    await fs.mkdir(path.dirname(packageJsonOutputPath), { recursive: true })\n    await fs.writeFile(packageJsonOutputPath, packageJsonContent)\n  } catch {}\n  const copiedFiles = new Set()\n  await fs.rm(outputPath, { recursive: true, force: true })\n\n  async function handleTraceFiles(traceFilePath: string) {\n    const traceData = JSON.parse(await fs.readFile(traceFilePath, 'utf8')) as {\n      files: string[]\n    }\n    const copySema = new Sema(10, { capacity: traceData.files.length })\n    const traceFileDir = path.dirname(traceFilePath)\n\n    await Promise.all(\n      traceData.files.map(async (relativeFile) => {\n        await copySema.acquire()\n\n        const tracedFilePath = path.join(traceFileDir, relativeFile)\n        const fileOutputPath = path.join(\n          outputPath,\n          path.relative(tracingRoot, tracedFilePath)\n        )\n\n        if (!copiedFiles.has(fileOutputPath)) {\n          copiedFiles.add(fileOutputPath)\n\n          await fs.mkdir(path.dirname(fileOutputPath), { recursive: true })\n          const symlink = await fs.readlink(tracedFilePath).catch(() => null)\n\n          if (symlink) {\n            try {\n              await fs.symlink(symlink, fileOutputPath)\n            } catch (e: any) {\n              if (e.code !== 'EEXIST') {\n                throw e\n              }\n            }\n          } else {\n            await fs.copyFile(tracedFilePath, fileOutputPath)\n          }\n        }\n\n        await copySema.release()\n      })\n    )\n  }\n\n  async function handleEdgeFunction(page: EdgeFunctionDefinition) {\n    async function handleFile(file: string) {\n      const originalPath = path.join(distDir, file)\n      const fileOutputPath = path.join(\n        outputPath,\n        path.relative(tracingRoot, distDir),\n        file\n      )\n      await fs.mkdir(path.dirname(fileOutputPath), { recursive: true })\n      await fs.copyFile(originalPath, fileOutputPath)\n    }\n    await Promise.all([\n      page.files.map(handleFile),\n      page.wasm?.map((file) => handleFile(file.filePath)),\n      page.assets?.map((file) => handleFile(file.filePath)),\n    ])\n  }\n\n  const edgeFunctionHandlers: Promise<any>[] = []\n\n  for (const middleware of Object.values(middlewareManifest.middleware)) {\n    if (isMiddlewareFilename(middleware.name)) {\n      edgeFunctionHandlers.push(handleEdgeFunction(middleware))\n    }\n  }\n\n  for (const page of Object.values(middlewareManifest.functions)) {\n    edgeFunctionHandlers.push(handleEdgeFunction(page))\n  }\n\n  await Promise.all(edgeFunctionHandlers)\n\n  for (const page of pageKeys) {\n    if (middlewareManifest.functions.hasOwnProperty(page)) {\n      continue\n    }\n    const route = normalizePagePath(page)\n\n    if (staticPages.has(route)) {\n      continue\n    }\n\n    const pageFile = path.join(\n      distDir,\n      'server',\n      'pages',\n      `${normalizePagePath(page)}.js`\n    )\n    const pageTraceFile = `${pageFile}.nft.json`\n    await handleTraceFiles(pageTraceFile).catch((err) => {\n      if (err.code !== 'ENOENT' || (page !== '/404' && page !== '/500')) {\n        Log.warn(`Failed to copy traced files for ${pageFile}`, err)\n      }\n    })\n  }\n\n  if (hasNodeMiddleware) {\n    const middlewareFile = path.join(distDir, 'server', 'middleware.js')\n    const middlewareTrace = `${middlewareFile}.nft.json`\n    await handleTraceFiles(middlewareTrace)\n  }\n\n  if (appPageKeys) {\n    for (const page of appPageKeys) {\n      if (middlewareManifest.functions.hasOwnProperty(page)) {\n        continue\n      }\n      const pageFile = path.join(distDir, 'server', 'app', `${page}.js`)\n      const pageTraceFile = `${pageFile}.nft.json`\n      await handleTraceFiles(pageTraceFile).catch((err) => {\n        Log.warn(`Failed to copy traced files for ${pageFile}`, err)\n      })\n    }\n  }\n\n  if (hasInstrumentationHook) {\n    await handleTraceFiles(\n      path.join(distDir, 'server', 'instrumentation.js.nft.json')\n    )\n  }\n\n  await handleTraceFiles(path.join(distDir, 'next-server.js.nft.json'))\n  const serverOutputPath = path.join(\n    outputPath,\n    path.relative(tracingRoot, dir),\n    'server.js'\n  )\n  await fs.mkdir(path.dirname(serverOutputPath), { recursive: true })\n\n  await fs.writeFile(\n    serverOutputPath,\n    `${\n      moduleType\n        ? `performance.mark('next-start');\nimport path from 'path'\nimport { fileURLToPath } from 'url'\nimport module from 'module'\nconst require = module.createRequire(import.meta.url)\nconst __dirname = fileURLToPath(new URL('.', import.meta.url))\n`\n        : `const path = require('path')`\n    }\n\nconst dir = path.join(__dirname)\n\nprocess.env.NODE_ENV = 'production'\nprocess.chdir(__dirname)\n\nconst currentPort = parseInt(process.env.PORT, 10) || 3000\nconst hostname = process.env.HOSTNAME || '0.0.0.0'\n\nlet keepAliveTimeout = parseInt(process.env.KEEP_ALIVE_TIMEOUT, 10)\nconst nextConfig = ${JSON.stringify(nextConfig)}\n\nprocess.env.__NEXT_PRIVATE_STANDALONE_CONFIG = JSON.stringify(nextConfig)\n\nrequire('next')\nconst { startServer } = require('next/dist/server/lib/start-server')\n\nif (\n  Number.isNaN(keepAliveTimeout) ||\n  !Number.isFinite(keepAliveTimeout) ||\n  keepAliveTimeout < 0\n) {\n  keepAliveTimeout = undefined\n}\n\nstartServer({\n  dir,\n  isDev: false,\n  config: nextConfig,\n  hostname,\n  port: currentPort,\n  allowRetry: false,\n  keepAliveTimeout,\n}).catch((err) => {\n  console.error(err);\n  process.exit(1);\n});`\n  )\n}\n\nexport function isReservedPage(page: string) {\n  return RESERVED_PAGE.test(page)\n}\n\nexport function isAppBuiltinNotFoundPage(page: string) {\n  return /next[\\\\/]dist[\\\\/]client[\\\\/]components[\\\\/]not-found-error/.test(\n    page\n  )\n}\n\nexport function isCustomErrorPage(page: string) {\n  return page === '/404' || page === '/500'\n}\n\nexport function isMiddlewareFile(file: string) {\n  return (\n    file === `/${MIDDLEWARE_FILENAME}` || file === `/src/${MIDDLEWARE_FILENAME}`\n  )\n}\n\nexport function isInstrumentationHookFile(file: string) {\n  return (\n    file === `/${INSTRUMENTATION_HOOK_FILENAME}` ||\n    file === `/src/${INSTRUMENTATION_HOOK_FILENAME}`\n  )\n}\n\nexport function getPossibleInstrumentationHookFilenames(\n  folder: string,\n  extensions: string[]\n) {\n  const files = []\n  for (const extension of extensions) {\n    files.push(\n      path.join(folder, `${INSTRUMENTATION_HOOK_FILENAME}.${extension}`),\n      path.join(folder, `src`, `${INSTRUMENTATION_HOOK_FILENAME}.${extension}`)\n    )\n  }\n\n  return files\n}\n\nexport function getPossibleMiddlewareFilenames(\n  folder: string,\n  extensions: string[]\n) {\n  return extensions.map((extension) =>\n    path.join(folder, `${MIDDLEWARE_FILENAME}.${extension}`)\n  )\n}\n\nexport class NestedMiddlewareError extends Error {\n  constructor(\n    nestedFileNames: string[],\n    mainDir: string,\n    pagesOrAppDir: string\n  ) {\n    super(\n      `Nested Middleware is not allowed, found:\\n` +\n        `${nestedFileNames.map((file) => `pages${file}`).join('\\n')}\\n` +\n        `Please move your code to a single file at ${path.join(\n          path.posix.sep,\n          path.relative(mainDir, path.resolve(pagesOrAppDir, '..')),\n          'middleware'\n        )} instead.\\n` +\n        `Read More - https://nextjs.org/docs/messages/nested-middleware`\n    )\n  }\n}\n\nexport function getSupportedBrowsers(\n  dir: string,\n  isDevelopment: boolean\n): string[] {\n  let browsers: any\n  try {\n    const browsersListConfig = browserslist.loadConfig({\n      path: dir,\n      env: isDevelopment ? 'development' : 'production',\n    })\n    // Running `browserslist` resolves `extends` and other config features into a list of browsers\n    if (browsersListConfig && browsersListConfig.length > 0) {\n      browsers = browserslist(browsersListConfig)\n    }\n  } catch {}\n\n  // When user has browserslist use that target\n  if (browsers && browsers.length > 0) {\n    return browsers\n  }\n\n  // Uses modern browsers as the default.\n  return MODERN_BROWSERSLIST_TARGET\n}\n\nexport function isWebpackServerOnlyLayer(\n  layer: WebpackLayerName | null | undefined\n): boolean {\n  return Boolean(\n    layer && WEBPACK_LAYERS.GROUP.serverOnly.includes(layer as any)\n  )\n}\n\nexport function isWebpackClientOnlyLayer(\n  layer: WebpackLayerName | null | undefined\n): boolean {\n  return Boolean(\n    layer && WEBPACK_LAYERS.GROUP.clientOnly.includes(layer as any)\n  )\n}\n\nexport function isWebpackDefaultLayer(\n  layer: WebpackLayerName | null | undefined\n): boolean {\n  return (\n    layer === null ||\n    layer === undefined ||\n    layer === WEBPACK_LAYERS.pagesDirBrowser ||\n    layer === WEBPACK_LAYERS.pagesDirEdge ||\n    layer === WEBPACK_LAYERS.pagesDirNode\n  )\n}\n\nexport function isWebpackBundledLayer(\n  layer: WebpackLayerName | null | undefined\n): boolean {\n  return Boolean(layer && WEBPACK_LAYERS.GROUP.bundled.includes(layer as any))\n}\n\nexport function isWebpackAppPagesLayer(\n  layer: WebpackLayerName | null | undefined\n): boolean {\n  return Boolean(layer && WEBPACK_LAYERS.GROUP.appPages.includes(layer as any))\n}\n\nexport function collectMeta({\n  status,\n  headers,\n}: {\n  status?: number\n  headers?: OutgoingHttpHeaders\n}): {\n  status?: number\n  headers?: Record<string, string>\n} {\n  const meta: {\n    status?: number\n    headers?: Record<string, string>\n  } = {}\n\n  if (status !== 200) {\n    meta.status = status\n  }\n\n  if (headers && Object.keys(headers).length) {\n    meta.headers = {}\n\n    // normalize header values as initialHeaders\n    // must be Record<string, string>\n    for (const key in headers) {\n      // set-cookie is already handled - the middleware cookie setting case\n      // isn't needed for the prerender manifest since it can't read cookies\n      if (key === 'x-middleware-set-cookie') continue\n\n      let value = headers[key]\n\n      if (Array.isArray(value)) {\n        if (key === 'set-cookie') {\n          value = value.join(',')\n        } else {\n          value = value[value.length - 1]\n        }\n      }\n\n      if (typeof value === 'string') {\n        meta.headers[key] = value\n      }\n    }\n  }\n\n  return meta\n}\n"], "names": ["green", "yellow", "red", "cyan", "white", "bold", "underline", "getGzipSize", "textTable", "path", "promises", "fs", "isValidElementType", "stripAnsi", "browserslist", "SSG_GET_INITIAL_PROPS_CONFLICT", "SERVER_PROPS_GET_INIT_PROPS_CONFLICT", "SERVER_PROPS_SSG_CONFLICT", "MIDDLEWARE_FILENAME", "INSTRUMENTATION_HOOK_FILENAME", "WEBPACK_LAYERS", "MODERN_BROWSERSLIST_TARGET", "UNDERSCORE_NOT_FOUND_ROUTE", "prettyBytes", "isDynamicRoute", "findPageFile", "isEdgeRuntime", "Log", "loadComponents", "trace", "setHttpClientAndAgentOptions", "<PERSON><PERSON>", "denormalizePagePath", "normalizePagePath", "getRuntimeContext", "isClientReference", "normalizeAppPath", "denormalizeAppPagePath", "RouteKind", "isInterceptionRouteAppPath", "checkIsRoutePPREnabled", "collectSegments", "createIncrementalCache", "collectRootParamKeys", "buildAppStaticPaths", "buildPagesStaticPaths", "formatExpire", "formatRevalidate", "print", "console", "log", "RESERVED_PAGE", "fileGzipStats", "fsStatGzip", "file", "cached", "fileSize", "stat", "size", "fileStats", "fsStat", "unique", "main", "sub", "Set", "difference", "a", "b", "filter", "x", "has", "intersect", "sum", "reduce", "cachedBuildManifest", "cachedAppBuildManifest", "lastCompute", "lastComputePageInfo", "computeFromManifest", "manifests", "distPath", "gzipSize", "pageInfos", "files", "Object", "is", "build", "app", "countBuildFiles", "map", "key", "manifest", "set", "Infinity", "get", "pages", "each", "Map", "expected", "pageInfo", "isHybridAmp", "getSize", "stats", "Promise", "all", "keys", "f", "join", "groupFiles", "listing", "entries", "shapeGroup", "group", "acc", "push", "total", "len", "common", "router", "undefined", "sizes", "isMiddlewareFilename", "isInstrumentationHookFilename", "filterAndSortList", "list", "routeType", "hasCustomApp", "e", "slice", "sort", "localeCompare", "collectRoutesUsingEdgeRuntime", "input", "routesUsingEdgeRuntime", "route", "info", "runtime", "printTreeView", "lists", "buildId", "pagesDir", "pageExtensions", "buildManifest", "appBuildManifest", "middlewareManifest", "useStaticPages404", "getPrettySize", "_size", "strong", "process", "env", "__NEXT_PRIVATE_DETERMINISTIC_BUILD_OUTPUT", "MIN_DURATION", "getPrettyDuration", "_duration", "duration", "getCleanName", "fileName", "replace", "usedSymbols", "messages", "printFileTree", "routerType", "filteredPages", "length", "showRevalidate", "showExpire", "page", "cacheControl", "initialCacheControl", "revalidate", "expire", "entry", "for<PERSON>ach", "item", "i", "arr", "border", "ampFirs<PERSON>", "ampFirstPages", "includes", "totalDuration", "pageDuration", "ssgPageDurations", "symbol", "isRoutePPREnabled", "hasEmptyPrelude", "isDynamicAppRoute", "hasPostponed", "isStatic", "isSSG", "add", "totalSize", "uniqueCssFiles", "endsWith", "contSymbol", "index", "innerSymbol", "ssgPageRoutes", "totalRoutes", "routes", "some", "d", "previewPages", "Math", "min", "routesWithDuration", "idx", "remainingRoutes", "remaining", "avgDuration", "round", "sharedFilesSize", "sharedFiles", "sharedCssFiles", "sharedJsChunks", "tenKbLimit", "restChunkSize", "restChunkCount", "originalName", "cleanName", "middlewareInfo", "middleware", "middlewareSizes", "dep", "align", "stringLength", "str", "staticFunctionInfo", "printCustomRoutes", "redirects", "rewrites", "headers", "printRoutes", "type", "isRedirects", "isHeaders", "routesStr", "routeStr", "source", "r", "destination", "statusCode", "permanent", "header", "last", "value", "combinedRewrites", "beforeFiles", "afterFiles", "fallback", "getJsPageSizeInKb", "cachedStats", "pageManifest", "Error", "new<PERSON>ey", "pageData", "pagePath", "fnFilterJs", "pageFiles", "appFiles", "fnMapRealPath", "allFilesReal", "selfFilesReal", "getCachedSize", "allFilesSize", "selfFilesSize", "isPageStatic", "dir", "distDir", "configFileName", "runtimeEnvConfig", "httpAgentOptions", "locales", "defaultLocale", "parentId", "pageRuntime", "edgeInfo", "pageType", "dynamicIO", "authInterrupts", "originalAppPath", "isrFlushToDisk", "maxMemoryCacheSize", "nextConfigOutput", "cache<PERSON><PERSON><PERSON>", "cacheHandlers", "cacheLifeProfiles", "pprConfig", "sriEnabled", "flushToDisk", "cacheMaxMemorySize", "isPageStaticSpan", "traceAsyncFn", "require", "setConfig", "componentsResult", "prerenderedRoutes", "prerenderFallbackMode", "appConfig", "rootParamKeys", "isClientComponent", "pathIsEdgeRuntime", "paths", "edgeFunctionEntry", "wasm", "binding", "filePath", "name", "useCache", "mod", "context", "_ENTRIES", "ComponentMod", "Component", "default", "Document", "App", "routeModule", "pageConfig", "config", "reactLoadableManifest", "getServerSideProps", "getStaticPaths", "getStaticProps", "isAppPath", "isDev", "Comp", "segments", "err", "cause", "reduceAppConfig", "dynamic", "warn", "definition", "kind", "APP_PAGE", "fallbackMode", "requestHeaders", "hasGetInitialProps", "getInitialProps", "hasStaticProps", "hasStaticPaths", "hasServerProps", "pageIsDynamic", "isNextImageImported", "globalThis", "__NEXT_IMAGE_IMPORTED", "amp", "isAmpOnly", "catch", "message", "error", "segment", "fetchCache", "preferredRegion", "experimental_ppr", "maxDuration", "hasCustomGetInitialProps", "checkingApp", "components", "_app", "origGetInitialProps", "getDefinedNamedExports", "detectConflictingPaths", "combinedPages", "ssgPages", "additionalGeneratedSSGPaths", "conflictingPaths", "dynamicSsgPages", "additionalSsgPathsByPath", "pathsPage", "curPath", "currentPath", "toLowerCase", "lowerPath", "conflictingPage", "find", "conflicting<PERSON><PERSON>", "conflictingPathsOutput", "pathItems", "pathItem", "isDynamic", "exit", "copyTracedFiles", "pageKeys", "appPageKeys", "tracingRoot", "serverConfig", "hasNodeMiddleware", "hasInstrumentationHook", "staticPages", "outputPath", "moduleType", "nextConfig", "relative", "packageJsonPath", "packageJsonContent", "readFile", "packageJson", "JSON", "parse", "packageJsonOutputPath", "mkdir", "dirname", "recursive", "writeFile", "copiedFiles", "rm", "force", "handleTraceFiles", "traceFilePath", "traceData", "copySema", "capacity", "traceFileDir", "relativeFile", "acquire", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "fileOutputPath", "symlink", "readlink", "code", "copyFile", "release", "handleEdgeFunction", "handleFile", "originalPath", "assets", "edgeFunctionHandlers", "values", "functions", "hasOwnProperty", "pageFile", "pageTraceFile", "middlewareFile", "middlewareTrace", "serverOutputPath", "stringify", "isReservedPage", "test", "isAppBuiltinNotFoundPage", "isCustomErrorPage", "isMiddlewareFile", "isInstrumentationHookFile", "getPossibleInstrumentationHookFilenames", "folder", "extensions", "extension", "getPossibleMiddlewareFilenames", "NestedMiddlewareError", "constructor", "nestedFileNames", "mainDir", "pagesOrAppDir", "posix", "sep", "resolve", "getSupportedBrowsers", "isDevelopment", "browsers", "browsersListConfig", "loadConfig", "isWebpackServerOnlyLayer", "layer", "Boolean", "GROUP", "serverOnly", "isWebpackClientOnlyLayer", "clientOnly", "isWebpackDefaultLayer", "pagesDirBrowser", "pagesDirEdge", "pagesDirNode", "isWebpackBundledLayer", "bundled", "isWebpackAppPagesLayer", "appPages", "collectMeta", "status", "meta", "Array", "isArray"], "mappings": "AAqBA,OAAO,yBAAwB;AAC/B,OAAO,iCAAgC;AACvC,OAAO,6BAA4B;AAEnC,SACEA,KAAK,EACLC,MAAM,EACNC,GAAG,EACHC,IAAI,EACJC,KAAK,EACLC,IAAI,EACJC,SAAS,QACJ,oBAAmB;AAC1B,OAAOC,iBAAiB,+BAA8B;AACtD,OAAOC,eAAe,gCAA+B;AACrD,OAAOC,UAAU,OAAM;AACvB,SAASC,YAAYC,EAAE,QAAQ,KAAI;AACnC,SAASC,kBAAkB,QAAQ,8BAA6B;AAChE,OAAOC,eAAe,gCAA+B;AACrD,OAAOC,kBAAkB,kCAAiC;AAC1D,SACEC,8BAA8B,EAC9BC,oCAAoC,EACpCC,yBAAyB,EACzBC,mBAAmB,EACnBC,6BAA6B,EAC7BC,cAAc,QACT,mBAAkB;AACzB,SACEC,0BAA0B,EAC1BC,0BAA0B,QACrB,0BAAyB;AAChC,OAAOC,iBAAiB,sBAAqB;AAC7C,SAASC,cAAc,QAAQ,wCAAuC;AACtE,SAASC,YAAY,QAAQ,+BAA8B;AAC3D,SAASC,aAAa,QAAQ,yBAAwB;AACtD,YAAYC,SAAS,eAAc;AACnC,SAASC,cAAc,QAAQ,4BAA2B;AAE1D,SAASC,KAAK,QAAQ,WAAU;AAChC,SAASC,4BAA4B,QAAQ,iCAAgC;AAC7E,SAASC,IAAI,QAAQ,gCAA+B;AACpD,SAASC,mBAAmB,QAAQ,gDAA+C;AACnF,SAASC,iBAAiB,QAAQ,8CAA6C;AAC/E,SAASC,iBAAiB,QAAQ,wBAAuB;AACzD,SAASC,iBAAiB,QAAQ,sCAAqC;AACvE,SAASC,gBAAgB,QAAQ,uCAAsC;AACvE,SAASC,sBAAsB,QAAQ,+CAA8C;AACrF,SAASC,SAAS,QAAQ,uBAAsB;AAEhD,SAASC,0BAA0B,QAAQ,iDAAgD;AAC3F,SAASC,sBAAsB,QAAQ,iCAAgC;AAKvE,SAASC,eAAe,QAAQ,oCAAmC;AACnE,SAASC,sBAAsB,QAAQ,6CAA4C;AACnF,SAASC,oBAAoB,QAAQ,+CAA8C;AACnF,SAASC,mBAAmB,QAAQ,qBAAoB;AACxD,SAASC,qBAAqB,QAAQ,uBAAsB;AAG5D,SAASC,YAAY,EAAEC,gBAAgB,QAAQ,kBAAiB;AAIhE,4CAA4C;AAC5C,MAAMC,QAAQC,QAAQC,GAAG;AAEzB,MAAMC,gBAAgB;AACtB,MAAMC,gBAA8D,CAAC;AACrE,MAAMC,aAAa,CAACC;IAClB,MAAMC,SAASH,aAAa,CAACE,KAAK;IAClC,IAAIC,QAAQ,OAAOA;IACnB,OAAQH,aAAa,CAACE,KAAK,GAAG/C,YAAY+C,IAAI,CAACA;AACjD;AAEA,MAAME,WAAW,OAAOF,OAAiB,AAAC,CAAA,MAAM3C,GAAG8C,IAAI,CAACH,KAAI,EAAGI,IAAI;AAEnE,MAAMC,YAA0D,CAAC;AACjE,MAAMC,SAAS,CAACN;IACd,MAAMC,SAASI,SAAS,CAACL,KAAK;IAC9B,IAAIC,QAAQ,OAAOA;IACnB,OAAQI,SAAS,CAACL,KAAK,GAAGE,SAASF;AACrC;AAEA,OAAO,SAASO,OAAUC,IAAsB,EAAEC,GAAqB;IACrE,OAAO;WAAI,IAAIC,IAAI;eAAIF;eAASC;SAAI;KAAE;AACxC;AAEA,OAAO,SAASE,WACdH,IAAuC,EACvCC,GAAsC;IAEtC,MAAMG,IAAI,IAAIF,IAAIF;IAClB,MAAMK,IAAI,IAAIH,IAAID;IAClB,OAAO;WAAIG;KAAE,CAACE,MAAM,CAAC,CAACC,IAAM,CAACF,EAAEG,GAAG,CAACD;AACrC;AAEA;;CAEC,GACD,SAASE,UAAaT,IAAsB,EAAEC,GAAqB;IACjE,MAAMG,IAAI,IAAIF,IAAIF;IAClB,MAAMK,IAAI,IAAIH,IAAID;IAClB,OAAO;WAAI,IAAIC,IAAI;eAAIE;SAAE,CAACE,MAAM,CAAC,CAACC,IAAMF,EAAEG,GAAG,CAACD;KAAK;AACrD;AAEA,SAASG,IAAIN,CAAwB;IACnC,OAAOA,EAAEO,MAAM,CAAC,CAACf,MAAMD,OAASC,OAAOD,MAAM;AAC/C;AAsBA,IAAIiB;AACJ,IAAIC;AAEJ,IAAIC;AACJ,IAAIC;AAEJ,OAAO,eAAeC,oBACpBC,SAGC,EACDC,QAAgB,EAChBC,WAAoB,IAAI,EACxBC,SAAiC;QAyD7BH,gBAmBMI;IA1EV,IACEC,OAAOC,EAAE,CAACX,qBAAqBK,UAAUO,KAAK,KAC9CT,wBAAwB,CAAC,CAACK,aAC1BE,OAAOC,EAAE,CAACV,wBAAwBI,UAAUQ,GAAG,GAC/C;QACA,OAAOX;IACT;IAEA,0EAA0E;IAC1E,wCAAwC;IAExC,MAAMY,kBAAkB,CACtBC,KACAC,KACAC;QAEA,KAAK,MAAMrC,QAAQqC,QAAQ,CAACD,IAAI,CAAE;YAChC,IAAIA,QAAQ,SAAS;gBACnBD,IAAIG,GAAG,CAACtC,MAAMuC;YAChB,OAAO,IAAIJ,IAAInB,GAAG,CAAChB,OAAO;gBACxBmC,IAAIG,GAAG,CAACtC,MAAMmC,IAAIK,GAAG,CAACxC,QAAS;YACjC,OAAO;gBACLmC,IAAIG,GAAG,CAACtC,MAAM;YAChB;QACF;IACF;IAEA,MAAM6B,QASF;QACFY,OAAO;YAAEC,MAAM,IAAIC;YAAOC,UAAU;QAAE;IACxC;IAEA,IAAK,MAAMR,OAAOX,UAAUO,KAAK,CAACS,KAAK,CAAE;QACvC,IAAIb,WAAW;YACb,MAAMiB,WAAWjB,UAAUY,GAAG,CAACJ;YAC/B,kEAAkE;YAClE,kDAAkD;YAClD,IAAIS,4BAAAA,SAAUC,WAAW,EAAE;gBACzB;YACF;QACF;QAEAjB,MAAMY,KAAK,CAACG,QAAQ;QACpBV,gBAAgBL,MAAMY,KAAK,CAACC,IAAI,EAAEN,KAAKX,UAAUO,KAAK,CAACS,KAAK;IAC9D;IAEA,iDAAiD;IACjD,KAAIhB,iBAAAA,UAAUQ,GAAG,qBAAbR,eAAegB,KAAK,EAAE;QACxBZ,MAAMI,GAAG,GAAG;YAAES,MAAM,IAAIC;YAAuBC,UAAU;QAAE;QAE3D,IAAK,MAAMR,OAAOX,UAAUQ,GAAG,CAACQ,KAAK,CAAE;YACrCZ,MAAMI,GAAG,CAACW,QAAQ;YAClBV,gBAAgBL,MAAMI,GAAG,CAACS,IAAI,EAAEN,KAAKX,UAAUQ,GAAG,CAACQ,KAAK;QAC1D;IACF;IAEA,MAAMM,UAAUpB,WAAW5B,aAAaO;IACxC,MAAM0C,QAAQ,IAAIL;IAElB,6EAA6E;IAC7E,WAAW;IAEX,MAAMM,QAAQC,GAAG,CACf;WACK,IAAIxC,IAAY;eACdmB,MAAMY,KAAK,CAACC,IAAI,CAACS,IAAI;eACpBtB,EAAAA,aAAAA,MAAMI,GAAG,qBAATJ,WAAWa,IAAI,CAACS,IAAI,OAAM,EAAE;SACjC;KACF,CAAChB,GAAG,CAAC,OAAOiB;QACX,IAAI;YACF,kCAAkC;YAClCJ,MAAMV,GAAG,CAACc,GAAG,MAAML,QAAQ5F,KAAKkG,IAAI,CAAC3B,UAAU0B;QACjD,EAAE,OAAM,CAAC;IACX;IAGF,MAAME,aAAa,OAAOC;QAIxB,MAAMC,UAAU;eAAID,QAAQb,IAAI,CAACc,OAAO;SAAG;QAE3C,MAAMC,aAAa,CAACC,QAClBA,MAAMvC,MAAM,CACV,CAACwC,KAAK,CAACP,EAAE;gBACPO,IAAI9B,KAAK,CAAC+B,IAAI,CAACR;gBAEf,MAAMhD,OAAO4C,MAAMR,GAAG,CAACY;gBACvB,IAAI,OAAOhD,SAAS,UAAU;oBAC5BuD,IAAIvD,IAAI,CAACyD,KAAK,IAAIzD;gBACpB;gBAEA,OAAOuD;YACT,GACA;gBACE9B,OAAO,EAAE;gBACTzB,MAAM;oBACJyD,OAAO;gBACT;YACF;QAGJ,OAAO;YACLtD,QAAQkD,WAAWD,QAAQ1C,MAAM,CAAC,CAAC,GAAGgD,IAAI,GAAKA,QAAQ;YACvDC,QAAQN,WACND,QAAQ1C,MAAM,CACZ,CAAC,GAAGgD,IAAI,GAAKA,QAAQP,QAAQX,QAAQ,IAAIkB,QAAQvB;QAGvD;IACF;IAEAjB,cAAc;QACZ0C,QAAQ;YACNvB,OAAO,MAAMa,WAAWzB,MAAMY,KAAK;YACnCR,KAAKJ,MAAMI,GAAG,GAAG,MAAMqB,WAAWzB,MAAMI,GAAG,IAAIgC;QACjD;QACAC,OAAOlB;IACT;IAEA5B,sBAAsBK,UAAUO,KAAK;IACrCX,yBAAyBI,UAAUQ,GAAG;IACtCV,sBAAsB,CAAC,CAACK;IACxB,OAAON;AACT;AAEA,OAAO,SAAS6C,qBAAqBnE,IAAoB;IACvD,OAAOA,SAASpC,uBAAuBoC,SAAS,CAAC,IAAI,EAAEpC,qBAAqB;AAC9E;AAEA,OAAO,SAASwG,8BAA8BpE,IAAoB;IAChE,OACEA,SAASnC,iCACTmC,SAAS,CAAC,IAAI,EAAEnC,+BAA+B;AAEnD;AAEA,MAAMwG,oBAAoB,CACxBC,MACAC,WACAC;IAEA,IAAI/B;IACJ,IAAI8B,cAAc,OAAO;QACvB,8CAA8C;QAC9C9B,QAAQ6B,KAAKxD,MAAM,CAAC,CAAC2D,IAAMA,MAAM;IACnC,OAAO;QACL,wBAAwB;QACxBhC,QAAQ6B,KACLI,KAAK,GACL5D,MAAM,CACL,CAAC2D,IACC,CACEA,CAAAA,MAAM,gBACNA,MAAM,aACL,CAACD,gBAAgBC,MAAM,OAAO;IAGzC;IACA,OAAOhC,MAAMkC,IAAI,CAAC,CAAC/D,GAAGC,IAAMD,EAAEgE,aAAa,CAAC/D;AAC9C;AA4BA,OAAO,SAASgE,8BACdC,KAAgB;IAEhB,MAAMC,yBAAiD,CAAC;IACxD,KAAK,MAAM,CAACC,OAAOC,KAAK,IAAIH,MAAMtB,OAAO,GAAI;QAC3C,IAAIpF,cAAc6G,KAAKC,OAAO,GAAG;YAC/BH,sBAAsB,CAACC,MAAM,GAAG;QAClC;IACF;IAEA,OAAOD;AACT;AAEA,OAAO,eAAeI,cACpBC,KAGC,EACDxD,SAAgC,EAChC,EACEF,QAAQ,EACR2D,OAAO,EACPC,QAAQ,EACRC,cAAc,EACdC,aAAa,EACbC,gBAAgB,EAChBC,kBAAkB,EAClBC,iBAAiB,EACjBhE,WAAW,IAAI,EAWhB;QAyWEyD,YAWoBM;IAlXvB,MAAME,gBAAgB,CACpBC,OACA,EAAEC,MAAM,EAAwB,GAAG,CAAC,CAAC;QAErC,MAAM1F,OAAO2F,QAAQC,GAAG,CAACC,yCAAyC,GAC9D,WACAhI,YAAY4H;QAEhB,OAAOC,SAAShJ,MAAMC,KAAKqD,SAASA;IACtC;IAEA,yEAAyE;IACzE,MAAM8F,eAAeH,QAAQC,GAAG,CAACC,yCAAyC,GACtE1D,SAAS,kCAAkC;OAC3C;IAEJ,MAAM4D,oBAAoB,CAACC;QACzB,MAAMC,WAAW,GAAGD,UAAU,GAAG,CAAC;QAClC,uBAAuB;QACvB,IAAIA,YAAY,MAAM,OAAO1J,MAAM2J;QACnC,yBAAyB;QACzB,IAAID,YAAY,MAAM,OAAOzJ,OAAO0J;QACpC,oBAAoB;QACpB,OAAOzJ,IAAIG,KAAKsJ;IAClB;IAEA,MAAMC,eAAe,CAACC,WACpBA,QACE,qBAAqB;SACpBC,OAAO,CAAC,aAAa,GACtB,kCAAkC;SACjCA,OAAO,CAAC,cAAc,SACvB,mBAAmB;SAClBA,OAAO,CAAC,6CAA6C;IAE1D,iCAAiC;IACjC,MAAMhC,eAAe,CAAC,CACpBc,CAAAA,YAAa,MAAMnH,aAAamH,UAAU,SAASC,gBAAgB,MAAM;IAG3E,gEAAgE;IAChE,MAAMkB,cAAc,IAAI/F;IAExB,MAAMgG,WAAuD,EAAE;IAE/D,MAAM1D,QAAQ,MAAMxB,oBAClB;QAAEQ,OAAOwD;QAAevD,KAAKwD;IAAiB,GAC9C/D,UACAC,UACAC;IAGF,MAAM+E,gBAAgB,OAAO,EAC3BrC,IAAI,EACJsC,UAAU,EAIX;YAwNyB5D,0BAIpBA;QA3NJ,MAAM6D,gBAAgBxC,kBAAkBC,MAAMsC,YAAYpC;QAC1D,IAAIqC,cAAcC,MAAM,KAAK,GAAG;YAC9B;QACF;QAEA,IAAIC,iBAAiB;QACrB,IAAIC,aAAa;QAEjB,KAAK,MAAMC,QAAQJ,cAAe;gBACXjF;YAArB,MAAMsF,gBAAetF,iBAAAA,UAAUY,GAAG,CAACyE,0BAAdrF,eAAqBuF,mBAAmB;YAE7D,IAAID,gCAAAA,aAAcE,UAAU,EAAE;gBAC5BL,iBAAiB;YACnB;YAEA,IAAIG,gCAAAA,aAAcG,MAAM,EAAE;gBACxBL,aAAa;YACf;YAEA,IAAID,kBAAkBC,YAAY;gBAChC;YACF;QACF;QAEAN,SAAS9C,IAAI,CACX;YACEgD,eAAe,QAAQ,gBAAgB;YACvC;YACA;YACAG,iBAAiB,eAAe;YAChCC,aAAa,WAAW;SACzB,CAAC7E,GAAG,CAAC,CAACmF,QAAUtK,UAAUsK;QAS7BT,cAAcU,OAAO,CAAC,CAACC,MAAMC,GAAGC;gBAc3B7E,4BA6DD2C,2BAsBE3C;YAhGJ,MAAM8E,SACJF,MAAM,IACFC,IAAIZ,MAAM,KAAK,IACb,MACA,MACFW,MAAMC,IAAIZ,MAAM,GAAG,IACjB,MACA;YAER,MAAMjE,WAAWjB,UAAUY,GAAG,CAACgF;YAC/B,MAAMI,WAAWpC,cAAcqC,aAAa,CAACC,QAAQ,CAACN;YACtD,MAAMO,gBACJ,AAAClF,CAAAA,CAAAA,4BAAAA,SAAUmF,YAAY,KAAI,CAAA,IAC1BnF,CAAAA,CAAAA,6BAAAA,6BAAAA,SAAUoF,gBAAgB,qBAA1BpF,2BAA4B1B,MAAM,CAAC,CAACP,GAAGC,IAAMD,IAAKC,CAAAA,KAAK,CAAA,GAAI,OAAM,CAAA;YAEpE,IAAIqH;YAEJ,IAAIV,SAAS,WAAWA,SAAS,gBAAgB;gBAC/CU,SAAS;YACX,OAAO,IAAI9J,cAAcyE,4BAAAA,SAAUqC,OAAO,GAAG;gBAC3CgD,SAAS;YACX,OAAO,IAAIrF,4BAAAA,SAAUsF,iBAAiB,EAAE;gBACtC,IACE,2EAA2E;gBAC3EtF,CAAAA,4BAAAA,SAAUuF,eAAe,KACzB,qEAAqE;gBACrE,0DAA0D;gBACzDvF,SAASwF,iBAAiB,IAAI,CAACxF,SAASyF,YAAY,EACrD;oBACAJ,SAAS;gBACX,OAAO,IAAI,EAACrF,4BAAAA,SAAUyF,YAAY,GAAE;oBAClCJ,SAAS;gBACX,OAAO;oBACLA,SAAS;gBACX;YACF,OAAO,IAAIrF,4BAAAA,SAAU0F,QAAQ,EAAE;gBAC7BL,SAAS;YACX,OAAO,IAAIrF,4BAAAA,SAAU2F,KAAK,EAAE;gBAC1BN,SAAS;YACX,OAAO;gBACLA,SAAS;YACX;YAEAzB,YAAYgC,GAAG,CAACP;YAEhBxB,SAAS9C,IAAI,CAAC;gBACZ,GAAG+D,OAAO,CAAC,EAAEO,OAAO,CAAC,EAAEV,OACrBO,gBAAgB7B,eACZ,CAAC,EAAE,EAAEC,kBAAkB4B,eAAe,CAAC,CAAC,GACxC,IACJ;gBACFlF,WACI+E,WACE/K,KAAK,SACLgG,SAASzC,IAAI,IAAI,IACfwF,cAAc/C,SAASzC,IAAI,IAC3B,KACJ;gBACJyC,WACI+E,WACE/K,KAAK,SACLgG,SAASzC,IAAI,IAAI,IACfwF,cAAc/C,SAAS6F,SAAS,EAAE;oBAAE5C,QAAQ;gBAAK,KACjD,KACJ;gBACJiB,mBAAkBlE,4BAAAA,SAAUsE,mBAAmB,IAC3C1H,iBAAiBoD,SAASsE,mBAAmB,IAC7C;gBACJH,eAAcnE,4BAAAA,SAAUsE,mBAAmB,IACvC3H,aAAaqD,SAASsE,mBAAmB,IACzC;aACL;YAED,MAAMwB,iBACJnD,EAAAA,4BAAAA,cAAc/C,KAAK,CAAC+E,KAAK,qBAAzBhC,0BAA2B1E,MAAM,CAC/B,CAACd;oBAECgD;uBADAhD,KAAK4I,QAAQ,CAAC,aACd5F,2BAAAA,MAAMgB,MAAM,CAAC4C,WAAW,qBAAxB5D,yBAA0BzC,MAAM,CAACsB,KAAK,CAACiG,QAAQ,CAAC9H;mBAC/C,EAAE;YAET,IAAI2I,eAAe7B,MAAM,GAAG,GAAG;gBAC7B,MAAM+B,aAAapB,MAAMC,IAAIZ,MAAM,GAAG,IAAI,MAAM;gBAEhD6B,eAAepB,OAAO,CAAC,CAACvH,MAAM8I,OAAO,EAAEhC,MAAM,EAAE;oBAC7C,MAAMiC,cAAcD,UAAUhC,SAAS,IAAI,MAAM;oBACjD,MAAM1G,OAAO4C,MAAMkB,KAAK,CAAC1B,GAAG,CAACxC;oBAC7B0G,SAAS9C,IAAI,CAAC;wBACZ,GAAGiF,WAAW,GAAG,EAAEE,YAAY,CAAC,EAAEzC,aAAatG,OAAO;wBACtD,OAAOI,SAAS,WAAWwF,cAAcxF,QAAQ;wBACjD;wBACA;wBACA;qBACD;gBACH;YACF;YAEA,IAAIyC,6BAAAA,0BAAAA,SAAUmG,aAAa,qBAAvBnG,wBAAyBiE,MAAM,EAAE;gBACnC,MAAMmC,cAAcpG,SAASmG,aAAa,CAAClC,MAAM;gBACjD,MAAM+B,aAAapB,MAAMC,IAAIZ,MAAM,GAAG,IAAI,MAAM;gBAEhD,IAAIoC;gBACJ,IACErG,SAASoF,gBAAgB,IACzBpF,SAASoF,gBAAgB,CAACkB,IAAI,CAAC,CAACC,IAAMA,IAAIlD,eAC1C;oBACA,MAAMmD,eAAeJ,gBAAgB,IAAI,IAAIK,KAAKC,GAAG,CAACN,aAAa;oBACnE,MAAMO,qBAAqB3G,SAASmG,aAAa,CAC9C7G,GAAG,CAAC,CAAC6C,OAAOyE,MAAS,CAAA;4BACpBzE;4BACAqB,UAAUxD,SAASoF,gBAAgB,AAAC,CAACwB,IAAI,IAAI;wBAC/C,CAAA,GACC9E,IAAI,CAAC,CAAC,EAAE0B,UAAUzF,CAAC,EAAE,EAAE,EAAEyF,UAAUxF,CAAC,EAAE,GACrC,mBAAmB;wBACnB,wDAAwD;wBACxDD,KAAKsF,gBAAgBrF,KAAKqF,eAAe,IAAIrF,IAAID;oBAErDsI,SAASM,mBAAmB9E,KAAK,CAAC,GAAG2E;oBACrC,MAAMK,kBAAkBF,mBAAmB9E,KAAK,CAAC2E;oBACjD,IAAIK,gBAAgB5C,MAAM,EAAE;wBAC1B,MAAM6C,YAAYD,gBAAgB5C,MAAM;wBACxC,MAAM8C,cAAcN,KAAKO,KAAK,CAC5BH,gBAAgBvI,MAAM,CACpB,CAAC0C,OAAO,EAAEwC,QAAQ,EAAE,GAAKxC,QAAQwC,UACjC,KACEqD,gBAAgB5C,MAAM;wBAE5BoC,OAAOtF,IAAI,CAAC;4BACVoB,OAAO,CAAC,EAAE,EAAE2E,UAAU,YAAY,CAAC;4BACnCtD,UAAU;4BACVuD;wBACF;oBACF;gBACF,OAAO;oBACL,MAAMP,eAAeJ,gBAAgB,IAAI,IAAIK,KAAKC,GAAG,CAACN,aAAa;oBACnEC,SAASrG,SAASmG,aAAa,CAC5BtE,KAAK,CAAC,GAAG2E,cACTlH,GAAG,CAAC,CAAC6C,QAAW,CAAA;4BAAEA;4BAAOqB,UAAU;wBAAE,CAAA;oBACxC,IAAI4C,cAAcI,cAAc;wBAC9B,MAAMM,YAAYV,cAAcI;wBAChCH,OAAOtF,IAAI,CAAC;4BAAEoB,OAAO,CAAC,EAAE,EAAE2E,UAAU,YAAY,CAAC;4BAAEtD,UAAU;wBAAE;oBACjE;gBACF;gBAEA6C,OAAO3B,OAAO,CACZ,CAAC,EAAEvC,KAAK,EAAEqB,QAAQ,EAAEuD,WAAW,EAAE,EAAEd,OAAO,EAAEhC,MAAM,EAAE;wBAIhDlF;oBAHF,MAAMmH,cAAcD,UAAUhC,SAAS,IAAI,MAAM;oBAEjD,MAAMK,uBACJvF,iBAAAA,UAAUY,GAAG,CAACwC,2BAAdpD,eAAsBuF,mBAAmB;oBAE3CT,SAAS9C,IAAI,CAAC;wBACZ,GAAGiF,WAAW,GAAG,EAAEE,YAAY,CAAC,EAAE/D,QAChCqB,WAAWH,eACP,CAAC,EAAE,EAAEC,kBAAkBE,UAAU,CAAC,CAAC,GACnC,KAEJuD,eAAeA,cAAc1D,eACzB,CAAC,MAAM,EAAEC,kBAAkByD,aAAa,CAAC,CAAC,GAC1C,IACJ;wBACF;wBACA;wBACA7C,kBAAkBI,sBACd1H,iBAAiB0H,uBACjB;wBACJH,cAAcG,sBACV3H,aAAa2H,uBACb;qBACL;gBACH;YAEJ;QACF;QAEA,MAAM2C,mBAAkB9G,2BAAAA,MAAMgB,MAAM,CAAC4C,WAAW,qBAAxB5D,yBAA0Be,MAAM,CAAC3D,IAAI,CAACyD,KAAK;QAEnE,MAAMkG,cAAchE,QAAQC,GAAG,CAACC,yCAAyC,GACrE,EAAE,GACFjD,EAAAA,4BAAAA,MAAMgB,MAAM,CAAC4C,WAAW,qBAAxB5D,0BAA0Be,MAAM,CAAClC,KAAK,KAAI,EAAE;QAEhD6E,SAAS9C,IAAI,CAAC;YACZ;YACA,OAAOkG,oBAAoB,WACvBlE,cAAckE,iBAAiB;gBAAEhE,QAAQ;YAAK,KAC9C;YACJ;YACA;YACA;SACD;QACD,MAAMkE,iBAA2B,EAAE;QACnC,MAAMC,iBAAiB;eAClBF,YACAjJ,MAAM,CAAC,CAACd;gBACP,IAAIA,KAAK4I,QAAQ,CAAC,SAAS;oBACzBoB,eAAepG,IAAI,CAAC5D;oBACpB,OAAO;gBACT;gBACA,OAAO;YACT,GACCmC,GAAG,CAAC,CAACsC,IAAMA,EAAE+B,OAAO,CAACnB,SAAS,cAC9BV,IAAI;eACJqF,eAAe7H,GAAG,CAAC,CAACsC,IAAMA,EAAE+B,OAAO,CAACnB,SAAS,cAAcV,IAAI;SACnE;QAED,0GAA0G;QAC1G,MAAMuF,aAAa,KAAK;QACxB,IAAIC,gBAAgB;QACpB,IAAIC,iBAAiB;QACrBH,eAAe1C,OAAO,CAAC,CAAChB,UAAUuC,OAAO,EAAEhC,MAAM,EAAE;YACjD,MAAMiC,cAAcD,QAAQsB,mBAAmBtD,SAAS,IAAI,MAAM;YAElE,MAAMuD,eAAe9D,SAASC,OAAO,CAAC,aAAanB;YACnD,MAAMiF,YAAYhE,aAAaC;YAC/B,MAAMnG,OAAO4C,MAAMkB,KAAK,CAAC1B,GAAG,CAAC6H;YAE7B,IAAI,CAACjK,QAAQA,OAAO8J,YAAY;gBAC9BE;gBACAD,iBAAiB/J,QAAQ;gBACzB;YACF;YAEAsG,SAAS9C,IAAI,CAAC;gBACZ,CAAC,EAAE,EAAEmF,YAAY,CAAC,EAAEuB,WAAW;gBAC/B1E,cAAcxF;gBACd;gBACA;gBACA;aACD;QACH;QAEA,IAAIgK,iBAAiB,GAAG;YACtB1D,SAAS9C,IAAI,CAAC;gBACZ,CAAC,+BAA+B,CAAC;gBACjCgC,cAAcuE;gBACd;gBACA;gBACA;aACD;QACH;IACF;IAEA,yDAAyD;IACzD,IAAI/E,MAAMnD,GAAG,IAAIe,MAAMgB,MAAM,CAAC/B,GAAG,EAAE;QACjC,MAAM0E,cAAc;YAClBC,YAAY;YACZtC,MAAMc,MAAMnD,GAAG;QACjB;QAEAyE,SAAS9C,IAAI,CAAC;YAAC;YAAI;YAAI;YAAI;YAAI;SAAG;IACpC;IAEAhC,UAAUU,GAAG,CAAC,QAAQ;QACpB,GAAIV,UAAUY,GAAG,CAAC,WAAWZ,UAAUY,GAAG,CAAC,UAAU;QACrD+F,UAAU5C;IACZ;IAEA,uFAAuF;IACvF,IACE,CAACP,MAAM3C,KAAK,CAACqF,QAAQ,CAAC,WACtB,GAAC1C,aAAAA,MAAMnD,GAAG,qBAATmD,WAAW0C,QAAQ,CAAC9J,8BACrB;QACAoH,MAAM3C,KAAK,GAAG;eAAI2C,MAAM3C,KAAK;YAAE;SAAO;IACxC;IAEA,+CAA+C;IAC/C,MAAMkE,cAAc;QAClBC,YAAY;QACZtC,MAAMc,MAAM3C,KAAK;IACnB;IAEA,MAAM8H,kBAAiB7E,iCAAAA,mBAAmB8E,UAAU,qBAA7B9E,8BAA+B,CAAC,IAAI;IAC3D,IAAI6E,CAAAA,kCAAAA,eAAgB1I,KAAK,CAACiF,MAAM,IAAG,GAAG;QACpC,MAAM2D,kBAAkB,MAAMxH,QAAQC,GAAG,CACvCqH,eAAe1I,KAAK,CACjBM,GAAG,CAAC,CAACuI,MAAQ,GAAGhJ,SAAS,CAAC,EAAEgJ,KAAK,EACjCvI,GAAG,CAACR,WAAW5B,aAAaO;QAGjCoG,SAAS9C,IAAI,CAAC;YAAC;YAAI;YAAI;YAAI;YAAI;SAAG;QAClC8C,SAAS9C,IAAI,CAAC;YACZ;YACAgC,cAAc1E,IAAIuJ,kBAAkB;gBAAE3E,QAAQ;YAAK;YACnD;YACA;YACA;SACD;IACH;IAEApG,MACExC,UAAUwJ,UAAU;QAClBiE,OAAO;YAAC;YAAK;YAAK;YAAK;YAAK;SAAI;QAChCC,cAAc,CAACC,MAAQtN,UAAUsN,KAAK/D,MAAM;IAC9C;IAGF,MAAMgE,qBACJ1F,MAAMnD,GAAG,IAAIe,MAAMgB,MAAM,CAAC/B,GAAG,GAAG,yBAAyB;IAC3DvC;IACAA,MACExC,UACE;QACEuJ,YAAYzF,GAAG,CAAC,QAAQ;YACtB;YACA;YACA;SACD;QACDyF,YAAYzF,GAAG,CAAC,QAAQ;YACtB;YACA;YACA,CAAC,iCAAiC,EAAEnE,KAAKiO,oBAAoB,CAAC,CAAC;SAChE;QACDrE,YAAYzF,GAAG,CAAC,QAAQ;YACtB;YACA;YACA;SACD;QACDyF,YAAYzF,GAAG,CAAC,QAAQ;YAAC;YAAK;YAAa,CAAC,yBAAyB,CAAC;SAAC;KACxE,CAACF,MAAM,CAAC,CAACC,IAAMA,IAChB;QACE4J,OAAO;YAAC;YAAK;YAAK;SAAI;QACtBC,cAAc,CAACC,MAAQtN,UAAUsN,KAAK/D,MAAM;IAC9C;IAIJpH;AACF;AAEA,OAAO,SAASqL,kBAAkB,EAChCC,SAAS,EACTC,QAAQ,EACRC,OAAO,EACM;IACb,MAAMC,cAAc,CAClBjC,QACAkC;QAEA,MAAMC,cAAcD,SAAS;QAC7B,MAAME,YAAYF,SAAS;QAC3B1L,MAAM1C,UAAUoO;QAEhB;;;;KAIC,GACD,MAAMG,YAAY,AAACrC,OAChB/G,GAAG,CAAC,CAAC6C;YACJ,IAAIwG,WAAW,CAAC,UAAU,EAAExG,MAAMyG,MAAM,CAAC,EAAE,CAAC;YAE5C,IAAI,CAACH,WAAW;gBACd,MAAMI,IAAI1G;gBACVwG,YAAY,GAAGH,cAAc,MAAM,IAAI,cAAc,EACnDK,EAAEC,WAAW,CACd,EAAE,CAAC;YACN;YACA,IAAIN,aAAa;gBACf,MAAMK,IAAI1G;gBACVwG,YAAY,CAAC,EAAE,EACbE,EAAEE,UAAU,GACR,CAAC,QAAQ,EAAEF,EAAEE,UAAU,EAAE,GACzB,CAAC,WAAW,EAAEF,EAAEG,SAAS,EAAE,CAChC,EAAE,CAAC;YACN;YAEA,IAAIP,WAAW;gBACb,MAAMI,IAAI1G;gBACVwG,YAAY,CAAC,YAAY,CAAC;gBAE1B,IAAK,IAAI/D,IAAI,GAAGA,IAAIiE,EAAER,OAAO,CAACpE,MAAM,EAAEW,IAAK;oBACzC,MAAMqE,SAASJ,EAAER,OAAO,CAACzD,EAAE;oBAC3B,MAAMsE,OAAOtE,MAAMyD,QAAQpE,MAAM,GAAG;oBAEpC0E,YAAY,CAAC,EAAE,EAAEO,OAAO,MAAM,IAAI,CAAC,EAAED,OAAO1J,GAAG,CAAC,EAAE,EAAE0J,OAAOE,KAAK,CAAC,EAAE,CAAC;gBACtE;YACF;YAEA,OAAOR;QACT,GACCnI,IAAI,CAAC;QAER3D,MAAM,GAAG6L,UAAU,EAAE,CAAC;IACxB;IAEA7L;IACA,IAAIsL,UAAUlE,MAAM,EAAE;QACpBqE,YAAYH,WAAW;IACzB;IACA,IAAIE,QAAQpE,MAAM,EAAE;QAClBqE,YAAYD,SAAS;IACvB;IAEA,MAAMe,mBAAmB;WACpBhB,SAASiB,WAAW;WACpBjB,SAASkB,UAAU;WACnBlB,SAASmB,QAAQ;KACrB;IACD,IAAIH,iBAAiBnF,MAAM,EAAE;QAC3BqE,YAAYc,kBAAkB;IAChC;AACF;AAEA,OAAO,eAAeI,kBACpBzF,UAAuB,EACvBK,IAAY,EACZvF,QAAgB,EAChB8D,aAA4B,EAC5BC,gBAAmC,EACnC9D,WAAoB,IAAI,EACxB2K,WAAwC;IAExC,MAAMC,eAAe3F,eAAe,UAAUpB,gBAAgBC;IAC9D,IAAI,CAAC8G,cAAc;QACjB,MAAM,qBAA6D,CAA7D,IAAIC,MAAM,qDAAV,qBAAA;mBAAA;wBAAA;0BAAA;QAA4D;IACpE;IAEA,kCAAkC;IAClC,IAAI5F,eAAe,OAAO;QACxB2F,aAAa9J,KAAK,GAAGX,OAAO0B,OAAO,CAAC+I,aAAa9J,KAAK,EAAEtB,MAAM,CAC5D,CAACwC,KAA+B,CAACvB,KAAK4J,MAAM;YAC1C,MAAMS,SAAS3N,iBAAiBsD;YAChCuB,GAAG,CAAC8I,OAAO,GAAGT;YACd,OAAOrI;QACT,GACA,CAAC;IAEL;IAEA,oDAAoD;IACpD,MAAMX,QACJsJ,eACC,MAAM9K,oBACL;QAAEQ,OAAOwD;QAAevD,KAAKwD;IAAiB,GAC9C/D,UACAC;IAGJ,MAAM+K,WAAW1J,MAAMgB,MAAM,CAAC4C,WAAW;IACzC,IAAI,CAAC8F,UAAU;QACb,kEAAkE;QAClE,MAAM,qBAAgE,CAAhE,IAAIF,MAAM,wDAAV,qBAAA;mBAAA;wBAAA;0BAAA;QAA+D;IACvE;IAEA,MAAMG,WACJ/F,eAAe,UACXlI,oBAAoBuI,QACpBlI,uBAAuBkI;IAE7B,MAAM2F,aAAa,CAACtF,QAAkBA,MAAMsB,QAAQ,CAAC;IAErD,MAAMiE,YAAY,AAACN,CAAAA,aAAa9J,KAAK,CAACkK,SAAS,IAAI,EAAE,AAAD,EAAG7L,MAAM,CAAC8L;IAC9D,MAAME,WAAW,AAACP,CAAAA,aAAa9J,KAAK,CAAC,QAAQ,IAAI,EAAE,AAAD,EAAG3B,MAAM,CAAC8L;IAE5D,MAAMG,gBAAgB,CAACrC,MAAgB,GAAGhJ,SAAS,CAAC,EAAEgJ,KAAK;IAE3D,MAAMsC,eAAezM,OAAOsM,WAAWC,UAAU3K,GAAG,CAAC4K;IACrD,MAAME,gBAAgBtM,WACpB,mEAAmE;IACnEM,UAAU4L,WAAWH,SAASnM,MAAM,CAACsB,KAAK,GAC1C,gCAAgC;IAChC6K,SAAS3I,MAAM,CAAClC,KAAK,EACrBM,GAAG,CAAC4K;IAEN,MAAMhK,UAAUpB,WAAW5B,aAAaO;IAExC,2EAA2E;IAC3E,eAAe;IACf,MAAM4M,gBAAgB,OAAOlN;QAC3B,MAAMoC,MAAMpC,KAAK0E,KAAK,CAAChD,SAASoF,MAAM,GAAG;QACzC,MAAM1G,OAA2B4C,MAAMkB,KAAK,CAAC1B,GAAG,CAACJ;QAEjD,oEAAoE;QACpE,YAAY;QACZ,IAAI,OAAOhC,SAAS,UAAU;YAC5B,OAAO2C,QAAQ/C;QACjB;QAEA,OAAOI;IACT;IAEA,IAAI;QACF,0EAA0E;QAC1E,kEAAkE;QAClE,MAAM+M,eAAejM,IAAI,MAAM+B,QAAQC,GAAG,CAAC8J,aAAa7K,GAAG,CAAC+K;QAC5D,MAAME,gBAAgBlM,IACpB,MAAM+B,QAAQC,GAAG,CAAC+J,cAAc9K,GAAG,CAAC+K;QAGtC,OAAO;YAACE;YAAeD;SAAa;IACtC,EAAE,OAAM,CAAC;IACT,OAAO;QAAC,CAAC;QAAG,CAAC;KAAE;AACjB;AAkBA,OAAO,eAAeE,aAAa,EACjCC,GAAG,EACHrG,IAAI,EACJsG,OAAO,EACPC,cAAc,EACdC,gBAAgB,EAChBC,gBAAgB,EAChBC,OAAO,EACPC,aAAa,EACbC,QAAQ,EACRC,WAAW,EACXC,QAAQ,EACRC,QAAQ,EACRC,SAAS,EACTC,cAAc,EACdC,eAAe,EACfC,cAAc,EACdC,kBAAkB,EAClBC,gBAAgB,EAChBC,YAAY,EACZC,aAAa,EACbC,iBAAiB,EACjBC,SAAS,EACTrJ,OAAO,EACPsJ,UAAU,EA4BX;IACC,MAAMvP,uBAAuB;QAC3BmP;QACAC;QACAjB;QACAD;QACAsB,aAAaR;QACbS,oBAAoBR;IACtB;IAEA,MAAMS,mBAAmBvQ,MAAM,wBAAwBsP;IACvD,OAAOiB,iBACJC,YAAY,CAAC;QACZC,QAAQ,yCAAyCC,SAAS,CACxDxB;QAEFjP,6BAA6B;YAC3BkP;QACF;QAEA,IAAIwB;QACJ,IAAIC;QACJ,IAAIC;QACJ,IAAIC,YAA8B,CAAC;QACnC,IAAIC;QACJ,IAAIC,oBAA6B;QACjC,MAAMC,oBAAoBpR,cAAc0P;QAExC,IAAI0B,mBAAmB;YACrB,MAAMtK,UAAU,MAAMtG,kBAAkB;gBACtC6Q,OAAO1B,SAASlM,KAAK,CAACM,GAAG,CAAC,CAACnC,OAAiB7C,KAAKkG,IAAI,CAACkK,SAASvN;gBAC/D0P,mBAAmB;oBACjB,GAAG3B,QAAQ;oBACX4B,MAAM,AAAC5B,CAAAA,SAAS4B,IAAI,IAAI,EAAE,AAAD,EAAGxN,GAAG,CAAC,CAACyN,UAA2B,CAAA;4BAC1D,GAAGA,OAAO;4BACVC,UAAU1S,KAAKkG,IAAI,CAACkK,SAASqC,QAAQC,QAAQ;wBAC/C,CAAA;gBACF;gBACAC,MAAM/B,SAAS+B,IAAI;gBACnBC,UAAU;gBACVxC;YACF;YACA,MAAMyC,MAAM,AACV,CAAA,MAAM9K,QAAQ+K,OAAO,CAACC,QAAQ,CAAC,CAAC,WAAW,EAAEnC,SAAS+B,IAAI,EAAE,CAAC,AAAD,EAC5DK,YAAY;YAEd,qCAAqC;YACrC,MAAM3K,gBAAgB,CAAC;YAEvB+J,oBAAoB1Q,kBAAkBmR;YACtCd,mBAAmB;gBACjBkB,WAAWJ,IAAIK,OAAO;gBACtBC,UAAUN,IAAIM,QAAQ;gBACtBC,KAAKP,IAAIO,GAAG;gBACZC,aAAaR,IAAIQ,WAAW;gBAC5BvJ;gBACAkJ,cAAcH;gBACdS,YAAYT,IAAIU,MAAM,IAAI,CAAC;gBAC3BlL;gBACAmL,uBAAuB,CAAC;gBACxBC,oBAAoBZ,IAAIY,kBAAkB;gBAC1CC,gBAAgBb,IAAIa,cAAc;gBAClCC,gBAAgBd,IAAIc,cAAc;YACpC;QACF,OAAO;YACL5B,mBAAmB,MAAM5Q,eAAe;gBACtCiP;gBACAtG,MAAMkH,mBAAmBlH;gBACzB8J,WAAW/C,aAAa;gBACxBgD,OAAO;gBACPrC;YACF;QACF;QACA,MAAMsC,OAAO/B,iBAAiBkB,SAAS;QAEvC,MAAMI,cAA2BtB,iBAAiBsB,WAAW;QAE7D,IAAIrI,oBAA6B;QAEjC,IAAI6F,aAAa,OAAO;YACtB,MAAMmC,eAA8BjB,iBAAiBiB,YAAY;YAEjEZ,oBAAoB1Q,kBAAkBqQ,iBAAiBiB,YAAY;YAEnE,IAAIe;YACJ,IAAI;gBACFA,WAAW,MAAM/R,gBAAgB+P;YACnC,EAAE,OAAOiC,KAAK;gBACZ,MAAM,qBAEJ,CAFI,IAAI3E,MAAM,CAAC,oCAAoC,EAAEvF,MAAM,EAAE;oBAC7DmK,OAAOD;gBACT,IAFM,qBAAA;2BAAA;gCAAA;kCAAA;gBAEL;YACH;YAEA9B,YAAYgC,gBAAgBH;YAE5B,IAAI7B,UAAUiC,OAAO,KAAK,kBAAkB9B,mBAAmB;gBAC7DnR,IAAIkT,IAAI,CACN,CAAC,MAAM,EAAEtK,KAAK,gKAAgK,CAAC;YAEnL;YAEAqI,gBAAgBjQ,qBAAqB6P;YAErC,uEAAuE;YACvE,wEAAwE;YACxE,uBAAuB;YACvB/G,oBACEqI,YAAYgB,UAAU,CAACC,IAAI,KAAKzS,UAAU0S,QAAQ,IAClD,CAACzS,2BAA2BgI,SAC5B/H,uBAAuBwP,WAAWW;YAEpC,uEAAuE;YACvE,mBAAmB;YACnB,yDAAyD;YACzD,IAAIA,UAAUiC,OAAO,KAAK,mBAAmB,CAACnJ,mBAAmB;gBAC/DkH,UAAUjI,UAAU,GAAG;YACzB;YAEA,wEAAwE;YACxE,kEAAkE;YAClE,SAAS;YACT,IAAIlJ,eAAe+I,SAAS,CAACuI,mBAAmB;;gBAC5C,CAAA,EAAEL,iBAAiB,EAAEwC,cAAcvC,qBAAqB,EAAE,GAC1D,MAAM9P,oBAAoB;oBACxBgO;oBACArG;oBACAgH;oBACAC;oBACAgD;oBACA3D;oBACAqE,gBAAgB,CAAC;oBACjBxD;oBACAC;oBACAE;oBACAE;oBACA0B;oBACA7B;oBACAnG;oBACA9C;oBACAiK;gBACF,EAAC;YACL;QACF,OAAO;YACL,IAAI,CAAC2B,QAAQ,CAAC3T,mBAAmB2T,SAAS,OAAOA,SAAS,UAAU;gBAClE,MAAM,qBAAmC,CAAnC,IAAIzE,MAAM,2BAAV,qBAAA;2BAAA;gCAAA;kCAAA;gBAAkC;YAC1C;QACF;QAEA,MAAMqF,qBAAqB,CAAC,EAACZ,wBAAAA,KAAMa,eAAe;QAClD,MAAMC,iBAAiB,CAAC,CAAC7C,iBAAiB4B,cAAc;QACxD,MAAMkB,iBAAiB,CAAC,CAAC9C,iBAAiB2B,cAAc;QACxD,MAAMoB,iBAAiB,CAAC,CAAC/C,iBAAiB0B,kBAAkB;QAE5D,uEAAuE;QACvE,iBAAiB;QACjB,IAAIiB,sBAAsBE,gBAAgB;YACxC,MAAM,qBAAyC,CAAzC,IAAIvF,MAAM/O,iCAAV,qBAAA;uBAAA;4BAAA;8BAAA;YAAwC;QAChD;QAEA,IAAIoU,sBAAsBI,gBAAgB;YACxC,MAAM,qBAA+C,CAA/C,IAAIzF,MAAM9O,uCAAV,qBAAA;uBAAA;4BAAA;8BAAA;YAA8C;QACtD;QAEA,IAAIqU,kBAAkBE,gBAAgB;YACpC,MAAM,qBAAoC,CAApC,IAAIzF,MAAM7O,4BAAV,qBAAA;uBAAA;4BAAA;8BAAA;YAAmC;QAC3C;QAEA,MAAMuU,gBAAgBhU,eAAe+I;QACrC,oEAAoE;QACpE,IAAI8K,kBAAkBC,kBAAkB,CAACE,eAAe;YACtD,MAAM,qBAGL,CAHK,IAAI1F,MACR,CAAC,yDAAyD,EAAEvF,KAAK,EAAE,CAAC,GAClE,CAAC,4DAA4D,CAAC,GAF5D,qBAAA;uBAAA;4BAAA;8BAAA;YAGN;QACF;QAEA,IAAI8K,kBAAkBG,iBAAiB,CAACF,gBAAgB;YACtD,MAAM,qBAGL,CAHK,IAAIxF,MACR,CAAC,qEAAqE,EAAEvF,KAAK,EAAE,CAAC,GAC9E,CAAC,0EAA0E,CAAC,GAF1E,qBAAA;uBAAA;4BAAA;8BAAA;YAGN;QACF;QAEA,IAAI8K,kBAAkBC,gBAAgB;;YAClC,CAAA,EAAE7C,iBAAiB,EAAEwC,cAAcvC,qBAAqB,EAAE,GAC1D,MAAM7P,sBAAsB;gBAC1B0H;gBACA0G;gBACAC;gBACAJ;gBACAqD,gBAAgB3B,iBAAiB2B,cAAc;YACjD,EAAC;QACL;QAEA,MAAMsB,sBAAsB,AAACC,WAAmBC,qBAAqB;QACrE,MAAM3B,SAAqBnB,oBACvB,CAAC,IACDL,iBAAiBuB,UAAU;QAE/B,IAAIlI,WAAW;QACf,IAAI,CAACwJ,kBAAkB,CAACF,sBAAsB,CAACI,gBAAgB;YAC7D1J,WAAW;QACb;QAEA,8DAA8D;QAC9D,6BAA6B;QAC7B,IAAIJ,mBAAmB;YACrBI,WAAW;QACb;QAEA,OAAO;YACLA;YACAJ;YACArF,aAAa4N,OAAO4B,GAAG,KAAK;YAC5BC,WAAW7B,OAAO4B,GAAG,KAAK;YAC1BlD;YACAD;YACAG;YACAyC;YACAE;YACAE;YACA9C;QACF;IACF,GACCmD,KAAK,CAAC,CAACrB;QACN,IAAIA,IAAIsB,OAAO,KAAK,0BAA0B;YAC5C,MAAMtB;QACR;QACAxR,QAAQ+S,KAAK,CAACvB;QACd,MAAM,qBAAoD,CAApD,IAAI3E,MAAM,CAAC,gCAAgC,EAAEvF,MAAM,GAAnD,qBAAA;mBAAA;wBAAA;0BAAA;QAAmD;IAC3D;AACJ;AAaA;;;;;;CAMC,GACD,OAAO,SAASoK,gBACdH,QAAsC;IAEtC,MAAMR,SAA2B,CAAC;IAElC,KAAK,MAAMiC,WAAWzB,SAAU;QAC9B,MAAM,EACJI,OAAO,EACPsB,UAAU,EACVC,eAAe,EACfzL,UAAU,EACV0L,gBAAgB,EAChB5N,OAAO,EACP6N,WAAW,EACZ,GAAGJ,QAAQjC,MAAM,IAAI,CAAC;QAEvB,uDAAuD;QACvD,6DAA6D;QAE7D,IAAI,OAAOmC,oBAAoB,aAAa;YAC1CnC,OAAOmC,eAAe,GAAGA;QAC3B;QAEA,IAAI,OAAOvB,YAAY,aAAa;YAClCZ,OAAOY,OAAO,GAAGA;QACnB;QAEA,IAAI,OAAOsB,eAAe,aAAa;YACrClC,OAAOkC,UAAU,GAAGA;QACtB;QAEA,IAAI,OAAOxL,eAAe,aAAa;YACrCsJ,OAAOtJ,UAAU,GAAGA;QACtB;QAEA,0EAA0E;QAC1E,sBAAsB;QACtB,IACE,OAAOA,eAAe,YACrB,CAAA,OAAOsJ,OAAOtJ,UAAU,KAAK,YAAYA,aAAasJ,OAAOtJ,UAAU,AAAD,GACvE;YACAsJ,OAAOtJ,UAAU,GAAGA;QACtB;QAEA,wEAAwE;QACxE,oEAAoE;QACpE,IAAI,OAAO0L,qBAAqB,aAAa;YAC3CpC,OAAOoC,gBAAgB,GAAGA;QAC5B;QAEA,IAAI,OAAO5N,YAAY,aAAa;YAClCwL,OAAOxL,OAAO,GAAGA;QACnB;QAEA,IAAI,OAAO6N,gBAAgB,aAAa;YACtCrC,OAAOqC,WAAW,GAAGA;QACvB;IACF;IAEA,OAAOrC;AACT;AAEA,OAAO,eAAesC,yBAAyB,EAC7C/L,IAAI,EACJsG,OAAO,EACPE,gBAAgB,EAChBwF,WAAW,EACXtE,UAAU,EAOX;IACCK,QAAQ,yCAAyCC,SAAS,CAACxB;IAE3D,MAAMyF,aAAa,MAAM5U,eAAe;QACtCiP;QACAtG,MAAMA;QACN8J,WAAW;QACXC,OAAO;QACPrC;IACF;IACA,IAAIqB,MAAMkD,WAAW/C,YAAY;IAEjC,IAAI8C,aAAa;QACfjD,MAAM,AAAC,MAAMA,IAAImD,IAAI,IAAKnD,IAAIK,OAAO,IAAIL;IAC3C,OAAO;QACLA,MAAMA,IAAIK,OAAO,IAAIL;IACvB;IACAA,MAAM,MAAMA;IACZ,OAAOA,IAAI8B,eAAe,KAAK9B,IAAIoD,mBAAmB;AACxD;AAEA,OAAO,eAAeC,uBAAuB,EAC3CpM,IAAI,EACJsG,OAAO,EACPE,gBAAgB,EAChBkB,UAAU,EAMX;IACCK,QAAQ,yCAAyCC,SAAS,CAACxB;IAC3D,MAAMyF,aAAa,MAAM5U,eAAe;QACtCiP;QACAtG,MAAMA;QACN8J,WAAW;QACXC,OAAO;QACPrC;IACF;IAEA,OAAO7M,OAAOqB,IAAI,CAAC+P,WAAW/C,YAAY,EAAErP,MAAM,CAAC,CAACsB;QAClD,OAAO,OAAO8Q,WAAW/C,YAAY,CAAC/N,IAAI,KAAK;IACjD;AACF;AAEA,OAAO,SAASkR,uBACdC,aAAuB,EACvBC,QAAqB,EACrBC,2BAAkD;IAElD,MAAMC,mBAAmB,IAAI/Q;IAQ7B,MAAMgR,kBAAkB;WAAIH;KAAS,CAAC1S,MAAM,CAAC,CAACmG,OAAS/I,eAAe+I;IACtE,MAAM2M,2BAEF,CAAC;IAELH,4BAA4BlM,OAAO,CAAC,CAACkI,OAAOoE;QAC1CD,wBAAwB,CAACC,UAAU,KAAK,CAAC;QACzCpE,MAAMlI,OAAO,CAAC,CAACuM;YACb,MAAMC,cAAcD,QAAQE,WAAW;YACvCJ,wBAAwB,CAACC,UAAU,CAACE,YAAY,GAAGD;QACrD;IACF;IAEAL,4BAA4BlM,OAAO,CAAC,CAACkI,OAAOoE;QAC1CpE,MAAMlI,OAAO,CAAC,CAACuM;YACb,MAAMG,YAAYH,QAAQE,WAAW;YACrC,IAAIE,kBAAkBX,cAAcY,IAAI,CACtC,CAAClN,OAASA,KAAK+M,WAAW,OAAOC;YAGnC,IAAIC,iBAAiB;gBACnBR,iBAAiBpR,GAAG,CAAC2R,WAAW;oBAC9B;wBAAE9W,MAAM2W;wBAAS7M,MAAM4M;oBAAU;oBACjC;wBAAE1W,MAAM+W;wBAAiBjN,MAAMiN;oBAAgB;iBAChD;YACH,OAAO;gBACL,IAAIE;gBAEJF,kBAAkBP,gBAAgBQ,IAAI,CAAC,CAAClN;oBACtC,IAAIA,SAAS4M,WAAW,OAAO;oBAE/BO,kBACEX,4BAA4BjR,GAAG,CAACyE,SAAS,OACrChD,YACA2P,wBAAwB,CAAC3M,KAAK,CAACgN,UAAU;oBAC/C,OAAOG;gBACT;gBAEA,IAAIF,mBAAmBE,iBAAiB;oBACtCV,iBAAiBpR,GAAG,CAAC2R,WAAW;wBAC9B;4BAAE9W,MAAM2W;4BAAS7M,MAAM4M;wBAAU;wBACjC;4BAAE1W,MAAMiX;4BAAiBnN,MAAMiN;wBAAgB;qBAChD;gBACH;YACF;QACF;IACF;IAEA,IAAIR,iBAAiBtT,IAAI,GAAG,GAAG;QAC7B,IAAIiU,yBAAyB;QAE7BX,iBAAiBnM,OAAO,CAAC,CAAC+M;YACxBA,UAAU/M,OAAO,CAAC,CAACgN,UAAU9K;gBAC3B,MAAM+K,YAAYD,SAAStN,IAAI,KAAKsN,SAASpX,IAAI;gBAEjD,IAAIsM,MAAM,GAAG;oBACX4K,0BAA0B;gBAC5B;gBAEAA,0BAA0B,CAAC,OAAO,EAAEE,SAASpX,IAAI,CAAC,CAAC,EACjDqX,YAAY,CAAC,aAAa,EAAED,SAAStN,IAAI,CAAC,EAAE,CAAC,GAAG,KAChD;YACJ;YACAoN,0BAA0B;QAC5B;QAEAhW,IAAIqU,KAAK,CACP,qFACE,mFACA2B;QAEJtO,QAAQ0O,IAAI,CAAC;IACf;AACF;AAEA,OAAO,eAAeC,gBACpBpH,GAAW,EACXC,OAAe,EACfoH,QAA2B,EAC3BC,WAA0C,EAC1CC,WAAmB,EACnBC,YAAgC,EAChCpP,kBAAsC,EACtCqP,iBAA0B,EAC1BC,sBAA+B,EAC/BC,WAAwB;IAExB,MAAMC,aAAa/X,KAAKkG,IAAI,CAACkK,SAAS;IACtC,IAAI4H,aAAa;IACjB,MAAMC,aAAa;QACjB,GAAGN,YAAY;QACfvH,SAAS,CAAC,EAAE,EAAEpQ,KAAKkY,QAAQ,CAAC/H,KAAKC,UAAU;IAC7C;IACA,IAAI;QACF,MAAM+H,kBAAkBnY,KAAKkG,IAAI,CAACkK,SAAS;QAC3C,MAAMgI,qBAAqB,MAAMlY,GAAGmY,QAAQ,CAACF,iBAAiB;QAC9D,MAAMG,cAAcC,KAAKC,KAAK,CAACJ;QAC/BJ,aAAaM,YAAYrK,IAAI,KAAK;QAElC,oDAAoD;QACpD,qDAAqD;QACrD,MAAMwK,wBAAwBzY,KAAKkG,IAAI,CACrC6R,YACA/X,KAAKkY,QAAQ,CAACR,aAAavH,MAC3B;QAEF,MAAMjQ,GAAGwY,KAAK,CAAC1Y,KAAK2Y,OAAO,CAACF,wBAAwB;YAAEG,WAAW;QAAK;QACtE,MAAM1Y,GAAG2Y,SAAS,CAACJ,uBAAuBL;IAC5C,EAAE,OAAM,CAAC;IACT,MAAMU,cAAc,IAAIvV;IACxB,MAAMrD,GAAG6Y,EAAE,CAAChB,YAAY;QAAEa,WAAW;QAAMI,OAAO;IAAK;IAEvD,eAAeC,iBAAiBC,aAAqB;QACnD,MAAMC,YAAYZ,KAAKC,KAAK,CAAC,MAAMtY,GAAGmY,QAAQ,CAACa,eAAe;QAG9D,MAAME,WAAW,IAAI9X,KAAK,IAAI;YAAE+X,UAAUF,UAAUzU,KAAK,CAACiF,MAAM;QAAC;QACjE,MAAM2P,eAAetZ,KAAK2Y,OAAO,CAACO;QAElC,MAAMpT,QAAQC,GAAG,CACfoT,UAAUzU,KAAK,CAACM,GAAG,CAAC,OAAOuU;YACzB,MAAMH,SAASI,OAAO;YAEtB,MAAMC,iBAAiBzZ,KAAKkG,IAAI,CAACoT,cAAcC;YAC/C,MAAMG,iBAAiB1Z,KAAKkG,IAAI,CAC9B6R,YACA/X,KAAKkY,QAAQ,CAACR,aAAa+B;YAG7B,IAAI,CAACX,YAAYjV,GAAG,CAAC6V,iBAAiB;gBACpCZ,YAAYxN,GAAG,CAACoO;gBAEhB,MAAMxZ,GAAGwY,KAAK,CAAC1Y,KAAK2Y,OAAO,CAACe,iBAAiB;oBAAEd,WAAW;gBAAK;gBAC/D,MAAMe,UAAU,MAAMzZ,GAAG0Z,QAAQ,CAACH,gBAAgBpE,KAAK,CAAC,IAAM;gBAE9D,IAAIsE,SAAS;oBACX,IAAI;wBACF,MAAMzZ,GAAGyZ,OAAO,CAACA,SAASD;oBAC5B,EAAE,OAAOpS,GAAQ;wBACf,IAAIA,EAAEuS,IAAI,KAAK,UAAU;4BACvB,MAAMvS;wBACR;oBACF;gBACF,OAAO;oBACL,MAAMpH,GAAG4Z,QAAQ,CAACL,gBAAgBC;gBACpC;YACF;YAEA,MAAMN,SAASW,OAAO;QACxB;IAEJ;IAEA,eAAeC,mBAAmBlQ,IAA4B;YAa1DA,YACAA;QAbF,eAAemQ,WAAWpX,IAAY;YACpC,MAAMqX,eAAela,KAAKkG,IAAI,CAACkK,SAASvN;YACxC,MAAM6W,iBAAiB1Z,KAAKkG,IAAI,CAC9B6R,YACA/X,KAAKkY,QAAQ,CAACR,aAAatH,UAC3BvN;YAEF,MAAM3C,GAAGwY,KAAK,CAAC1Y,KAAK2Y,OAAO,CAACe,iBAAiB;gBAAEd,WAAW;YAAK;YAC/D,MAAM1Y,GAAG4Z,QAAQ,CAACI,cAAcR;QAClC;QACA,MAAM5T,QAAQC,GAAG,CAAC;YAChB+D,KAAKpF,KAAK,CAACM,GAAG,CAACiV;aACfnQ,aAAAA,KAAK0I,IAAI,qBAAT1I,WAAW9E,GAAG,CAAC,CAACnC,OAASoX,WAAWpX,KAAK6P,QAAQ;aACjD5I,eAAAA,KAAKqQ,MAAM,qBAAXrQ,aAAa9E,GAAG,CAAC,CAACnC,OAASoX,WAAWpX,KAAK6P,QAAQ;SACpD;IACH;IAEA,MAAM0H,uBAAuC,EAAE;IAE/C,KAAK,MAAM/M,cAAc1I,OAAO0V,MAAM,CAAC9R,mBAAmB8E,UAAU,EAAG;QACrE,IAAIrG,qBAAqBqG,WAAWsF,IAAI,GAAG;YACzCyH,qBAAqB3T,IAAI,CAACuT,mBAAmB3M;QAC/C;IACF;IAEA,KAAK,MAAMvD,QAAQnF,OAAO0V,MAAM,CAAC9R,mBAAmB+R,SAAS,EAAG;QAC9DF,qBAAqB3T,IAAI,CAACuT,mBAAmBlQ;IAC/C;IAEA,MAAMhE,QAAQC,GAAG,CAACqU;IAElB,KAAK,MAAMtQ,QAAQ0N,SAAU;QAC3B,IAAIjP,mBAAmB+R,SAAS,CAACC,cAAc,CAACzQ,OAAO;YACrD;QACF;QACA,MAAMjC,QAAQrG,kBAAkBsI;QAEhC,IAAIgO,YAAYjU,GAAG,CAACgE,QAAQ;YAC1B;QACF;QAEA,MAAM2S,WAAWxa,KAAKkG,IAAI,CACxBkK,SACA,UACA,SACA,GAAG5O,kBAAkBsI,MAAM,GAAG,CAAC;QAEjC,MAAM2Q,gBAAgB,GAAGD,SAAS,SAAS,CAAC;QAC5C,MAAMvB,iBAAiBwB,eAAepF,KAAK,CAAC,CAACrB;YAC3C,IAAIA,IAAI6F,IAAI,KAAK,YAAa/P,SAAS,UAAUA,SAAS,QAAS;gBACjE5I,IAAIkT,IAAI,CAAC,CAAC,gCAAgC,EAAEoG,UAAU,EAAExG;YAC1D;QACF;IACF;IAEA,IAAI4D,mBAAmB;QACrB,MAAM8C,iBAAiB1a,KAAKkG,IAAI,CAACkK,SAAS,UAAU;QACpD,MAAMuK,kBAAkB,GAAGD,eAAe,SAAS,CAAC;QACpD,MAAMzB,iBAAiB0B;IACzB;IAEA,IAAIlD,aAAa;QACf,KAAK,MAAM3N,QAAQ2N,YAAa;YAC9B,IAAIlP,mBAAmB+R,SAAS,CAACC,cAAc,CAACzQ,OAAO;gBACrD;YACF;YACA,MAAM0Q,WAAWxa,KAAKkG,IAAI,CAACkK,SAAS,UAAU,OAAO,GAAGtG,KAAK,GAAG,CAAC;YACjE,MAAM2Q,gBAAgB,GAAGD,SAAS,SAAS,CAAC;YAC5C,MAAMvB,iBAAiBwB,eAAepF,KAAK,CAAC,CAACrB;gBAC3C9S,IAAIkT,IAAI,CAAC,CAAC,gCAAgC,EAAEoG,UAAU,EAAExG;YAC1D;QACF;IACF;IAEA,IAAI6D,wBAAwB;QAC1B,MAAMoB,iBACJjZ,KAAKkG,IAAI,CAACkK,SAAS,UAAU;IAEjC;IAEA,MAAM6I,iBAAiBjZ,KAAKkG,IAAI,CAACkK,SAAS;IAC1C,MAAMwK,mBAAmB5a,KAAKkG,IAAI,CAChC6R,YACA/X,KAAKkY,QAAQ,CAACR,aAAavH,MAC3B;IAEF,MAAMjQ,GAAGwY,KAAK,CAAC1Y,KAAK2Y,OAAO,CAACiC,mBAAmB;QAAEhC,WAAW;IAAK;IAEjE,MAAM1Y,GAAG2Y,SAAS,CAChB+B,kBACA,GACE5C,aACI,CAAC;;;;;;AAMX,CAAC,GACS,CAAC,4BAA4B,CAAC,CACnC;;;;;;;;;;;mBAWc,EAAEO,KAAKsC,SAAS,CAAC5C,YAAY;;;;;;;;;;;;;;;;;;;;;;;;;;GA0B7C,CAAC;AAEJ;AAEA,OAAO,SAAS6C,eAAehR,IAAY;IACzC,OAAOpH,cAAcqY,IAAI,CAACjR;AAC5B;AAEA,OAAO,SAASkR,yBAAyBlR,IAAY;IACnD,OAAO,8DAA8DiR,IAAI,CACvEjR;AAEJ;AAEA,OAAO,SAASmR,kBAAkBnR,IAAY;IAC5C,OAAOA,SAAS,UAAUA,SAAS;AACrC;AAEA,OAAO,SAASoR,iBAAiBrY,IAAY;IAC3C,OACEA,SAAS,CAAC,CAAC,EAAEpC,qBAAqB,IAAIoC,SAAS,CAAC,KAAK,EAAEpC,qBAAqB;AAEhF;AAEA,OAAO,SAAS0a,0BAA0BtY,IAAY;IACpD,OACEA,SAAS,CAAC,CAAC,EAAEnC,+BAA+B,IAC5CmC,SAAS,CAAC,KAAK,EAAEnC,+BAA+B;AAEpD;AAEA,OAAO,SAAS0a,wCACdC,MAAc,EACdC,UAAoB;IAEpB,MAAM5W,QAAQ,EAAE;IAChB,KAAK,MAAM6W,aAAaD,WAAY;QAClC5W,MAAM+B,IAAI,CACRzG,KAAKkG,IAAI,CAACmV,QAAQ,GAAG3a,8BAA8B,CAAC,EAAE6a,WAAW,GACjEvb,KAAKkG,IAAI,CAACmV,QAAQ,CAAC,GAAG,CAAC,EAAE,GAAG3a,8BAA8B,CAAC,EAAE6a,WAAW;IAE5E;IAEA,OAAO7W;AACT;AAEA,OAAO,SAAS8W,+BACdH,MAAc,EACdC,UAAoB;IAEpB,OAAOA,WAAWtW,GAAG,CAAC,CAACuW,YACrBvb,KAAKkG,IAAI,CAACmV,QAAQ,GAAG5a,oBAAoB,CAAC,EAAE8a,WAAW;AAE3D;AAEA,OAAO,MAAME,8BAA8BpM;IACzCqM,YACEC,eAAyB,EACzBC,OAAe,EACfC,aAAqB,CACrB;QACA,KAAK,CACH,CAAC,0CAA0C,CAAC,GAC1C,GAAGF,gBAAgB3W,GAAG,CAAC,CAACnC,OAAS,CAAC,KAAK,EAAEA,MAAM,EAAEqD,IAAI,CAAC,MAAM,EAAE,CAAC,GAC/D,CAAC,0CAA0C,EAAElG,KAAKkG,IAAI,CACpDlG,KAAK8b,KAAK,CAACC,GAAG,EACd/b,KAAKkY,QAAQ,CAAC0D,SAAS5b,KAAKgc,OAAO,CAACH,eAAe,QACnD,cACA,WAAW,CAAC,GACd,CAAC,8DAA8D,CAAC;IAEtE;AACF;AAEA,OAAO,SAASI,qBACd9L,GAAW,EACX+L,aAAsB;IAEtB,IAAIC;IACJ,IAAI;QACF,MAAMC,qBAAqB/b,aAAagc,UAAU,CAAC;YACjDrc,MAAMmQ;YACNtH,KAAKqT,gBAAgB,gBAAgB;QACvC;QACA,8FAA8F;QAC9F,IAAIE,sBAAsBA,mBAAmBzS,MAAM,GAAG,GAAG;YACvDwS,WAAW9b,aAAa+b;QAC1B;IACF,EAAE,OAAM,CAAC;IAET,6CAA6C;IAC7C,IAAID,YAAYA,SAASxS,MAAM,GAAG,GAAG;QACnC,OAAOwS;IACT;IAEA,uCAAuC;IACvC,OAAOvb;AACT;AAEA,OAAO,SAAS0b,yBACdC,KAA0C;IAE1C,OAAOC,QACLD,SAAS5b,eAAe8b,KAAK,CAACC,UAAU,CAAC/R,QAAQ,CAAC4R;AAEtD;AAEA,OAAO,SAASI,yBACdJ,KAA0C;IAE1C,OAAOC,QACLD,SAAS5b,eAAe8b,KAAK,CAACG,UAAU,CAACjS,QAAQ,CAAC4R;AAEtD;AAEA,OAAO,SAASM,sBACdN,KAA0C;IAE1C,OACEA,UAAU,QACVA,UAAUzV,aACVyV,UAAU5b,eAAemc,eAAe,IACxCP,UAAU5b,eAAeoc,YAAY,IACrCR,UAAU5b,eAAeqc,YAAY;AAEzC;AAEA,OAAO,SAASC,sBACdV,KAA0C;IAE1C,OAAOC,QAAQD,SAAS5b,eAAe8b,KAAK,CAACS,OAAO,CAACvS,QAAQ,CAAC4R;AAChE;AAEA,OAAO,SAASY,uBACdZ,KAA0C;IAE1C,OAAOC,QAAQD,SAAS5b,eAAe8b,KAAK,CAACW,QAAQ,CAACzS,QAAQ,CAAC4R;AACjE;AAEA,OAAO,SAASc,YAAY,EAC1BC,MAAM,EACNvP,OAAO,EAIR;IAIC,MAAMwP,OAGF,CAAC;IAEL,IAAID,WAAW,KAAK;QAClBC,KAAKD,MAAM,GAAGA;IAChB;IAEA,IAAIvP,WAAWpJ,OAAOqB,IAAI,CAAC+H,SAASpE,MAAM,EAAE;QAC1C4T,KAAKxP,OAAO,GAAG,CAAC;QAEhB,4CAA4C;QAC5C,iCAAiC;QACjC,IAAK,MAAM9I,OAAO8I,QAAS;YACzB,qEAAqE;YACrE,sEAAsE;YACtE,IAAI9I,QAAQ,2BAA2B;YAEvC,IAAI4J,QAAQd,OAAO,CAAC9I,IAAI;YAExB,IAAIuY,MAAMC,OAAO,CAAC5O,QAAQ;gBACxB,IAAI5J,QAAQ,cAAc;oBACxB4J,QAAQA,MAAM3I,IAAI,CAAC;gBACrB,OAAO;oBACL2I,QAAQA,KAAK,CAACA,MAAMlF,MAAM,GAAG,EAAE;gBACjC;YACF;YAEA,IAAI,OAAOkF,UAAU,UAAU;gBAC7B0O,KAAKxP,OAAO,CAAC9I,IAAI,GAAG4J;YACtB;QACF;IACF;IAEA,OAAO0O;AACT"}