{"version": 3, "sources": ["../../src/client/route-loader.ts"], "sourcesContent": ["import type { ComponentType } from 'react'\nimport type { MiddlewareMatcher } from '../build/analysis/get-page-static-info'\nimport getAssetPathFromRoute from '../shared/lib/router/utils/get-asset-path-from-route'\nimport { __unsafeCreateTrustedScriptURL } from './trusted-types'\nimport { requestIdleCallback } from './request-idle-callback'\nimport { getDeploymentIdQueryOrEmptyString } from '../build/deployment-id'\nimport { encodeURIPath } from '../shared/lib/encode-uri-path'\n\n// 3.8s was arbitrarily chosen as it's what https://web.dev/interactive\n// considers as \"Good\" time-to-interactive. We must assume something went\n// wrong beyond this point, and then fall-back to a full page transition to\n// show the user something of value.\nconst MS_MAX_IDLE_DELAY = 3800\n\ndeclare global {\n  interface Window {\n    __BUILD_MANIFEST?: Record<string, string[]>\n    __BUILD_MANIFEST_CB?: Function\n    __MIDDLEWARE_MATCHERS?: MiddlewareMatcher[]\n    __MIDDLEWARE_MANIFEST_CB?: Function\n    __REACT_LOADABLE_MANIFEST?: any\n    __DYNAMIC_CSS_MANIFEST?: any\n    __RSC_MANIFEST?: any\n    __RSC_SERVER_MANIFEST?: any\n    __NEXT_FONT_MANIFEST?: any\n    __SUBRESOURCE_INTEGRITY_MANIFEST?: string\n    __INTERCEPTION_ROUTE_REWRITE_MANIFEST?: string\n  }\n}\n\ninterface LoadedEntrypointSuccess {\n  component: ComponentType\n  exports: any\n}\ninterface LoadedEntrypointFailure {\n  error: unknown\n}\ntype RouteEntrypoint = LoadedEntrypointSuccess | LoadedEntrypointFailure\n\ninterface RouteStyleSheet {\n  href: string\n  content: string\n}\n\ninterface LoadedRouteSuccess extends LoadedEntrypointSuccess {\n  styles: RouteStyleSheet[]\n}\ninterface LoadedRouteFailure {\n  error: unknown\n}\ntype RouteLoaderEntry = LoadedRouteSuccess | LoadedRouteFailure\n\ninterface Future<V> {\n  resolve: (entrypoint: V) => void\n  future: Promise<V>\n}\nfunction withFuture<T extends object>(\n  key: string,\n  map: Map<string, Future<T> | T>,\n  generator?: () => Promise<T>\n): Promise<T> {\n  let entry = map.get(key)\n  if (entry) {\n    if ('future' in entry) {\n      return entry.future\n    }\n    return Promise.resolve(entry)\n  }\n  let resolver: (entrypoint: T) => void\n  const prom: Promise<T> = new Promise<T>((resolve) => {\n    resolver = resolve\n  })\n  map.set(key, { resolve: resolver!, future: prom })\n  return generator\n    ? generator()\n        .then((value) => {\n          resolver(value)\n          return value\n        })\n        .catch((err) => {\n          map.delete(key)\n          throw err\n        })\n    : prom\n}\n\nexport interface RouteLoader {\n  whenEntrypoint(route: string): Promise<RouteEntrypoint>\n  onEntrypoint(route: string, execute: () => unknown): void\n  loadRoute(route: string, prefetch?: boolean): Promise<RouteLoaderEntry>\n  prefetch(route: string): Promise<void>\n}\n\nconst ASSET_LOAD_ERROR = Symbol('ASSET_LOAD_ERROR')\n// TODO: unexport\nexport function markAssetError(err: Error): Error {\n  return Object.defineProperty(err, ASSET_LOAD_ERROR, {})\n}\n\nexport function isAssetError(err?: Error): boolean | undefined {\n  return err && ASSET_LOAD_ERROR in err\n}\n\nfunction hasPrefetch(link?: HTMLLinkElement): boolean {\n  try {\n    link = document.createElement('link')\n    return (\n      // detect IE11 since it supports prefetch but isn't detected\n      // with relList.support\n      (!!window.MSInputMethodContext && !!(document as any).documentMode) ||\n      link.relList.supports('prefetch')\n    )\n  } catch {\n    return false\n  }\n}\n\nconst canPrefetch: boolean = hasPrefetch()\n\nconst getAssetQueryString = () => {\n  return getDeploymentIdQueryOrEmptyString()\n}\n\nfunction prefetchViaDom(\n  href: string,\n  as: string,\n  link?: HTMLLinkElement\n): Promise<any> {\n  return new Promise<void>((resolve, reject) => {\n    const selector = `\n      link[rel=\"prefetch\"][href^=\"${href}\"],\n      link[rel=\"preload\"][href^=\"${href}\"],\n      script[src^=\"${href}\"]`\n    if (document.querySelector(selector)) {\n      return resolve()\n    }\n\n    link = document.createElement('link')\n\n    // The order of property assignment here is intentional:\n    if (as) link!.as = as\n    link!.rel = `prefetch`\n    link!.crossOrigin = process.env.__NEXT_CROSS_ORIGIN!\n    link!.onload = resolve as any\n    link!.onerror = () =>\n      reject(markAssetError(new Error(`Failed to prefetch: ${href}`)))\n\n    // `href` should always be last:\n    link!.href = href\n\n    document.head.appendChild(link)\n  })\n}\n\nfunction appendScript(\n  src: TrustedScriptURL | string,\n  script?: HTMLScriptElement\n): Promise<unknown> {\n  return new Promise((resolve, reject) => {\n    script = document.createElement('script')\n\n    // The order of property assignment here is intentional.\n    // 1. Setup success/failure hooks in case the browser synchronously\n    //    executes when `src` is set.\n    script.onload = resolve\n    script.onerror = () =>\n      reject(markAssetError(new Error(`Failed to load script: ${src}`)))\n\n    // 2. Configure the cross-origin attribute before setting `src` in case the\n    //    browser begins to fetch.\n    script.crossOrigin = process.env.__NEXT_CROSS_ORIGIN!\n\n    // 3. Finally, set the source and inject into the DOM in case the child\n    //    must be appended for fetching to start.\n    script.src = src as string\n    document.body.appendChild(script)\n  })\n}\n\n// We wait for pages to be built in dev before we start the route transition\n// timeout to prevent an un-necessary hard navigation in development.\nlet devBuildPromise: Promise<void> | undefined\n\n// Resolve a promise that times out after given amount of milliseconds.\nfunction resolvePromiseWithTimeout<T>(\n  p: Promise<T>,\n  ms: number,\n  err: Error\n): Promise<T> {\n  return new Promise((resolve, reject) => {\n    let cancelled = false\n\n    p.then((r) => {\n      // Resolved, cancel the timeout\n      cancelled = true\n      resolve(r)\n    }).catch(reject)\n\n    // We wrap these checks separately for better dead-code elimination in\n    // production bundles.\n    if (process.env.NODE_ENV === 'development') {\n      ;(devBuildPromise || Promise.resolve()).then(() => {\n        requestIdleCallback(() =>\n          setTimeout(() => {\n            if (!cancelled) {\n              reject(err)\n            }\n          }, ms)\n        )\n      })\n    }\n\n    if (process.env.NODE_ENV !== 'development') {\n      requestIdleCallback(() =>\n        setTimeout(() => {\n          if (!cancelled) {\n            reject(err)\n          }\n        }, ms)\n      )\n    }\n  })\n}\n\n// TODO: stop exporting or cache the failure\n// It'd be best to stop exporting this. It's an implementation detail. We're\n// only exporting it for backwards compatibility with the `page-loader`.\n// Only cache this response as a last resort if we cannot eliminate all other\n// code branches that use the Build Manifest Callback and push them through\n// the Route Loader interface.\nexport function getClientBuildManifest() {\n  if (self.__BUILD_MANIFEST) {\n    return Promise.resolve(self.__BUILD_MANIFEST)\n  }\n\n  const onBuildManifest = new Promise<Record<string, string[]>>((resolve) => {\n    // Mandatory because this is not concurrent safe:\n    const cb = self.__BUILD_MANIFEST_CB\n    self.__BUILD_MANIFEST_CB = () => {\n      resolve(self.__BUILD_MANIFEST!)\n      cb && cb()\n    }\n  })\n\n  return resolvePromiseWithTimeout(\n    onBuildManifest,\n    MS_MAX_IDLE_DELAY,\n    markAssetError(new Error('Failed to load client build manifest'))\n  )\n}\n\ninterface RouteFiles {\n  scripts: (TrustedScriptURL | string)[]\n  css: string[]\n}\nfunction getFilesForRoute(\n  assetPrefix: string,\n  route: string\n): Promise<RouteFiles> {\n  if (process.env.NODE_ENV === 'development') {\n    const scriptUrl =\n      assetPrefix +\n      '/_next/static/chunks/pages' +\n      encodeURIPath(getAssetPathFromRoute(route, '.js')) +\n      getAssetQueryString()\n    return Promise.resolve({\n      scripts: [__unsafeCreateTrustedScriptURL(scriptUrl)],\n      // Styles are handled by `style-loader` in development:\n      css: [],\n    })\n  }\n  return getClientBuildManifest().then((manifest) => {\n    if (!(route in manifest)) {\n      throw markAssetError(new Error(`Failed to lookup route: ${route}`))\n    }\n    const allFiles = manifest[route].map(\n      (entry) => assetPrefix + '/_next/' + encodeURIPath(entry)\n    )\n    return {\n      scripts: allFiles\n        .filter((v) => v.endsWith('.js'))\n        .map((v) => __unsafeCreateTrustedScriptURL(v) + getAssetQueryString()),\n      css: allFiles\n        .filter((v) => v.endsWith('.css'))\n        .map((v) => v + getAssetQueryString()),\n    }\n  })\n}\n\nexport function createRouteLoader(assetPrefix: string): RouteLoader {\n  const entrypoints: Map<string, Future<RouteEntrypoint> | RouteEntrypoint> =\n    new Map()\n  const loadedScripts: Map<string, Promise<unknown>> = new Map()\n  const styleSheets: Map<string, Promise<RouteStyleSheet>> = new Map()\n  const routes: Map<string, Future<RouteLoaderEntry> | RouteLoaderEntry> =\n    new Map()\n\n  function maybeExecuteScript(\n    src: TrustedScriptURL | string\n  ): Promise<unknown> {\n    // With HMR we might need to \"reload\" scripts when they are\n    // disposed and readded. Executing scripts twice has no functional\n    // differences\n    if (process.env.NODE_ENV !== 'development') {\n      let prom: Promise<unknown> | undefined = loadedScripts.get(src.toString())\n      if (prom) {\n        return prom\n      }\n\n      // Skip executing script if it's already in the DOM:\n      if (document.querySelector(`script[src^=\"${src}\"]`)) {\n        return Promise.resolve()\n      }\n\n      loadedScripts.set(src.toString(), (prom = appendScript(src)))\n      return prom\n    } else {\n      return appendScript(src)\n    }\n  }\n\n  function fetchStyleSheet(href: string): Promise<RouteStyleSheet> {\n    let prom: Promise<RouteStyleSheet> | undefined = styleSheets.get(href)\n    if (prom) {\n      return prom\n    }\n\n    styleSheets.set(\n      href,\n      (prom = fetch(href, { credentials: 'same-origin' })\n        .then((res) => {\n          if (!res.ok) {\n            throw new Error(`Failed to load stylesheet: ${href}`)\n          }\n          return res.text().then((text) => ({ href: href, content: text }))\n        })\n        .catch((err) => {\n          throw markAssetError(err)\n        }))\n    )\n    return prom\n  }\n\n  return {\n    whenEntrypoint(route: string) {\n      return withFuture(route, entrypoints)\n    },\n    onEntrypoint(route: string, execute: undefined | (() => unknown)) {\n      ;(execute\n        ? Promise.resolve()\n            .then(() => execute())\n            .then(\n              (exports: any) => ({\n                component: (exports && exports.default) || exports,\n                exports: exports,\n              }),\n              (err) => ({ error: err })\n            )\n        : Promise.resolve(undefined)\n      ).then((input: RouteEntrypoint | undefined) => {\n        const old = entrypoints.get(route)\n        if (old && 'resolve' in old) {\n          if (input) {\n            entrypoints.set(route, input)\n            old.resolve(input)\n          }\n        } else {\n          if (input) {\n            entrypoints.set(route, input)\n          } else {\n            entrypoints.delete(route)\n          }\n          // when this entrypoint has been resolved before\n          // the route is outdated and we want to invalidate\n          // this cache entry\n          routes.delete(route)\n        }\n      })\n    },\n    loadRoute(route: string, prefetch?: boolean) {\n      return withFuture<RouteLoaderEntry>(route, routes, () => {\n        let devBuildPromiseResolve: () => void\n\n        if (process.env.NODE_ENV === 'development') {\n          devBuildPromise = new Promise<void>((resolve) => {\n            devBuildPromiseResolve = resolve\n          })\n        }\n\n        return resolvePromiseWithTimeout(\n          getFilesForRoute(assetPrefix, route)\n            .then(({ scripts, css }) => {\n              return Promise.all([\n                entrypoints.has(route)\n                  ? []\n                  : Promise.all(scripts.map(maybeExecuteScript)),\n                Promise.all(css.map(fetchStyleSheet)),\n              ] as const)\n            })\n            .then((res) => {\n              return this.whenEntrypoint(route).then((entrypoint) => ({\n                entrypoint,\n                styles: res[1],\n              }))\n            }),\n          MS_MAX_IDLE_DELAY,\n          markAssetError(new Error(`Route did not complete loading: ${route}`))\n        )\n          .then(({ entrypoint, styles }) => {\n            const res: RouteLoaderEntry = Object.assign<\n              { styles: RouteStyleSheet[] },\n              RouteEntrypoint\n            >({ styles: styles! }, entrypoint)\n            return 'error' in entrypoint ? entrypoint : res\n          })\n          .catch((err) => {\n            if (prefetch) {\n              // we don't want to cache errors during prefetch\n              throw err\n            }\n            return { error: err }\n          })\n          .finally(() => devBuildPromiseResolve?.())\n      })\n    },\n    prefetch(route: string): Promise<void> {\n      // https://github.com/GoogleChromeLabs/quicklink/blob/453a661fa1fa940e2d2e044452398e38c67a98fb/src/index.mjs#L115-L118\n      // License: Apache 2.0\n      let cn\n      if ((cn = (navigator as any).connection)) {\n        // Don't prefetch if using 2G or if Save-Data is enabled.\n        if (cn.saveData || /2g/.test(cn.effectiveType)) return Promise.resolve()\n      }\n      return getFilesForRoute(assetPrefix, route)\n        .then((output) =>\n          Promise.all(\n            canPrefetch\n              ? output.scripts.map((script) =>\n                  prefetchViaDom(script.toString(), 'script')\n                )\n              : []\n          )\n        )\n        .then(() => {\n          requestIdleCallback(() => this.loadRoute(route, true).catch(() => {}))\n        })\n        .catch(\n          // swallow prefetch errors\n          () => {}\n        )\n    },\n  }\n}\n"], "names": ["getAssetPathFromRoute", "__unsafeCreateTrustedScriptURL", "requestIdleCallback", "getDeploymentIdQueryOrEmptyString", "encodeURIPath", "MS_MAX_IDLE_DELAY", "withFuture", "key", "map", "generator", "entry", "get", "future", "Promise", "resolve", "resolver", "prom", "set", "then", "value", "catch", "err", "delete", "ASSET_LOAD_ERROR", "Symbol", "<PERSON><PERSON><PERSON><PERSON>", "Object", "defineProperty", "isAssetError", "hasPrefetch", "link", "document", "createElement", "window", "MSInputMethodContext", "documentMode", "relList", "supports", "canPrefetch", "getAssetQueryString", "prefetchViaDom", "href", "as", "reject", "selector", "querySelector", "rel", "crossOrigin", "process", "env", "__NEXT_CROSS_ORIGIN", "onload", "onerror", "Error", "head", "append<PERSON><PERSON><PERSON>", "appendScript", "src", "script", "body", "devBuildPromise", "resolvePromiseWithTimeout", "p", "ms", "cancelled", "r", "NODE_ENV", "setTimeout", "getClientBuildManifest", "self", "__BUILD_MANIFEST", "onBuildManifest", "cb", "__BUILD_MANIFEST_CB", "getFilesForRoute", "assetPrefix", "route", "scriptUrl", "scripts", "css", "manifest", "allFiles", "filter", "v", "endsWith", "createRouteLoader", "entrypoints", "Map", "loadedScripts", "styleSheets", "routes", "maybeExecuteScript", "toString", "fetchStyleSheet", "fetch", "credentials", "res", "ok", "text", "content", "whenEntrypoint", "onEntrypoint", "execute", "exports", "component", "default", "error", "undefined", "input", "old", "loadRoute", "prefetch", "devBuildPromiseResolve", "all", "has", "entrypoint", "styles", "assign", "finally", "cn", "navigator", "connection", "saveData", "test", "effectiveType", "output"], "mappings": "AAEA,OAAOA,2BAA2B,uDAAsD;AACxF,SAASC,8BAA8B,QAAQ,kBAAiB;AAChE,SAASC,mBAAmB,QAAQ,0BAAyB;AAC7D,SAASC,iCAAiC,QAAQ,yBAAwB;AAC1E,SAASC,aAAa,QAAQ,gCAA+B;AAE7D,uEAAuE;AACvE,yEAAyE;AACzE,2EAA2E;AAC3E,oCAAoC;AACpC,MAAMC,oBAAoB;AA4C1B,SAASC,WACPC,GAAW,EACXC,GAA+B,EAC/BC,SAA4B;IAE5B,IAAIC,QAAQF,IAAIG,GAAG,CAACJ;IACpB,IAAIG,OAAO;QACT,IAAI,YAAYA,OAAO;YACrB,OAAOA,MAAME,MAAM;QACrB;QACA,OAAOC,QAAQC,OAAO,CAACJ;IACzB;IACA,IAAIK;IACJ,MAAMC,OAAmB,IAAIH,QAAW,CAACC;QACvCC,WAAWD;IACb;IACAN,IAAIS,GAAG,CAACV,KAAK;QAAEO,SAASC;QAAWH,QAAQI;IAAK;IAChD,OAAOP,YACHA,YACGS,IAAI,CAAC,CAACC;QACLJ,SAASI;QACT,OAAOA;IACT,GACCC,KAAK,CAAC,CAACC;QACNb,IAAIc,MAAM,CAACf;QACX,MAAMc;IACR,KACFL;AACN;AASA,MAAMO,mBAAmBC,OAAO;AAChC,iBAAiB;AACjB,OAAO,SAASC,eAAeJ,GAAU;IACvC,OAAOK,OAAOC,cAAc,CAACN,KAAKE,kBAAkB,CAAC;AACvD;AAEA,OAAO,SAASK,aAAaP,GAAW;IACtC,OAAOA,OAAOE,oBAAoBF;AACpC;AAEA,SAASQ,YAAYC,IAAsB;IACzC,IAAI;QACFA,OAAOC,SAASC,aAAa,CAAC;QAC9B,OAGE,AAFA,4DAA4D;QAC5D,uBAAuB;QACtB,CAAC,CAACC,OAAOC,oBAAoB,IAAI,CAAC,CAAC,AAACH,SAAiBI,YAAY,IAClEL,KAAKM,OAAO,CAACC,QAAQ,CAAC;IAE1B,EAAE,UAAM;QACN,OAAO;IACT;AACF;AAEA,MAAMC,cAAuBT;AAE7B,MAAMU,sBAAsB;IAC1B,OAAOpC;AACT;AAEA,SAASqC,eACPC,IAAY,EACZC,EAAU,EACVZ,IAAsB;IAEtB,OAAO,IAAIjB,QAAc,CAACC,SAAS6B;QACjC,MAAMC,WAAW,AAAC,yCACcH,OAAK,2CACNA,OAAK,6BACnBA,OAAK;QACtB,IAAIV,SAASc,aAAa,CAACD,WAAW;YACpC,OAAO9B;QACT;QAEAgB,OAAOC,SAASC,aAAa,CAAC;QAE9B,wDAAwD;QACxD,IAAIU,IAAIZ,KAAMY,EAAE,GAAGA;QACnBZ,KAAMgB,GAAG,GAAI;QACbhB,KAAMiB,WAAW,GAAGC,QAAQC,GAAG,CAACC,mBAAmB;QACnDpB,KAAMqB,MAAM,GAAGrC;QACfgB,KAAMsB,OAAO,GAAG,IACdT,OAAOlB,eAAe,qBAAwC,CAAxC,IAAI4B,MAAM,AAAC,yBAAsBZ,OAAjC,qBAAA;uBAAA;4BAAA;8BAAA;YAAuC;QAE/D,gCAAgC;QAChCX,KAAMW,IAAI,GAAGA;QAEbV,SAASuB,IAAI,CAACC,WAAW,CAACzB;IAC5B;AACF;AAEA,SAAS0B,aACPC,GAA8B,EAC9BC,MAA0B;IAE1B,OAAO,IAAI7C,QAAQ,CAACC,SAAS6B;QAC3Be,SAAS3B,SAASC,aAAa,CAAC;QAEhC,wDAAwD;QACxD,mEAAmE;QACnE,iCAAiC;QACjC0B,OAAOP,MAAM,GAAGrC;QAChB4C,OAAON,OAAO,GAAG,IACfT,OAAOlB,eAAe,qBAA0C,CAA1C,IAAI4B,MAAM,AAAC,4BAAyBI,MAApC,qBAAA;uBAAA;4BAAA;8BAAA;YAAyC;QAEjE,2EAA2E;QAC3E,8BAA8B;QAC9BC,OAAOX,WAAW,GAAGC,QAAQC,GAAG,CAACC,mBAAmB;QAEpD,uEAAuE;QACvE,6CAA6C;QAC7CQ,OAAOD,GAAG,GAAGA;QACb1B,SAAS4B,IAAI,CAACJ,WAAW,CAACG;IAC5B;AACF;AAEA,4EAA4E;AAC5E,qEAAqE;AACrE,IAAIE;AAEJ,uEAAuE;AACvE,SAASC,0BACPC,CAAa,EACbC,EAAU,EACV1C,GAAU;IAEV,OAAO,IAAIR,QAAQ,CAACC,SAAS6B;QAC3B,IAAIqB,YAAY;QAEhBF,EAAE5C,IAAI,CAAC,CAAC+C;YACN,+BAA+B;YAC/BD,YAAY;YACZlD,QAAQmD;QACV,GAAG7C,KAAK,CAACuB;QAET,sEAAsE;QACtE,sBAAsB;QACtB,IAAIK,QAAQC,GAAG,CAACiB,QAAQ,KAAK,eAAe;;YACxCN,CAAAA,mBAAmB/C,QAAQC,OAAO,EAAC,EAAGI,IAAI,CAAC;gBAC3ChB,oBAAoB,IAClBiE,WAAW;wBACT,IAAI,CAACH,WAAW;4BACdrB,OAAOtB;wBACT;oBACF,GAAG0C;YAEP;QACF;QAEA,IAAIf,QAAQC,GAAG,CAACiB,QAAQ,KAAK,eAAe;YAC1ChE,oBAAoB,IAClBiE,WAAW;oBACT,IAAI,CAACH,WAAW;wBACdrB,OAAOtB;oBACT;gBACF,GAAG0C;QAEP;IACF;AACF;AAEA,4CAA4C;AAC5C,4EAA4E;AAC5E,wEAAwE;AACxE,6EAA6E;AAC7E,2EAA2E;AAC3E,8BAA8B;AAC9B,OAAO,SAASK;IACd,IAAIC,KAAKC,gBAAgB,EAAE;QACzB,OAAOzD,QAAQC,OAAO,CAACuD,KAAKC,gBAAgB;IAC9C;IAEA,MAAMC,kBAAkB,IAAI1D,QAAkC,CAACC;QAC7D,iDAAiD;QACjD,MAAM0D,KAAKH,KAAKI,mBAAmB;QACnCJ,KAAKI,mBAAmB,GAAG;YACzB3D,QAAQuD,KAAKC,gBAAgB;YAC7BE,MAAMA;QACR;IACF;IAEA,OAAOX,0BACLU,iBACAlE,mBACAoB,eAAe,qBAAiD,CAAjD,IAAI4B,MAAM,yCAAV,qBAAA;eAAA;oBAAA;sBAAA;IAAgD;AAEnE;AAMA,SAASqB,iBACPC,WAAmB,EACnBC,KAAa;IAEb,IAAI5B,QAAQC,GAAG,CAACiB,QAAQ,KAAK,eAAe;QAC1C,MAAMW,YACJF,cACA,+BACAvE,cAAcJ,sBAAsB4E,OAAO,UAC3CrC;QACF,OAAO1B,QAAQC,OAAO,CAAC;YACrBgE,SAAS;gBAAC7E,+BAA+B4E;aAAW;YACpD,uDAAuD;YACvDE,KAAK,EAAE;QACT;IACF;IACA,OAAOX,yBAAyBlD,IAAI,CAAC,CAAC8D;QACpC,IAAI,CAAEJ,CAAAA,SAASI,QAAO,GAAI;YACxB,MAAMvD,eAAe,qBAA6C,CAA7C,IAAI4B,MAAM,AAAC,6BAA0BuB,QAArC,qBAAA;uBAAA;4BAAA;8BAAA;YAA4C;QACnE;QACA,MAAMK,WAAWD,QAAQ,CAACJ,MAAM,CAACpE,GAAG,CAClC,CAACE,QAAUiE,cAAc,YAAYvE,cAAcM;QAErD,OAAO;YACLoE,SAASG,SACNC,MAAM,CAAC,CAACC,IAAMA,EAAEC,QAAQ,CAAC,QACzB5E,GAAG,CAAC,CAAC2E,IAAMlF,+BAA+BkF,KAAK5C;YAClDwC,KAAKE,SACFC,MAAM,CAAC,CAACC,IAAMA,EAAEC,QAAQ,CAAC,SACzB5E,GAAG,CAAC,CAAC2E,IAAMA,IAAI5C;QACpB;IACF;AACF;AAEA,OAAO,SAAS8C,kBAAkBV,WAAmB;IACnD,MAAMW,cACJ,IAAIC;IACN,MAAMC,gBAA+C,IAAID;IACzD,MAAME,cAAqD,IAAIF;IAC/D,MAAMG,SACJ,IAAIH;IAEN,SAASI,mBACPlC,GAA8B;QAE9B,2DAA2D;QAC3D,kEAAkE;QAClE,cAAc;QACd,IAAIT,QAAQC,GAAG,CAACiB,QAAQ,KAAK,eAAe;YAC1C,IAAIlD,OAAqCwE,cAAc7E,GAAG,CAAC8C,IAAImC,QAAQ;YACvE,IAAI5E,MAAM;gBACR,OAAOA;YACT;YAEA,oDAAoD;YACpD,IAAIe,SAASc,aAAa,CAAC,AAAC,kBAAeY,MAAI,OAAM;gBACnD,OAAO5C,QAAQC,OAAO;YACxB;YAEA0E,cAAcvE,GAAG,CAACwC,IAAImC,QAAQ,IAAK5E,OAAOwC,aAAaC;YACvD,OAAOzC;QACT,OAAO;YACL,OAAOwC,aAAaC;QACtB;IACF;IAEA,SAASoC,gBAAgBpD,IAAY;QACnC,IAAIzB,OAA6CyE,YAAY9E,GAAG,CAAC8B;QACjE,IAAIzB,MAAM;YACR,OAAOA;QACT;QAEAyE,YAAYxE,GAAG,CACbwB,MACCzB,OAAO8E,MAAMrD,MAAM;YAAEsD,aAAa;QAAc,GAC9C7E,IAAI,CAAC,CAAC8E;YACL,IAAI,CAACA,IAAIC,EAAE,EAAE;gBACX,MAAM,qBAA+C,CAA/C,IAAI5C,MAAM,AAAC,gCAA6BZ,OAAxC,qBAAA;2BAAA;gCAAA;kCAAA;gBAA8C;YACtD;YACA,OAAOuD,IAAIE,IAAI,GAAGhF,IAAI,CAAC,CAACgF,OAAU,CAAA;oBAAEzD,MAAMA;oBAAM0D,SAASD;gBAAK,CAAA;QAChE,GACC9E,KAAK,CAAC,CAACC;YACN,MAAMI,eAAeJ;QACvB;QAEJ,OAAOL;IACT;IAEA,OAAO;QACLoF,gBAAexB,KAAa;YAC1B,OAAOtE,WAAWsE,OAAOU;QAC3B;QACAe,cAAazB,KAAa,EAAE0B,OAAoC;;YAC5DA,CAAAA,UACEzF,QAAQC,OAAO,GACZI,IAAI,CAAC,IAAMoF,WACXpF,IAAI,CACH,CAACqF,UAAkB,CAAA;oBACjBC,WAAW,AAACD,WAAWA,QAAQE,OAAO,IAAKF;oBAC3CA,SAASA;gBACX,CAAA,GACA,CAAClF,MAAS,CAAA;oBAAEqF,OAAOrF;gBAAI,CAAA,KAE3BR,QAAQC,OAAO,CAAC6F,UAAS,EAC3BzF,IAAI,CAAC,CAAC0F;gBACN,MAAMC,MAAMvB,YAAY3E,GAAG,CAACiE;gBAC5B,IAAIiC,OAAO,aAAaA,KAAK;oBAC3B,IAAID,OAAO;wBACTtB,YAAYrE,GAAG,CAAC2D,OAAOgC;wBACvBC,IAAI/F,OAAO,CAAC8F;oBACd;gBACF,OAAO;oBACL,IAAIA,OAAO;wBACTtB,YAAYrE,GAAG,CAAC2D,OAAOgC;oBACzB,OAAO;wBACLtB,YAAYhE,MAAM,CAACsD;oBACrB;oBACA,gDAAgD;oBAChD,kDAAkD;oBAClD,mBAAmB;oBACnBc,OAAOpE,MAAM,CAACsD;gBAChB;YACF;QACF;QACAkC,WAAUlC,KAAa,EAAEmC,QAAkB;YACzC,OAAOzG,WAA6BsE,OAAOc,QAAQ;gBACjD,IAAIsB;gBAEJ,IAAIhE,QAAQC,GAAG,CAACiB,QAAQ,KAAK,eAAe;oBAC1CN,kBAAkB,IAAI/C,QAAc,CAACC;wBACnCkG,yBAAyBlG;oBAC3B;gBACF;gBAEA,OAAO+C,0BACLa,iBAAiBC,aAAaC,OAC3B1D,IAAI,CAAC;wBAAC,EAAE4D,OAAO,EAAEC,GAAG,EAAE;oBACrB,OAAOlE,QAAQoG,GAAG,CAAC;wBACjB3B,YAAY4B,GAAG,CAACtC,SACZ,EAAE,GACF/D,QAAQoG,GAAG,CAACnC,QAAQtE,GAAG,CAACmF;wBAC5B9E,QAAQoG,GAAG,CAAClC,IAAIvE,GAAG,CAACqF;qBACrB;gBACH,GACC3E,IAAI,CAAC,CAAC8E;oBACL,OAAO,IAAI,CAACI,cAAc,CAACxB,OAAO1D,IAAI,CAAC,CAACiG,aAAgB,CAAA;4BACtDA;4BACAC,QAAQpB,GAAG,CAAC,EAAE;wBAChB,CAAA;gBACF,IACF3F,mBACAoB,eAAe,qBAAqD,CAArD,IAAI4B,MAAM,AAAC,qCAAkCuB,QAA7C,qBAAA;2BAAA;gCAAA;kCAAA;gBAAoD,KAElE1D,IAAI,CAAC;wBAAC,EAAEiG,UAAU,EAAEC,MAAM,EAAE;oBAC3B,MAAMpB,MAAwBtE,OAAO2F,MAAM,CAGzC;wBAAED,QAAQA;oBAAQ,GAAGD;oBACvB,OAAO,WAAWA,aAAaA,aAAanB;gBAC9C,GACC5E,KAAK,CAAC,CAACC;oBACN,IAAI0F,UAAU;wBACZ,gDAAgD;wBAChD,MAAM1F;oBACR;oBACA,OAAO;wBAAEqF,OAAOrF;oBAAI;gBACtB,GACCiG,OAAO,CAAC,IAAMN,0CAAAA;YACnB;QACF;QACAD,UAASnC,KAAa;YACpB,sHAAsH;YACtH,sBAAsB;YACtB,IAAI2C;YACJ,IAAKA,KAAK,AAACC,UAAkBC,UAAU,EAAG;gBACxC,yDAAyD;gBACzD,IAAIF,GAAGG,QAAQ,IAAI,KAAKC,IAAI,CAACJ,GAAGK,aAAa,GAAG,OAAO/G,QAAQC,OAAO;YACxE;YACA,OAAO4D,iBAAiBC,aAAaC,OAClC1D,IAAI,CAAC,CAAC2G,SACLhH,QAAQoG,GAAG,CACT3E,cACIuF,OAAO/C,OAAO,CAACtE,GAAG,CAAC,CAACkD,SAClBlB,eAAekB,OAAOkC,QAAQ,IAAI,aAEpC,EAAE,GAGT1E,IAAI,CAAC;gBACJhB,oBAAoB,IAAM,IAAI,CAAC4G,SAAS,CAAClC,OAAO,MAAMxD,KAAK,CAAC,KAAO;YACrE,GACCA,KAAK,CACJ,0BAA0B;YAC1B,KAAO;QAEb;IACF;AACF"}