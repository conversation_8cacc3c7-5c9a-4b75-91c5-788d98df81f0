{"version": 3, "sources": ["../../src/server/render.tsx"], "sourcesContent": ["import type { IncomingMessage, ServerResponse } from 'http'\nimport type { ParsedUrlQuery } from 'querystring'\nimport type { NextRouter } from '../shared/lib/router/router'\nimport type { HtmlProps } from '../shared/lib/html-context.shared-runtime'\nimport type { DomainLocale } from './config'\nimport type {\n  AppType,\n  DocumentInitialProps,\n  DocumentType,\n  DocumentProps,\n  DocumentContext,\n  NextComponentType,\n  RenderPage,\n  RenderPageResult,\n} from '../shared/lib/utils'\nimport type { ImageConfigComplete } from '../shared/lib/image-config'\nimport type { Redirect } from '../lib/load-custom-routes'\nimport {\n  type NextApiRequestCookies,\n  type __ApiPreviewProps,\n  setLazyProp,\n} from './api-utils'\nimport { getCookieParser } from './api-utils/get-cookie-parser'\nimport type { LoadComponentsReturnType } from './load-components'\nimport type {\n  GetServerSideProps,\n  GetStaticProps,\n  PreviewData,\n  ServerRuntime,\n  SizeLimit,\n} from '../types'\nimport type { UnwrapPromise } from '../lib/coalesced-function'\nimport type { ReactReadableStream } from './stream-utils/node-web-streams-helper'\nimport type { ClientReferenceManifest } from '../build/webpack/plugins/flight-manifest-plugin'\nimport type { NextFontManifest } from '../build/webpack/plugins/next-font-manifest-plugin'\nimport type { PagesModule } from './route-modules/pages/module'\nimport type { ComponentsEnhancer } from '../shared/lib/utils'\nimport type { NextParsedUrlQuery } from './request-meta'\nimport type { Revalidate } from './lib/cache-control'\nimport type { COMPILER_NAMES } from '../shared/lib/constants'\n\nimport React, { type JSX } from 'react'\nimport ReactDOMServerPages from 'next/dist/server/ReactDOMServerPages'\nimport { StyleRegistry, createStyleRegistry } from 'styled-jsx'\nimport {\n  GSP_NO_RETURNED_VALUE,\n  GSSP_COMPONENT_MEMBER_ERROR,\n  GSSP_NO_RETURNED_VALUE,\n  STATIC_STATUS_PAGE_GET_INITIAL_PROPS_ERROR,\n  SERVER_PROPS_GET_INIT_PROPS_CONFLICT,\n  SERVER_PROPS_SSG_CONFLICT,\n  SSG_GET_INITIAL_PROPS_CONFLICT,\n  UNSTABLE_REVALIDATE_RENAME_ERROR,\n} from '../lib/constants'\nimport {\n  NEXT_BUILTIN_DOCUMENT,\n  SERVER_PROPS_ID,\n  STATIC_PROPS_ID,\n  STATIC_STATUS_PAGES,\n} from '../shared/lib/constants'\nimport { isSerializableProps } from '../lib/is-serializable-props'\nimport { isInAmpMode } from '../shared/lib/amp-mode'\nimport { AmpStateContext } from '../shared/lib/amp-context.shared-runtime'\nimport { defaultHead } from '../shared/lib/head'\nimport { HeadManagerContext } from '../shared/lib/head-manager-context.shared-runtime'\nimport Loadable from '../shared/lib/loadable.shared-runtime'\nimport { LoadableContext } from '../shared/lib/loadable-context.shared-runtime'\nimport { RouterContext } from '../shared/lib/router-context.shared-runtime'\nimport { isDynamicRoute } from '../shared/lib/router/utils/is-dynamic'\nimport {\n  getDisplayName,\n  isResSent,\n  loadGetInitialProps,\n} from '../shared/lib/utils'\nimport { HtmlContext } from '../shared/lib/html-context.shared-runtime'\nimport { normalizePagePath } from '../shared/lib/page-path/normalize-page-path'\nimport { denormalizePagePath } from '../shared/lib/page-path/denormalize-page-path'\nimport { getRequestMeta } from './request-meta'\nimport { allowedStatusCodes, getRedirectStatus } from '../lib/redirect-status'\nimport RenderResult, { type PagesRenderResultMetadata } from './render-result'\nimport isError from '../lib/is-error'\nimport {\n  streamToString,\n  renderToInitialFizzStream,\n} from './stream-utils/node-web-streams-helper'\nimport { ImageConfigContext } from '../shared/lib/image-config-context.shared-runtime'\nimport stripAnsi from 'next/dist/compiled/strip-ansi'\nimport { stripInternalQueries } from './internal-utils'\nimport {\n  adaptForAppRouterInstance,\n  adaptForPathParams,\n  adaptForSearchParams,\n  PathnameContextProviderAdapter,\n} from '../shared/lib/router/adapters'\nimport { AppRouterContext } from '../shared/lib/app-router-context.shared-runtime'\nimport {\n  SearchParamsContext,\n  PathParamsContext,\n} from '../shared/lib/hooks-client-context.shared-runtime'\nimport { getTracer } from './lib/trace/tracer'\nimport { RenderSpan } from './lib/trace/constants'\nimport { ReflectAdapter } from './web/spec-extension/adapters/reflect'\nimport { getCacheControlHeader } from './lib/cache-control'\nimport { getErrorSource } from '../shared/lib/error-source'\nimport type { DeepReadonly } from '../shared/lib/deep-readonly'\nimport type { PagesDevOverlayType } from '../client/components/react-dev-overlay/pages/pages-dev-overlay'\n\nlet tryGetPreviewData: typeof import('./api-utils/node/try-get-preview-data').tryGetPreviewData\nlet warn: typeof import('../build/output/log').warn\nlet postProcessHTML: typeof import('./post-process').postProcessHTML\n\nconst DOCTYPE = '<!DOCTYPE html>'\n\nif (process.env.NEXT_RUNTIME !== 'edge') {\n  tryGetPreviewData =\n    require('./api-utils/node/try-get-preview-data').tryGetPreviewData\n  warn = require('../build/output/log').warn\n  postProcessHTML = require('./post-process').postProcessHTML\n} else {\n  warn = console.warn.bind(console)\n  postProcessHTML = async (_pathname: string, html: string) => html\n}\n\nfunction noRouter() {\n  const message =\n    'No router instance found. you should only use \"next/router\" inside the client side of your app. https://nextjs.org/docs/messages/no-router-instance'\n  throw new Error(message)\n}\n\nasync function renderToString(element: React.ReactElement) {\n  const renderStream = await ReactDOMServerPages.renderToReadableStream(element)\n  await renderStream.allReady\n  return streamToString(renderStream)\n}\n\nclass ServerRouter implements NextRouter {\n  route: string\n  pathname: string\n  query: ParsedUrlQuery\n  asPath: string\n  basePath: string\n  events: any\n  isFallback: boolean\n  locale?: string\n  isReady: boolean\n  locales?: readonly string[]\n  defaultLocale?: string\n  domainLocales?: readonly DomainLocale[]\n  isPreview: boolean\n  isLocaleDomain: boolean\n\n  constructor(\n    pathname: string,\n    query: ParsedUrlQuery,\n    as: string,\n    { isFallback }: { isFallback: boolean },\n    isReady: boolean,\n    basePath: string,\n    locale?: string,\n    locales?: readonly string[],\n    defaultLocale?: string,\n    domainLocales?: readonly DomainLocale[],\n    isPreview?: boolean,\n    isLocaleDomain?: boolean\n  ) {\n    this.route = pathname.replace(/\\/$/, '') || '/'\n    this.pathname = pathname\n    this.query = query\n    this.asPath = as\n    this.isFallback = isFallback\n    this.basePath = basePath\n    this.locale = locale\n    this.locales = locales\n    this.defaultLocale = defaultLocale\n    this.isReady = isReady\n    this.domainLocales = domainLocales\n    this.isPreview = !!isPreview\n    this.isLocaleDomain = !!isLocaleDomain\n  }\n\n  push(): any {\n    noRouter()\n  }\n  replace(): any {\n    noRouter()\n  }\n  reload() {\n    noRouter()\n  }\n  back() {\n    noRouter()\n  }\n  forward(): void {\n    noRouter()\n  }\n  prefetch(): any {\n    noRouter()\n  }\n  beforePopState() {\n    noRouter()\n  }\n}\n\nfunction enhanceComponents(\n  options: ComponentsEnhancer,\n  App: AppType,\n  Component: NextComponentType\n): {\n  App: AppType\n  Component: NextComponentType\n} {\n  // For backwards compatibility\n  if (typeof options === 'function') {\n    return {\n      App,\n      Component: options(Component),\n    }\n  }\n\n  return {\n    App: options.enhanceApp ? options.enhanceApp(App) : App,\n    Component: options.enhanceComponent\n      ? options.enhanceComponent(Component)\n      : Component,\n  }\n}\n\nfunction renderPageTree(\n  App: AppType,\n  Component: NextComponentType,\n  props: any\n) {\n  return <App Component={Component} {...props} />\n}\n\nexport type RenderOptsPartial = {\n  canonicalBase: string\n  runtimeConfig?: { [key: string]: any }\n  assetPrefix?: string\n  err?: Error | null\n  nextExport?: boolean\n  dev?: boolean\n  ampPath?: string\n  ErrorDebug?: PagesDevOverlayType\n  ampValidator?: (html: string, pathname: string) => Promise<void>\n  ampSkipValidation?: boolean\n  ampOptimizerConfig?: { [key: string]: any }\n  isNextDataRequest?: boolean\n  params?: ParsedUrlQuery\n  previewProps: __ApiPreviewProps | undefined\n  basePath: string\n  unstable_runtimeJS?: false\n  unstable_JsPreload?: false\n  optimizeCss: any\n  nextConfigOutput?: 'standalone' | 'export'\n  nextScriptWorkers: any\n  assetQueryString?: string\n  resolvedUrl?: string\n  resolvedAsPath?: string\n  setIsrStatus?: (key: string, value: boolean | null) => void\n  clientReferenceManifest?: DeepReadonly<ClientReferenceManifest>\n  nextFontManifest?: DeepReadonly<NextFontManifest>\n  distDir?: string\n  locale?: string\n  locales?: readonly string[]\n  defaultLocale?: string\n  domainLocales?: readonly DomainLocale[]\n  disableOptimizedLoading?: boolean\n  supportsDynamicResponse: boolean\n  botType?: 'dom' | 'html' | undefined\n  serveStreamingMetadata?: boolean\n  runtime?: ServerRuntime\n  serverComponents?: boolean\n  serverActions?: {\n    bodySizeLimit?: SizeLimit\n    allowedOrigins?: string[]\n  }\n  crossOrigin?: 'anonymous' | 'use-credentials' | '' | undefined\n  images: ImageConfigComplete\n  largePageDataBytes?: number\n  isOnDemandRevalidate?: boolean\n  strictNextHead: boolean\n  isPossibleServerAction?: boolean\n  isExperimentalCompile?: boolean\n  isPrefetch?: boolean\n  expireTime?: number\n  experimental: {\n    clientTraceMetadata?: string[]\n  }\n}\n\nexport type RenderOpts = LoadComponentsReturnType<PagesModule> &\n  RenderOptsPartial\n\n/**\n * Shared context used for all page renders.\n */\nexport type PagesSharedContext = {\n  /**\n   * Used to facilitate caching of page bundles, we send it to the client so\n   * that pageloader knows where to load bundles.\n   */\n  buildId: string\n\n  /**\n   * The deployment ID if the user is deploying to a platform that provides one.\n   */\n  deploymentId: string | undefined\n\n  /**\n   * True if the user is using a custom server.\n   */\n  customServer: true | undefined\n}\n\n/**\n * The context for the given request.\n */\nexport type PagesRenderContext = {\n  /**\n   * Whether this should be rendered as a fallback page.\n   */\n  isFallback: boolean\n\n  /**\n   * Whether this is in draft mode.\n   */\n  isDraftMode: boolean | undefined\n\n  /**\n   * In development, the original source page that returned a 404.\n   */\n  developmentNotFoundSourcePage: string | undefined\n}\n\n/**\n * RenderOptsExtra is being used to split away functionality that's within the\n * renderOpts. Eventually we can have more explicit render options for each\n * route kind.\n */\nexport type RenderOptsExtra = {\n  App: AppType\n  Document: DocumentType\n}\n\nconst invalidKeysMsg = (\n  methodName: 'getServerSideProps' | 'getStaticProps',\n  invalidKeys: string[]\n) => {\n  const docsPathname = `invalid-${methodName.toLocaleLowerCase()}-value`\n\n  return (\n    `Additional keys were returned from \\`${methodName}\\`. Properties intended for your component must be nested under the \\`props\\` key, e.g.:` +\n    `\\n\\n\\treturn { props: { title: 'My Title', content: '...' } }` +\n    `\\n\\nKeys that need to be moved: ${invalidKeys.join(', ')}.` +\n    `\\nRead more: https://nextjs.org/docs/messages/${docsPathname}`\n  )\n}\n\nfunction checkRedirectValues(\n  redirect: Redirect,\n  req: IncomingMessage,\n  method: 'getStaticProps' | 'getServerSideProps'\n) {\n  const { destination, permanent, statusCode, basePath } = redirect\n  let errors: string[] = []\n\n  const hasStatusCode = typeof statusCode !== 'undefined'\n  const hasPermanent = typeof permanent !== 'undefined'\n\n  if (hasPermanent && hasStatusCode) {\n    errors.push(`\\`permanent\\` and \\`statusCode\\` can not both be provided`)\n  } else if (hasPermanent && typeof permanent !== 'boolean') {\n    errors.push(`\\`permanent\\` must be \\`true\\` or \\`false\\``)\n  } else if (hasStatusCode && !allowedStatusCodes.has(statusCode!)) {\n    errors.push(\n      `\\`statusCode\\` must undefined or one of ${[...allowedStatusCodes].join(\n        ', '\n      )}`\n    )\n  }\n  const destinationType = typeof destination\n\n  if (destinationType !== 'string') {\n    errors.push(\n      `\\`destination\\` should be string but received ${destinationType}`\n    )\n  }\n\n  const basePathType = typeof basePath\n\n  if (basePathType !== 'undefined' && basePathType !== 'boolean') {\n    errors.push(\n      `\\`basePath\\` should be undefined or a false, received ${basePathType}`\n    )\n  }\n\n  if (errors.length > 0) {\n    throw new Error(\n      `Invalid redirect object returned from ${method} for ${req.url}\\n` +\n        errors.join(' and ') +\n        '\\n' +\n        `See more info here: https://nextjs.org/docs/messages/invalid-redirect-gssp`\n    )\n  }\n}\n\nexport function errorToJSON(err: Error) {\n  let source: typeof COMPILER_NAMES.server | typeof COMPILER_NAMES.edgeServer =\n    'server'\n\n  if (process.env.NEXT_RUNTIME !== 'edge') {\n    source = getErrorSource(err) || 'server'\n  }\n\n  return {\n    name: err.name,\n    source,\n    message: stripAnsi(err.message),\n    stack: err.stack,\n    digest: (err as any).digest,\n  }\n}\n\nfunction serializeError(\n  dev: boolean | undefined,\n  err: Error\n): Error & {\n  statusCode?: number\n  source?: typeof COMPILER_NAMES.server | typeof COMPILER_NAMES.edgeServer\n} {\n  if (dev) {\n    return errorToJSON(err)\n  }\n\n  return {\n    name: 'Internal Server Error.',\n    message: '500 - Internal Server Error.',\n    statusCode: 500,\n  }\n}\n\nexport async function renderToHTMLImpl(\n  req: IncomingMessage,\n  res: ServerResponse,\n  pathname: string,\n  query: NextParsedUrlQuery,\n  renderOpts: Omit<RenderOpts, keyof RenderOptsExtra>,\n  extra: RenderOptsExtra,\n  sharedContext: PagesSharedContext,\n  renderContext: PagesRenderContext\n): Promise<RenderResult> {\n  // Adds support for reading `cookies` in `getServerSideProps` when SSR.\n  setLazyProp({ req: req as any }, 'cookies', getCookieParser(req.headers))\n\n  const metadata: PagesRenderResultMetadata = {}\n\n  metadata.assetQueryString =\n    (renderOpts.dev && renderOpts.assetQueryString) || ''\n\n  if (renderOpts.dev && !metadata.assetQueryString) {\n    const userAgent = (req.headers['user-agent'] || '').toLowerCase()\n    if (userAgent.includes('safari') && !userAgent.includes('chrome')) {\n      // In dev we invalidate the cache by appending a timestamp to the resource URL.\n      // This is a workaround to fix https://github.com/vercel/next.js/issues/5860\n      // TODO: remove this workaround when https://bugs.webkit.org/show_bug.cgi?id=187726 is fixed.\n      // Note: The workaround breaks breakpoints on reload since the script url always changes,\n      // so we only apply it to Safari.\n      metadata.assetQueryString = `?ts=${Date.now()}`\n    }\n  }\n\n  // if deploymentId is provided we append it to all asset requests\n  if (sharedContext.deploymentId) {\n    metadata.assetQueryString += `${metadata.assetQueryString ? '&' : '?'}dpl=${\n      sharedContext.deploymentId\n    }`\n  }\n\n  // don't modify original query object\n  query = Object.assign({}, query)\n\n  const {\n    err,\n    dev = false,\n    ampPath = '',\n    pageConfig = {},\n    buildManifest,\n    reactLoadableManifest,\n    ErrorDebug,\n    getStaticProps,\n    getStaticPaths,\n    getServerSideProps,\n    isNextDataRequest,\n    params,\n    previewProps,\n    basePath,\n    images,\n    runtime: globalRuntime,\n    isExperimentalCompile,\n    expireTime,\n  } = renderOpts\n  const { App } = extra\n\n  const assetQueryString = metadata.assetQueryString\n\n  let Document = extra.Document\n\n  let Component: React.ComponentType<{}> | ((props: any) => JSX.Element) =\n    renderOpts.Component\n  const OriginComponent = Component\n\n  const isFallback = renderContext.isFallback ?? false\n  const notFoundSrcPage = renderContext.developmentNotFoundSourcePage\n\n  // next internal queries should be stripped out\n  stripInternalQueries(query)\n\n  const isSSG = !!getStaticProps\n  const isBuildTimeSSG = isSSG && renderOpts.nextExport\n  const defaultAppGetInitialProps =\n    App.getInitialProps === (App as any).origGetInitialProps\n\n  const hasPageGetInitialProps = !!(Component as any)?.getInitialProps\n  const hasPageScripts = (Component as any)?.unstable_scriptLoader\n\n  const pageIsDynamic = isDynamicRoute(pathname)\n\n  const defaultErrorGetInitialProps =\n    pathname === '/_error' &&\n    (Component as any).getInitialProps ===\n      (Component as any).origGetInitialProps\n\n  if (\n    renderOpts.nextExport &&\n    hasPageGetInitialProps &&\n    !defaultErrorGetInitialProps\n  ) {\n    warn(\n      `Detected getInitialProps on page '${pathname}'` +\n        ` while running export. It's recommended to use getStaticProps` +\n        ` which has a more correct behavior for static exporting.` +\n        `\\nRead more: https://nextjs.org/docs/messages/get-initial-props-export`\n    )\n  }\n\n  let isAutoExport =\n    !hasPageGetInitialProps &&\n    defaultAppGetInitialProps &&\n    !isSSG &&\n    !getServerSideProps\n\n  // if we are running from experimental compile and the page\n  // would normally be automatically statically optimized\n  // ensure we set cache header so it's not rendered on-demand\n  // every request\n  if (isAutoExport && !dev && isExperimentalCompile) {\n    res.setHeader(\n      'Cache-Control',\n      getCacheControlHeader({ revalidate: false, expire: expireTime })\n    )\n    isAutoExport = false\n  }\n\n  if (hasPageGetInitialProps && isSSG) {\n    throw new Error(SSG_GET_INITIAL_PROPS_CONFLICT + ` ${pathname}`)\n  }\n\n  if (hasPageGetInitialProps && getServerSideProps) {\n    throw new Error(SERVER_PROPS_GET_INIT_PROPS_CONFLICT + ` ${pathname}`)\n  }\n\n  if (getServerSideProps && isSSG) {\n    throw new Error(SERVER_PROPS_SSG_CONFLICT + ` ${pathname}`)\n  }\n\n  if (getServerSideProps && renderOpts.nextConfigOutput === 'export') {\n    throw new Error(\n      'getServerSideProps cannot be used with \"output: export\". See more info here: https://nextjs.org/docs/advanced-features/static-html-export'\n    )\n  }\n\n  if (getStaticPaths && !pageIsDynamic) {\n    throw new Error(\n      `getStaticPaths is only allowed for dynamic SSG pages and was found on '${pathname}'.` +\n        `\\nRead more: https://nextjs.org/docs/messages/non-dynamic-getstaticpaths-usage`\n    )\n  }\n\n  if (!!getStaticPaths && !isSSG) {\n    throw new Error(\n      `getStaticPaths was added without a getStaticProps in ${pathname}. Without getStaticProps, getStaticPaths does nothing`\n    )\n  }\n\n  if (isSSG && pageIsDynamic && !getStaticPaths) {\n    throw new Error(\n      `getStaticPaths is required for dynamic SSG pages and is missing for '${pathname}'.` +\n        `\\nRead more: https://nextjs.org/docs/messages/invalid-getstaticpaths-value`\n    )\n  }\n\n  let asPath: string = renderOpts.resolvedAsPath || (req.url as string)\n\n  if (dev) {\n    const { isValidElementType } = require('next/dist/compiled/react-is')\n    if (!isValidElementType(Component)) {\n      throw new Error(\n        `The default export is not a React Component in page: \"${pathname}\"`\n      )\n    }\n\n    if (!isValidElementType(App)) {\n      throw new Error(\n        `The default export is not a React Component in page: \"/_app\"`\n      )\n    }\n\n    if (!isValidElementType(Document)) {\n      throw new Error(\n        `The default export is not a React Component in page: \"/_document\"`\n      )\n    }\n\n    if (isAutoExport || isFallback) {\n      // remove query values except ones that will be set during export\n      query = {\n        ...(query.amp\n          ? {\n              amp: query.amp,\n            }\n          : {}),\n      }\n      asPath = `${pathname}${\n        // ensure trailing slash is present for non-dynamic auto-export pages\n        req.url!.endsWith('/') && pathname !== '/' && !pageIsDynamic ? '/' : ''\n      }`\n      req.url = pathname\n    }\n\n    if (pathname === '/404' && (hasPageGetInitialProps || getServerSideProps)) {\n      throw new Error(\n        `\\`pages/404\\` ${STATIC_STATUS_PAGE_GET_INITIAL_PROPS_ERROR}`\n      )\n    }\n    if (\n      STATIC_STATUS_PAGES.includes(pathname) &&\n      (hasPageGetInitialProps || getServerSideProps)\n    ) {\n      throw new Error(\n        `\\`pages${pathname}\\` ${STATIC_STATUS_PAGE_GET_INITIAL_PROPS_ERROR}`\n      )\n    }\n\n    if (renderOpts?.setIsrStatus) {\n      renderOpts.setIsrStatus(asPath, isSSG || isAutoExport ? true : null)\n    }\n  }\n\n  for (const methodName of [\n    'getStaticProps',\n    'getServerSideProps',\n    'getStaticPaths',\n  ]) {\n    if ((Component as any)?.[methodName]) {\n      throw new Error(\n        `page ${pathname} ${methodName} ${GSSP_COMPONENT_MEMBER_ERROR}`\n      )\n    }\n  }\n\n  await Loadable.preloadAll() // Make sure all dynamic imports are loaded\n\n  let isPreview: boolean | undefined = undefined\n  let previewData: PreviewData\n\n  if (\n    (isSSG || getServerSideProps) &&\n    !isFallback &&\n    process.env.NEXT_RUNTIME !== 'edge' &&\n    previewProps\n  ) {\n    // Reads of this are cached on the `req` object, so this should resolve\n    // instantly. There's no need to pass this data down from a previous\n    // invoke.\n    previewData = tryGetPreviewData(\n      req,\n      res,\n      previewProps,\n      !!renderOpts.multiZoneDraftMode\n    )\n    isPreview = previewData !== false\n  }\n\n  // url will always be set\n  const routerIsReady = !!(\n    getServerSideProps ||\n    hasPageGetInitialProps ||\n    (!defaultAppGetInitialProps && !isSSG) ||\n    isExperimentalCompile\n  )\n  const router = new ServerRouter(\n    pathname,\n    query,\n    asPath,\n    {\n      isFallback: isFallback,\n    },\n    routerIsReady,\n    basePath,\n    renderOpts.locale,\n    renderOpts.locales,\n    renderOpts.defaultLocale,\n    renderOpts.domainLocales,\n    isPreview,\n    getRequestMeta(req, 'isLocaleDomain')\n  )\n\n  const appRouter = adaptForAppRouterInstance(router)\n\n  let scriptLoader: any = {}\n  const jsxStyleRegistry = createStyleRegistry()\n  const ampState = {\n    ampFirst: pageConfig.amp === true,\n    hasQuery: Boolean(query.amp),\n    hybrid: pageConfig.amp === 'hybrid',\n  }\n\n  // Disable AMP under the web environment\n  const inAmpMode = process.env.NEXT_RUNTIME !== 'edge' && isInAmpMode(ampState)\n  let head: JSX.Element[] = defaultHead(inAmpMode)\n  const reactLoadableModules: string[] = []\n\n  let initialScripts: any = {}\n  if (hasPageScripts) {\n    initialScripts.beforeInteractive = []\n      .concat(hasPageScripts())\n      .filter((script: any) => script.props.strategy === 'beforeInteractive')\n      .map((script: any) => script.props)\n  }\n\n  const AppContainer = ({ children }: { children: JSX.Element }) => (\n    <AppRouterContext.Provider value={appRouter}>\n      <SearchParamsContext.Provider value={adaptForSearchParams(router)}>\n        <PathnameContextProviderAdapter\n          router={router}\n          isAutoExport={isAutoExport}\n        >\n          <PathParamsContext.Provider value={adaptForPathParams(router)}>\n            <RouterContext.Provider value={router}>\n              <AmpStateContext.Provider value={ampState}>\n                <HeadManagerContext.Provider\n                  value={{\n                    updateHead: (state) => {\n                      head = state\n                    },\n                    updateScripts: (scripts) => {\n                      scriptLoader = scripts\n                    },\n                    scripts: initialScripts,\n                    mountedInstances: new Set(),\n                  }}\n                >\n                  <LoadableContext.Provider\n                    value={(moduleName) =>\n                      reactLoadableModules.push(moduleName)\n                    }\n                  >\n                    <StyleRegistry registry={jsxStyleRegistry}>\n                      <ImageConfigContext.Provider value={images}>\n                        {children}\n                      </ImageConfigContext.Provider>\n                    </StyleRegistry>\n                  </LoadableContext.Provider>\n                </HeadManagerContext.Provider>\n              </AmpStateContext.Provider>\n            </RouterContext.Provider>\n          </PathParamsContext.Provider>\n        </PathnameContextProviderAdapter>\n      </SearchParamsContext.Provider>\n    </AppRouterContext.Provider>\n  )\n\n  // The `useId` API uses the path indexes to generate an ID for each node.\n  // To guarantee the match of hydration, we need to ensure that the structure\n  // of wrapper nodes is isomorphic in server and client.\n  // TODO: With `enhanceApp` and `enhanceComponents` options, this approach may\n  // not be useful.\n  // https://github.com/facebook/react/pull/22644\n  const Noop = () => null\n  const AppContainerWithIsomorphicFiberStructure: React.FC<{\n    children: JSX.Element\n  }> = ({ children }) => {\n    return (\n      <>\n        {/* <Head/> */}\n        <Noop />\n        <AppContainer>\n          <>\n            {/* <ReactDevOverlay/> */}\n            {dev ? (\n              <>\n                {children}\n                <Noop />\n              </>\n            ) : (\n              children\n            )}\n            {/* <RouteAnnouncer/> */}\n            <Noop />\n          </>\n        </AppContainer>\n      </>\n    )\n  }\n\n  const ctx = {\n    err,\n    req: isAutoExport ? undefined : req,\n    res: isAutoExport ? undefined : res,\n    pathname,\n    query,\n    asPath,\n    locale: renderOpts.locale,\n    locales: renderOpts.locales,\n    defaultLocale: renderOpts.defaultLocale,\n    AppTree: (props: any) => {\n      return (\n        <AppContainerWithIsomorphicFiberStructure>\n          {renderPageTree(App, OriginComponent, { ...props, router })}\n        </AppContainerWithIsomorphicFiberStructure>\n      )\n    },\n    defaultGetInitialProps: async (\n      docCtx: DocumentContext,\n      options: { nonce?: string } = {}\n    ): Promise<DocumentInitialProps> => {\n      const enhanceApp = (AppComp: any) => {\n        return (props: any) => <AppComp {...props} />\n      }\n\n      const { html, head: renderPageHead } = await docCtx.renderPage({\n        enhanceApp,\n      })\n      const styles = jsxStyleRegistry.styles({ nonce: options.nonce })\n      jsxStyleRegistry.flush()\n      return { html, head: renderPageHead, styles }\n    },\n  }\n  let props: any\n\n  const nextExport =\n    !isSSG && (renderOpts.nextExport || (dev && (isAutoExport || isFallback)))\n\n  const styledJsxInsertedHTML = () => {\n    const styles = jsxStyleRegistry.styles()\n    jsxStyleRegistry.flush()\n    return <>{styles}</>\n  }\n\n  props = await loadGetInitialProps(App, {\n    AppTree: ctx.AppTree,\n    Component,\n    router,\n    ctx,\n  })\n\n  if ((isSSG || getServerSideProps) && isPreview) {\n    props.__N_PREVIEW = true\n  }\n\n  if (isSSG) {\n    props[STATIC_PROPS_ID] = true\n  }\n\n  if (isSSG && !isFallback) {\n    let data: Readonly<UnwrapPromise<ReturnType<GetStaticProps>>>\n\n    try {\n      data = await getTracer().trace(\n        RenderSpan.getStaticProps,\n        {\n          spanName: `getStaticProps ${pathname}`,\n          attributes: {\n            'next.route': pathname,\n          },\n        },\n        () =>\n          getStaticProps({\n            ...(pageIsDynamic ? { params } : undefined),\n            ...(isPreview\n              ? { draftMode: true, preview: true, previewData: previewData }\n              : undefined),\n            locales: [...(renderOpts.locales ?? [])],\n            locale: renderOpts.locale,\n            defaultLocale: renderOpts.defaultLocale,\n            revalidateReason: renderOpts.isOnDemandRevalidate\n              ? 'on-demand'\n              : isBuildTimeSSG\n                ? 'build'\n                : 'stale',\n          })\n      )\n    } catch (staticPropsError: any) {\n      // remove not found error code to prevent triggering legacy\n      // 404 rendering\n      if (staticPropsError && staticPropsError.code === 'ENOENT') {\n        delete staticPropsError.code\n      }\n      throw staticPropsError\n    }\n\n    if (data == null) {\n      throw new Error(GSP_NO_RETURNED_VALUE)\n    }\n\n    const invalidKeys = Object.keys(data).filter(\n      (key) =>\n        key !== 'revalidate' &&\n        key !== 'props' &&\n        key !== 'redirect' &&\n        key !== 'notFound'\n    )\n\n    if (invalidKeys.includes('unstable_revalidate')) {\n      throw new Error(UNSTABLE_REVALIDATE_RENAME_ERROR)\n    }\n\n    if (invalidKeys.length) {\n      throw new Error(invalidKeysMsg('getStaticProps', invalidKeys))\n    }\n\n    if (process.env.NODE_ENV !== 'production') {\n      if (\n        typeof (data as any).notFound !== 'undefined' &&\n        typeof (data as any).redirect !== 'undefined'\n      ) {\n        throw new Error(\n          `\\`redirect\\` and \\`notFound\\` can not both be returned from ${\n            isSSG ? 'getStaticProps' : 'getServerSideProps'\n          } at the same time. Page: ${pathname}\\nSee more info here: https://nextjs.org/docs/messages/gssp-mixed-not-found-redirect`\n        )\n      }\n    }\n\n    if ('notFound' in data && data.notFound) {\n      if (pathname === '/404') {\n        throw new Error(\n          `The /404 page can not return notFound in \"getStaticProps\", please remove it to continue!`\n        )\n      }\n\n      metadata.isNotFound = true\n    }\n\n    if (\n      'redirect' in data &&\n      data.redirect &&\n      typeof data.redirect === 'object'\n    ) {\n      checkRedirectValues(data.redirect as Redirect, req, 'getStaticProps')\n\n      if (isBuildTimeSSG) {\n        throw new Error(\n          `\\`redirect\\` can not be returned from getStaticProps during prerendering (${req.url})\\n` +\n            `See more info here: https://nextjs.org/docs/messages/gsp-redirect-during-prerender`\n        )\n      }\n\n      ;(data as any).props = {\n        __N_REDIRECT: data.redirect.destination,\n        __N_REDIRECT_STATUS: getRedirectStatus(data.redirect),\n      }\n      if (typeof data.redirect.basePath !== 'undefined') {\n        ;(data as any).props.__N_REDIRECT_BASE_PATH = data.redirect.basePath\n      }\n      metadata.isRedirect = true\n    }\n\n    if (\n      (dev || isBuildTimeSSG) &&\n      !metadata.isNotFound &&\n      !isSerializableProps(pathname, 'getStaticProps', (data as any).props)\n    ) {\n      // this fn should throw an error instead of ever returning `false`\n      throw new Error(\n        'invariant: getStaticProps did not return valid props. Please report this.'\n      )\n    }\n\n    let revalidate: Revalidate\n    if ('revalidate' in data) {\n      if (data.revalidate && renderOpts.nextConfigOutput === 'export') {\n        throw new Error(\n          'ISR cannot be used with \"output: export\". See more info here: https://nextjs.org/docs/advanced-features/static-html-export'\n        )\n      }\n      if (typeof data.revalidate === 'number') {\n        if (!Number.isInteger(data.revalidate)) {\n          throw new Error(\n            `A page's revalidate option must be seconds expressed as a natural number for ${req.url}. Mixed numbers, such as '${data.revalidate}', cannot be used.` +\n              `\\nTry changing the value to '${Math.ceil(\n                data.revalidate\n              )}' or using \\`Math.ceil()\\` if you're computing the value.`\n          )\n        } else if (data.revalidate <= 0) {\n          throw new Error(\n            `A page's revalidate option can not be less than or equal to zero for ${req.url}. A revalidate option of zero means to revalidate after _every_ request, and implies stale data cannot be tolerated.` +\n              `\\n\\nTo never revalidate, you can set revalidate to \\`false\\` (only ran once at build-time).` +\n              `\\nTo revalidate as soon as possible, you can set the value to \\`1\\`.`\n          )\n        } else {\n          if (data.revalidate > 31536000) {\n            // if it's greater than a year for some reason error\n            console.warn(\n              `Warning: A page's revalidate option was set to more than a year for ${req.url}. This may have been done in error.` +\n                `\\nTo only run getStaticProps at build-time and not revalidate at runtime, you can set \\`revalidate\\` to \\`false\\`!`\n            )\n          }\n\n          revalidate = data.revalidate\n        }\n      } else if (data.revalidate === true) {\n        // When enabled, revalidate after 1 second. This value is optimal for\n        // the most up-to-date page possible, but without a 1-to-1\n        // request-refresh ratio.\n        revalidate = 1\n      } else if (\n        data.revalidate === false ||\n        typeof data.revalidate === 'undefined'\n      ) {\n        // By default, we never revalidate.\n        revalidate = false\n      } else {\n        throw new Error(\n          `A page's revalidate option must be seconds expressed as a natural number. Mixed numbers and strings cannot be used. Received '${JSON.stringify(\n            data.revalidate\n          )}' for ${req.url}`\n        )\n      }\n    } else {\n      // By default, we never revalidate.\n      revalidate = false\n    }\n\n    props.pageProps = Object.assign(\n      {},\n      props.pageProps,\n      'props' in data ? data.props : undefined\n    )\n\n    // pass up cache control and props for export\n    metadata.cacheControl = { revalidate, expire: undefined }\n    metadata.pageData = props\n\n    // this must come after revalidate is added to renderResultMeta\n    if (metadata.isNotFound) {\n      return new RenderResult(null, { metadata })\n    }\n  }\n\n  if (getServerSideProps) {\n    props[SERVER_PROPS_ID] = true\n  }\n\n  if (getServerSideProps && !isFallback) {\n    let data: UnwrapPromise<ReturnType<GetServerSideProps>>\n\n    let canAccessRes = true\n    let resOrProxy = res\n    let deferredContent = false\n    if (process.env.NODE_ENV !== 'production') {\n      resOrProxy = new Proxy<ServerResponse>(res, {\n        get: function (obj, prop) {\n          if (!canAccessRes) {\n            const message =\n              `You should not access 'res' after getServerSideProps resolves.` +\n              `\\nRead more: https://nextjs.org/docs/messages/gssp-no-mutating-res`\n\n            if (deferredContent) {\n              throw new Error(message)\n            } else {\n              warn(message)\n            }\n          }\n\n          if (typeof prop === 'symbol') {\n            return ReflectAdapter.get(obj, prop, res)\n          }\n\n          return ReflectAdapter.get(obj, prop, res)\n        },\n      })\n    }\n\n    try {\n      data = await getTracer().trace(\n        RenderSpan.getServerSideProps,\n        {\n          spanName: `getServerSideProps ${pathname}`,\n          attributes: {\n            'next.route': pathname,\n          },\n        },\n        async () =>\n          getServerSideProps({\n            req: req as IncomingMessage & {\n              cookies: NextApiRequestCookies\n            },\n            res: resOrProxy,\n            query,\n            resolvedUrl: renderOpts.resolvedUrl as string,\n            ...(pageIsDynamic ? { params } : undefined),\n            ...(previewData !== false\n              ? { draftMode: true, preview: true, previewData: previewData }\n              : undefined),\n            // We create a copy here to avoid having the types of\n            // `getServerSideProps` change. This ensures that users can't\n            // mutate this array and have it poison the reference.\n            locales: [...(renderOpts.locales ?? [])],\n            locale: renderOpts.locale,\n            defaultLocale: renderOpts.defaultLocale,\n          })\n      )\n      canAccessRes = false\n      metadata.cacheControl = { revalidate: 0, expire: undefined }\n    } catch (serverSidePropsError: any) {\n      // remove not found error code to prevent triggering legacy\n      // 404 rendering\n      if (\n        isError(serverSidePropsError) &&\n        serverSidePropsError.code === 'ENOENT'\n      ) {\n        delete serverSidePropsError.code\n      }\n      throw serverSidePropsError\n    }\n\n    if (data == null) {\n      throw new Error(GSSP_NO_RETURNED_VALUE)\n    }\n\n    if ((data as any).props instanceof Promise) {\n      deferredContent = true\n    }\n\n    const invalidKeys = Object.keys(data).filter(\n      (key) => key !== 'props' && key !== 'redirect' && key !== 'notFound'\n    )\n\n    if ((data as any).unstable_notFound) {\n      throw new Error(\n        `unstable_notFound has been renamed to notFound, please update the field to continue. Page: ${pathname}`\n      )\n    }\n    if ((data as any).unstable_redirect) {\n      throw new Error(\n        `unstable_redirect has been renamed to redirect, please update the field to continue. Page: ${pathname}`\n      )\n    }\n\n    if (invalidKeys.length) {\n      throw new Error(invalidKeysMsg('getServerSideProps', invalidKeys))\n    }\n\n    if ('notFound' in data && data.notFound) {\n      if (pathname === '/404') {\n        throw new Error(\n          `The /404 page can not return notFound in \"getStaticProps\", please remove it to continue!`\n        )\n      }\n\n      metadata.isNotFound = true\n      return new RenderResult(null, { metadata })\n    }\n\n    if ('redirect' in data && typeof data.redirect === 'object') {\n      checkRedirectValues(data.redirect as Redirect, req, 'getServerSideProps')\n      ;(data as any).props = {\n        __N_REDIRECT: data.redirect.destination,\n        __N_REDIRECT_STATUS: getRedirectStatus(data.redirect),\n      }\n      if (typeof data.redirect.basePath !== 'undefined') {\n        ;(data as any).props.__N_REDIRECT_BASE_PATH = data.redirect.basePath\n      }\n      metadata.isRedirect = true\n    }\n\n    if (deferredContent) {\n      ;(data as any).props = await (data as any).props\n    }\n\n    if (\n      (dev || isBuildTimeSSG) &&\n      !isSerializableProps(pathname, 'getServerSideProps', (data as any).props)\n    ) {\n      // this fn should throw an error instead of ever returning `false`\n      throw new Error(\n        'invariant: getServerSideProps did not return valid props. Please report this.'\n      )\n    }\n\n    props.pageProps = Object.assign({}, props.pageProps, (data as any).props)\n    metadata.pageData = props\n  }\n\n  if (\n    !isSSG && // we only show this warning for legacy pages\n    !getServerSideProps &&\n    process.env.NODE_ENV !== 'production' &&\n    Object.keys(props?.pageProps || {}).includes('url')\n  ) {\n    console.warn(\n      `The prop \\`url\\` is a reserved prop in Next.js for legacy reasons and will be overridden on page ${pathname}\\n` +\n        `See more info here: https://nextjs.org/docs/messages/reserved-page-prop`\n    )\n  }\n\n  // Avoid rendering page un-necessarily for getServerSideProps data request\n  // and getServerSideProps/getStaticProps redirects\n  if ((isNextDataRequest && !isSSG) || metadata.isRedirect) {\n    return new RenderResult(JSON.stringify(props), {\n      metadata,\n    })\n  }\n\n  // We don't call getStaticProps or getServerSideProps while generating\n  // the fallback so make sure to set pageProps to an empty object\n  if (isFallback) {\n    props.pageProps = {}\n  }\n\n  // the response might be finished on the getInitialProps call\n  if (isResSent(res) && !isSSG) return new RenderResult(null, { metadata })\n\n  // we preload the buildManifest for auto-export dynamic pages\n  // to speed up hydrating query values\n  let filteredBuildManifest = buildManifest\n  if (isAutoExport && pageIsDynamic) {\n    const page = denormalizePagePath(normalizePagePath(pathname))\n    // This code would be much cleaner using `immer` and directly pushing into\n    // the result from `getPageFiles`, we could maybe consider that in the\n    // future.\n    if (page in filteredBuildManifest.pages) {\n      filteredBuildManifest = {\n        ...filteredBuildManifest,\n        pages: {\n          ...filteredBuildManifest.pages,\n          [page]: [\n            ...filteredBuildManifest.pages[page],\n            ...filteredBuildManifest.lowPriorityFiles.filter((f) =>\n              f.includes('_buildManifest')\n            ),\n          ],\n        },\n        lowPriorityFiles: filteredBuildManifest.lowPriorityFiles.filter(\n          (f) => !f.includes('_buildManifest')\n        ),\n      }\n    }\n  }\n\n  const Body = ({ children }: { children: JSX.Element }) => {\n    return inAmpMode ? children : <div id=\"__next\">{children}</div>\n  }\n\n  const renderDocument = async () => {\n    // For `Document`, there are two cases that we don't support:\n    // 1. Using `Document.getInitialProps` in the Edge runtime.\n    // 2. Using the class component `Document` with concurrent features.\n\n    const BuiltinFunctionalDocument: DocumentType | undefined = (\n      Document as any\n    )[NEXT_BUILTIN_DOCUMENT]\n\n    if (process.env.NEXT_RUNTIME === 'edge' && Document.getInitialProps) {\n      // In the Edge runtime, `Document.getInitialProps` isn't supported.\n      // We throw an error here if it's customized.\n      if (BuiltinFunctionalDocument) {\n        Document = BuiltinFunctionalDocument\n      } else {\n        throw new Error(\n          '`getInitialProps` in Document component is not supported with the Edge Runtime.'\n        )\n      }\n    }\n\n    async function loadDocumentInitialProps(\n      renderShell: (\n        _App: AppType,\n        _Component: NextComponentType\n      ) => Promise<ReactReadableStream>\n    ) {\n      const renderPage: RenderPage = async (\n        options: ComponentsEnhancer = {}\n      ): Promise<RenderPageResult> => {\n        if (ctx.err && ErrorDebug) {\n          // Always start rendering the shell even if there's an error.\n          if (renderShell) {\n            renderShell(App, Component)\n          }\n\n          const html = await renderToString(\n            <Body>\n              <ErrorDebug />\n            </Body>\n          )\n          return { html, head }\n        }\n\n        if (dev && (props.router || props.Component)) {\n          throw new Error(\n            `'router' and 'Component' can not be returned in getInitialProps from _app.js https://nextjs.org/docs/messages/cant-override-next-props`\n          )\n        }\n\n        const { App: EnhancedApp, Component: EnhancedComponent } =\n          enhanceComponents(options, App, Component)\n\n        const stream = await renderShell(EnhancedApp, EnhancedComponent)\n        await stream.allReady\n        const html = await streamToString(stream)\n\n        return { html, head }\n      }\n      const documentCtx = { ...ctx, renderPage }\n      const docProps: DocumentInitialProps = await loadGetInitialProps(\n        Document,\n        documentCtx\n      )\n      // the response might be finished on the getInitialProps call\n      if (isResSent(res) && !isSSG) return null\n\n      if (!docProps || typeof docProps.html !== 'string') {\n        const message = `\"${getDisplayName(\n          Document\n        )}.getInitialProps()\" should resolve to an object with a \"html\" prop set with a valid html string`\n        throw new Error(message)\n      }\n\n      return { docProps, documentCtx }\n    }\n\n    const renderContent = (_App: AppType, _Component: NextComponentType) => {\n      const EnhancedApp = _App || App\n      const EnhancedComponent = _Component || Component\n\n      return ctx.err && ErrorDebug ? (\n        <Body>\n          <ErrorDebug />\n        </Body>\n      ) : (\n        <Body>\n          <AppContainerWithIsomorphicFiberStructure>\n            {renderPageTree(EnhancedApp, EnhancedComponent, {\n              ...props,\n              router,\n            })}\n          </AppContainerWithIsomorphicFiberStructure>\n        </Body>\n      )\n    }\n\n    // Always using react concurrent rendering mode with required react version 18.x\n    const renderShell = async (\n      EnhancedApp: AppType,\n      EnhancedComponent: NextComponentType\n    ) => {\n      const content = renderContent(EnhancedApp, EnhancedComponent)\n      return await renderToInitialFizzStream({\n        ReactDOMServer: ReactDOMServerPages,\n        element: content,\n      })\n    }\n\n    const hasDocumentGetInitialProps =\n      process.env.NEXT_RUNTIME !== 'edge' && !!Document.getInitialProps\n\n    // If it has getInitialProps, we will render the shell in `renderPage`.\n    // Otherwise we do it right now.\n    let documentInitialPropsRes:\n      | {}\n      | Awaited<ReturnType<typeof loadDocumentInitialProps>>\n\n    const [rawStyledJsxInsertedHTML, content] = await Promise.all([\n      renderToString(styledJsxInsertedHTML()),\n      (async () => {\n        if (hasDocumentGetInitialProps) {\n          documentInitialPropsRes = await loadDocumentInitialProps(renderShell)\n          if (documentInitialPropsRes === null) return null\n          const { docProps } = documentInitialPropsRes as any\n          return docProps.html\n        } else {\n          documentInitialPropsRes = {}\n          const stream = await renderShell(App, Component)\n          await stream.allReady\n          return streamToString(stream)\n        }\n      })(),\n    ])\n\n    if (content === null) {\n      return null\n    }\n\n    const contentHTML = rawStyledJsxInsertedHTML + content\n\n    // @ts-ignore: documentInitialPropsRes is set\n    const { docProps } = (documentInitialPropsRes as any) || {}\n    const documentElement = (htmlProps: any) => {\n      if (process.env.NEXT_RUNTIME === 'edge') {\n        return (Document as any)()\n      } else {\n        return <Document {...htmlProps} {...docProps} />\n      }\n    }\n\n    let styles\n    if (hasDocumentGetInitialProps) {\n      styles = docProps.styles\n      head = docProps.head\n    } else {\n      styles = jsxStyleRegistry.styles()\n      jsxStyleRegistry.flush()\n    }\n\n    return {\n      contentHTML,\n      documentElement,\n      head,\n      headTags: [],\n      styles,\n    }\n  }\n\n  getTracer().setRootSpanAttribute('next.route', renderOpts.page)\n  const documentResult = await getTracer().trace(\n    RenderSpan.renderDocument,\n    {\n      spanName: `render route (pages) ${renderOpts.page}`,\n      attributes: {\n        'next.route': renderOpts.page,\n      },\n    },\n    async () => renderDocument()\n  )\n  if (!documentResult) {\n    return new RenderResult(null, { metadata })\n  }\n\n  const dynamicImportsIds = new Set<string | number>()\n  const dynamicImports = new Set<string>()\n\n  for (const mod of reactLoadableModules) {\n    const manifestItem = reactLoadableManifest[mod]\n\n    if (manifestItem) {\n      dynamicImportsIds.add(manifestItem.id)\n      manifestItem.files.forEach((item) => {\n        dynamicImports.add(item)\n      })\n    }\n  }\n\n  const hybridAmp = ampState.hybrid\n  const docComponentsRendered: DocumentProps['docComponentsRendered'] = {}\n\n  const {\n    assetPrefix,\n    defaultLocale,\n    disableOptimizedLoading,\n    domainLocales,\n    locale,\n    locales,\n    runtimeConfig,\n  } = renderOpts\n  const htmlProps: HtmlProps = {\n    __NEXT_DATA__: {\n      props, // The result of getInitialProps\n      page: pathname, // The rendered page\n      query, // querystring parsed / passed by the user\n      buildId: sharedContext.buildId,\n      assetPrefix: assetPrefix === '' ? undefined : assetPrefix, // send assetPrefix to the client side when configured, otherwise don't sent in the resulting HTML\n      runtimeConfig, // runtimeConfig if provided, otherwise don't sent in the resulting HTML\n      nextExport: nextExport === true ? true : undefined, // If this is a page exported by `next export`\n      autoExport: isAutoExport === true ? true : undefined, // If this is an auto exported page\n      isFallback,\n      isExperimentalCompile,\n      dynamicIds:\n        dynamicImportsIds.size === 0\n          ? undefined\n          : Array.from(dynamicImportsIds),\n      err: renderOpts.err ? serializeError(dev, renderOpts.err) : undefined, // Error if one happened, otherwise don't sent in the resulting HTML\n      gsp: !!getStaticProps ? true : undefined, // whether the page is getStaticProps\n      gssp: !!getServerSideProps ? true : undefined, // whether the page is getServerSideProps\n      customServer: sharedContext.customServer,\n      gip: hasPageGetInitialProps ? true : undefined, // whether the page has getInitialProps\n      appGip: !defaultAppGetInitialProps ? true : undefined, // whether the _app has getInitialProps\n      locale,\n      locales,\n      defaultLocale,\n      domainLocales,\n      isPreview: isPreview === true ? true : undefined,\n      notFoundSrcPage: notFoundSrcPage && dev ? notFoundSrcPage : undefined,\n    },\n    strictNextHead: renderOpts.strictNextHead,\n    buildManifest: filteredBuildManifest,\n    docComponentsRendered,\n    dangerousAsPath: router.asPath,\n    canonicalBase:\n      !renderOpts.ampPath && getRequestMeta(req, 'didStripLocale')\n        ? `${renderOpts.canonicalBase || ''}/${renderOpts.locale}`\n        : renderOpts.canonicalBase,\n    ampPath,\n    inAmpMode,\n    isDevelopment: !!dev,\n    hybridAmp,\n    dynamicImports: Array.from(dynamicImports),\n    dynamicCssManifest: new Set(renderOpts.dynamicCssManifest || []),\n    assetPrefix,\n    // Only enabled in production as development mode has features relying on HMR (style injection for example)\n    unstable_runtimeJS:\n      process.env.NODE_ENV === 'production'\n        ? pageConfig.unstable_runtimeJS\n        : undefined,\n    unstable_JsPreload: pageConfig.unstable_JsPreload,\n    assetQueryString,\n    scriptLoader,\n    locale,\n    disableOptimizedLoading,\n    head: documentResult.head,\n    headTags: documentResult.headTags,\n    styles: documentResult.styles,\n    crossOrigin: renderOpts.crossOrigin,\n    optimizeCss: renderOpts.optimizeCss,\n    nextConfigOutput: renderOpts.nextConfigOutput,\n    nextScriptWorkers: renderOpts.nextScriptWorkers,\n    runtime: globalRuntime,\n    largePageDataBytes: renderOpts.largePageDataBytes,\n    nextFontManifest: renderOpts.nextFontManifest,\n    experimentalClientTraceMetadata:\n      renderOpts.experimental.clientTraceMetadata,\n  }\n\n  const document = (\n    <AmpStateContext.Provider value={ampState}>\n      <HtmlContext.Provider value={htmlProps}>\n        {documentResult.documentElement(htmlProps)}\n      </HtmlContext.Provider>\n    </AmpStateContext.Provider>\n  )\n\n  const documentHTML = await getTracer().trace(\n    RenderSpan.renderToString,\n    async () => renderToString(document)\n  )\n\n  if (process.env.NODE_ENV !== 'production') {\n    const nonRenderedComponents = []\n    const expectedDocComponents = ['Main', 'Head', 'NextScript', 'Html']\n\n    for (const comp of expectedDocComponents) {\n      if (!(docComponentsRendered as any)[comp]) {\n        nonRenderedComponents.push(comp)\n      }\n    }\n\n    if (nonRenderedComponents.length) {\n      const missingComponentList = nonRenderedComponents\n        .map((e) => `<${e} />`)\n        .join(', ')\n      const plural = nonRenderedComponents.length !== 1 ? 's' : ''\n      console.warn(\n        `Your custom Document (pages/_document) did not render all the required subcomponent${plural}.\\n` +\n          `Missing component${plural}: ${missingComponentList}\\n` +\n          'Read how to fix here: https://nextjs.org/docs/messages/missing-document-component'\n      )\n    }\n  }\n\n  const [renderTargetPrefix, renderTargetSuffix] = documentHTML.split(\n    '<next-js-internal-body-render-target></next-js-internal-body-render-target>',\n    2\n  )\n\n  let prefix = ''\n  if (!documentHTML.startsWith(DOCTYPE)) {\n    prefix += DOCTYPE\n  }\n  prefix += renderTargetPrefix\n  if (inAmpMode) {\n    prefix += '<!-- __NEXT_DATA__ -->'\n  }\n\n  const content = prefix + documentResult.contentHTML + renderTargetSuffix\n\n  const optimizedHtml = await postProcessHTML(pathname, content, renderOpts, {\n    inAmpMode,\n    hybridAmp,\n  })\n\n  return new RenderResult(optimizedHtml, { metadata })\n}\n\nexport type PagesRender = (\n  req: IncomingMessage,\n  res: ServerResponse,\n  pathname: string,\n  query: NextParsedUrlQuery,\n  renderOpts: RenderOpts,\n  sharedContext: PagesSharedContext,\n  renderContext: PagesRenderContext\n) => Promise<RenderResult>\n\nexport const renderToHTML: PagesRender = (\n  req,\n  res,\n  pathname,\n  query,\n  renderOpts,\n  sharedContext,\n  renderContext\n) => {\n  return renderToHTMLImpl(\n    req,\n    res,\n    pathname,\n    query,\n    renderOpts,\n    renderOpts,\n    sharedContext,\n    renderContext\n  )\n}\n"], "names": ["setLazyProp", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "React", "ReactDOMServerPages", "StyleRegistry", "createStyleRegistry", "GSP_NO_RETURNED_VALUE", "GSSP_COMPONENT_MEMBER_ERROR", "GSSP_NO_RETURNED_VALUE", "STATIC_STATUS_PAGE_GET_INITIAL_PROPS_ERROR", "SERVER_PROPS_GET_INIT_PROPS_CONFLICT", "SERVER_PROPS_SSG_CONFLICT", "SSG_GET_INITIAL_PROPS_CONFLICT", "UNSTABLE_REVALIDATE_RENAME_ERROR", "NEXT_BUILTIN_DOCUMENT", "SERVER_PROPS_ID", "STATIC_PROPS_ID", "STATIC_STATUS_PAGES", "isSerializableProps", "isInAmpMode", "AmpStateContext", "defaultHead", "HeadManagerContext", "Loadable", "LoadableContext", "RouterContext", "isDynamicRoute", "getDisplayName", "isResSent", "loadGetInitialProps", "HtmlContext", "normalizePagePath", "denormalizePagePath", "getRequestMeta", "allowedStatusCodes", "getRedirectStatus", "RenderResult", "isError", "streamToString", "renderToInitialFizzStream", "ImageConfigContext", "stripAnsi", "stripInternalQueries", "adaptForAppRouterInstance", "adaptForPathParams", "adaptForSearchParams", "PathnameContextProviderAdapter", "AppRouterContext", "SearchParamsContext", "PathParamsContext", "getTracer", "RenderSpan", "ReflectAdapter", "getCacheControlHeader", "getErrorSource", "tryGetPreviewData", "warn", "postProcessHTML", "DOCTYPE", "process", "env", "NEXT_RUNTIME", "require", "console", "bind", "_pathname", "html", "noRouter", "message", "Error", "renderToString", "element", "renderStream", "renderToReadableStream", "allReady", "ServerRouter", "constructor", "pathname", "query", "as", "<PERSON><PERSON><PERSON><PERSON>", "isReady", "basePath", "locale", "locales", "defaultLocale", "domainLocales", "isPreview", "isLocaleDomain", "route", "replace", "<PERSON><PERSON><PERSON>", "push", "reload", "back", "forward", "prefetch", "beforePopState", "enhanceComponents", "options", "App", "Component", "enhanceApp", "enhanceComponent", "renderPageTree", "props", "invalidKeysMsg", "methodName", "<PERSON><PERSON><PERSON><PERSON>", "docsPathname", "toLocaleLowerCase", "join", "checkRedirectValues", "redirect", "req", "method", "destination", "permanent", "statusCode", "errors", "hasStatusCode", "hasPermanent", "has", "destinationType", "basePathType", "length", "url", "errorToJSON", "err", "source", "name", "stack", "digest", "serializeError", "dev", "renderToHTMLImpl", "res", "renderOpts", "extra", "sharedContext", "renderContext", "headers", "metadata", "assetQueryString", "userAgent", "toLowerCase", "includes", "Date", "now", "deploymentId", "Object", "assign", "ampPath", "pageConfig", "buildManifest", "reactLoadableManifest", "ErrorDebug", "getStaticProps", "getStaticPaths", "getServerSideProps", "isNextDataRequest", "params", "previewProps", "images", "runtime", "globalRuntime", "isExperimentalCompile", "expireTime", "Document", "OriginComponent", "notFoundSrcPage", "developmentNotFoundSourcePage", "isSSG", "isBuildTimeSSG", "nextExport", "defaultAppGetInitialProps", "getInitialProps", "origGetInitialProps", "hasPageGetInitialProps", "hasPageScripts", "unstable_scriptLoader", "pageIsDynamic", "defaultErrorGetInitialProps", "isAutoExport", "<PERSON><PERSON><PERSON><PERSON>", "revalidate", "expire", "nextConfigOutput", "resolvedAsPath", "isValidElementType", "amp", "endsWith", "setIsrStatus", "preloadAll", "undefined", "previewData", "multiZoneDraftMode", "routerIsReady", "router", "appRouter", "<PERSON><PERSON><PERSON><PERSON>", "jsxStyleRegistry", "ampState", "ampFirs<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "Boolean", "hybrid", "inAmpMode", "head", "reactLoadableModules", "initialScripts", "beforeInteractive", "concat", "filter", "script", "strategy", "map", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "children", "Provider", "value", "updateHead", "state", "updateScripts", "scripts", "mountedInstances", "Set", "moduleName", "registry", "Noop", "AppContainerWithIsomorphicFiberStructure", "ctx", "AppTree", "defaultGetInitialProps", "docCtx", "AppComp", "renderPageHead", "renderPage", "styles", "nonce", "flush", "styledJsxInsertedHTML", "__N_PREVIEW", "data", "trace", "spanName", "attributes", "draftMode", "preview", "revalidateReason", "isOnDemandRevalidate", "staticPropsError", "code", "keys", "key", "NODE_ENV", "notFound", "isNotFound", "__N_REDIRECT", "__N_REDIRECT_STATUS", "__N_REDIRECT_BASE_PATH", "isRedirect", "Number", "isInteger", "Math", "ceil", "JSON", "stringify", "pageProps", "cacheControl", "pageData", "canAccessRes", "resOrProxy", "deferred<PERSON><PERSON>nt", "Proxy", "get", "obj", "prop", "resolvedUrl", "serverSidePropsError", "Promise", "unstable_notFound", "unstable_redirect", "filteredBuildManifest", "page", "pages", "lowPriorityFiles", "f", "Body", "div", "id", "renderDocument", "BuiltinFunctionalDocument", "loadDocumentInitialProps", "renderShell", "EnhancedApp", "EnhancedComponent", "stream", "documentCtx", "docProps", "renderContent", "_App", "_Component", "content", "ReactDOMServer", "hasDocumentGetInitialProps", "documentInitialPropsRes", "rawStyledJsxInsertedHTML", "all", "contentHTML", "documentElement", "htmlProps", "headTags", "setRootSpanAttribute", "documentResult", "dynamicImportsIds", "dynamicImports", "mod", "manifestItem", "add", "files", "for<PERSON>ach", "item", "hybridAmp", "doc<PERSON><PERSON><PERSON><PERSON><PERSON>ed", "assetPrefix", "disableOptimizedLoading", "runtimeConfig", "__NEXT_DATA__", "buildId", "autoExport", "dynamicIds", "size", "Array", "from", "gsp", "gssp", "customServer", "gip", "appGip", "strictNextHead", "dangerousAsPath", "canonicalBase", "isDevelopment", "dynamicCssManifest", "unstable_runtimeJS", "unstable_JsPreload", "crossOrigin", "optimizeCss", "nextScriptWorkers", "largePageDataBytes", "nextFontManifest", "experimentalClientTraceMetadata", "experimental", "clientTraceMetadata", "document", "documentHTML", "nonRenderedComponents", "expectedDocComponents", "comp", "missingComponentList", "e", "plural", "renderTargetPrefix", "renderTargetSuffix", "split", "prefix", "startsWith", "optimizedHtml", "renderToHTML"], "mappings": ";AAiBA,SAGEA,WAAW,QACN,cAAa;AACpB,SAASC,eAAe,QAAQ,gCAA+B;AAmB/D,OAAOC,WAAyB,QAAO;AACvC,OAAOC,yBAAyB,uCAAsC;AACtE,SAASC,aAAa,EAAEC,mBAAmB,QAAQ,aAAY;AAC/D,SACEC,qBAAqB,EACrBC,2BAA2B,EAC3BC,sBAAsB,EACtBC,0CAA0C,EAC1CC,oCAAoC,EACpCC,yBAAyB,EACzBC,8BAA8B,EAC9BC,gCAAgC,QAC3B,mBAAkB;AACzB,SACEC,qBAAqB,EACrBC,eAAe,EACfC,eAAe,EACfC,mBAAmB,QACd,0BAAyB;AAChC,SAASC,mBAAmB,QAAQ,+BAA8B;AAClE,SAASC,WAAW,QAAQ,yBAAwB;AACpD,SAASC,eAAe,QAAQ,2CAA0C;AAC1E,SAASC,WAAW,QAAQ,qBAAoB;AAChD,SAASC,kBAAkB,QAAQ,oDAAmD;AACtF,OAAOC,cAAc,wCAAuC;AAC5D,SAASC,eAAe,QAAQ,gDAA+C;AAC/E,SAASC,aAAa,QAAQ,8CAA6C;AAC3E,SAASC,cAAc,QAAQ,wCAAuC;AACtE,SACEC,cAAc,EACdC,SAAS,EACTC,mBAAmB,QACd,sBAAqB;AAC5B,SAASC,WAAW,QAAQ,4CAA2C;AACvE,SAASC,iBAAiB,QAAQ,8CAA6C;AAC/E,SAASC,mBAAmB,QAAQ,gDAA+C;AACnF,SAASC,cAAc,QAAQ,iBAAgB;AAC/C,SAASC,kBAAkB,EAAEC,iBAAiB,QAAQ,yBAAwB;AAC9E,OAAOC,kBAAsD,kBAAiB;AAC9E,OAAOC,aAAa,kBAAiB;AACrC,SACEC,cAAc,EACdC,yBAAyB,QACpB,yCAAwC;AAC/C,SAASC,kBAAkB,QAAQ,oDAAmD;AACtF,OAAOC,eAAe,gCAA+B;AACrD,SAASC,oBAAoB,QAAQ,mBAAkB;AACvD,SACEC,yBAAyB,EACzBC,kBAAkB,EAClBC,oBAAoB,EACpBC,8BAA8B,QACzB,gCAA+B;AACtC,SAASC,gBAAgB,QAAQ,kDAAiD;AAClF,SACEC,mBAAmB,EACnBC,iBAAiB,QACZ,oDAAmD;AAC1D,SAASC,SAAS,QAAQ,qBAAoB;AAC9C,SAASC,UAAU,QAAQ,wBAAuB;AAClD,SAASC,cAAc,QAAQ,wCAAuC;AACtE,SAASC,qBAAqB,QAAQ,sBAAqB;AAC3D,SAASC,cAAc,QAAQ,6BAA4B;AAI3D,IAAIC;AACJ,IAAIC;AACJ,IAAIC;AAEJ,MAAMC,UAAU;AAEhB,IAAIC,QAAQC,GAAG,CAACC,YAAY,KAAK,QAAQ;IACvCN,oBACEO,QAAQ,yCAAyCP,iBAAiB;IACpEC,OAAOM,QAAQ,uBAAuBN,IAAI;IAC1CC,kBAAkBK,QAAQ,kBAAkBL,eAAe;AAC7D,OAAO;IACLD,OAAOO,QAAQP,IAAI,CAACQ,IAAI,CAACD;IACzBN,kBAAkB,OAAOQ,WAAmBC,OAAiBA;AAC/D;AAEA,SAASC;IACP,MAAMC,UACJ;IACF,MAAM,qBAAkB,CAAlB,IAAIC,MAAMD,UAAV,qBAAA;eAAA;oBAAA;sBAAA;IAAiB;AACzB;AAEA,eAAeE,eAAeC,OAA2B;IACvD,MAAMC,eAAe,MAAMrE,oBAAoBsE,sBAAsB,CAACF;IACtE,MAAMC,aAAaE,QAAQ;IAC3B,OAAOpC,eAAekC;AACxB;AAEA,MAAMG;IAgBJC,YACEC,QAAgB,EAChBC,KAAqB,EACrBC,EAAU,EACV,EAAEC,UAAU,EAA2B,EACvCC,OAAgB,EAChBC,QAAgB,EAChBC,MAAe,EACfC,OAA2B,EAC3BC,aAAsB,EACtBC,aAAuC,EACvCC,SAAmB,EACnBC,cAAwB,CACxB;QACA,IAAI,CAACC,KAAK,GAAGZ,SAASa,OAAO,CAAC,OAAO,OAAO;QAC5C,IAAI,CAACb,QAAQ,GAAGA;QAChB,IAAI,CAACC,KAAK,GAAGA;QACb,IAAI,CAACa,MAAM,GAAGZ;QACd,IAAI,CAACC,UAAU,GAAGA;QAClB,IAAI,CAACE,QAAQ,GAAGA;QAChB,IAAI,CAACC,MAAM,GAAGA;QACd,IAAI,CAACC,OAAO,GAAGA;QACf,IAAI,CAACC,aAAa,GAAGA;QACrB,IAAI,CAACJ,OAAO,GAAGA;QACf,IAAI,CAACK,aAAa,GAAGA;QACrB,IAAI,CAACC,SAAS,GAAG,CAAC,CAACA;QACnB,IAAI,CAACC,cAAc,GAAG,CAAC,CAACA;IAC1B;IAEAI,OAAY;QACVzB;IACF;IACAuB,UAAe;QACbvB;IACF;IACA0B,SAAS;QACP1B;IACF;IACA2B,OAAO;QACL3B;IACF;IACA4B,UAAgB;QACd5B;IACF;IACA6B,WAAgB;QACd7B;IACF;IACA8B,iBAAiB;QACf9B;IACF;AACF;AAEA,SAAS+B,kBACPC,OAA2B,EAC3BC,GAAY,EACZC,SAA4B;IAK5B,8BAA8B;IAC9B,IAAI,OAAOF,YAAY,YAAY;QACjC,OAAO;YACLC;YACAC,WAAWF,QAAQE;QACrB;IACF;IAEA,OAAO;QACLD,KAAKD,QAAQG,UAAU,GAAGH,QAAQG,UAAU,CAACF,OAAOA;QACpDC,WAAWF,QAAQI,gBAAgB,GAC/BJ,QAAQI,gBAAgB,CAACF,aACzBA;IACN;AACF;AAEA,SAASG,eACPJ,GAAY,EACZC,SAA4B,EAC5BI,KAAU;IAEV,qBAAO,KAACL;QAAIC,WAAWA;QAAY,GAAGI,KAAK;;AAC7C;AAgHA,MAAMC,iBAAiB,CACrBC,YACAC;IAEA,MAAMC,eAAe,CAAC,QAAQ,EAAEF,WAAWG,iBAAiB,GAAG,MAAM,CAAC;IAEtE,OACE,CAAC,qCAAqC,EAAEH,WAAW,wFAAwF,CAAC,GAC5I,CAAC,6DAA6D,CAAC,GAC/D,CAAC,gCAAgC,EAAEC,YAAYG,IAAI,CAAC,MAAM,CAAC,CAAC,GAC5D,CAAC,8CAA8C,EAAEF,cAAc;AAEnE;AAEA,SAASG,oBACPC,QAAkB,EAClBC,GAAoB,EACpBC,MAA+C;IAE/C,MAAM,EAAEC,WAAW,EAAEC,SAAS,EAAEC,UAAU,EAAEpC,QAAQ,EAAE,GAAG+B;IACzD,IAAIM,SAAmB,EAAE;IAEzB,MAAMC,gBAAgB,OAAOF,eAAe;IAC5C,MAAMG,eAAe,OAAOJ,cAAc;IAE1C,IAAII,gBAAgBD,eAAe;QACjCD,OAAO3B,IAAI,CAAC,CAAC,yDAAyD,CAAC;IACzE,OAAO,IAAI6B,gBAAgB,OAAOJ,cAAc,WAAW;QACzDE,OAAO3B,IAAI,CAAC,CAAC,2CAA2C,CAAC;IAC3D,OAAO,IAAI4B,iBAAiB,CAACtF,mBAAmBwF,GAAG,CAACJ,aAAc;QAChEC,OAAO3B,IAAI,CACT,CAAC,wCAAwC,EAAE;eAAI1D;SAAmB,CAAC6E,IAAI,CACrE,OACC;IAEP;IACA,MAAMY,kBAAkB,OAAOP;IAE/B,IAAIO,oBAAoB,UAAU;QAChCJ,OAAO3B,IAAI,CACT,CAAC,8CAA8C,EAAE+B,iBAAiB;IAEtE;IAEA,MAAMC,eAAe,OAAO1C;IAE5B,IAAI0C,iBAAiB,eAAeA,iBAAiB,WAAW;QAC9DL,OAAO3B,IAAI,CACT,CAAC,sDAAsD,EAAEgC,cAAc;IAE3E;IAEA,IAAIL,OAAOM,MAAM,GAAG,GAAG;QACrB,MAAM,qBAKL,CALK,IAAIxD,MACR,CAAC,sCAAsC,EAAE8C,OAAO,KAAK,EAAED,IAAIY,GAAG,CAAC,EAAE,CAAC,GAChEP,OAAOR,IAAI,CAAC,WACZ,OACA,CAAC,0EAA0E,CAAC,GAJ1E,qBAAA;mBAAA;wBAAA;0BAAA;QAKN;IACF;AACF;AAEA,OAAO,SAASgB,YAAYC,GAAU;IACpC,IAAIC,SACF;IAEF,IAAItE,QAAQC,GAAG,CAACC,YAAY,KAAK,QAAQ;QACvCoE,SAAS3E,eAAe0E,QAAQ;IAClC;IAEA,OAAO;QACLE,MAAMF,IAAIE,IAAI;QACdD;QACA7D,SAAS3B,UAAUuF,IAAI5D,OAAO;QAC9B+D,OAAOH,IAAIG,KAAK;QAChBC,QAAQ,AAACJ,IAAYI,MAAM;IAC7B;AACF;AAEA,SAASC,eACPC,GAAwB,EACxBN,GAAU;IAKV,IAAIM,KAAK;QACP,OAAOP,YAAYC;IACrB;IAEA,OAAO;QACLE,MAAM;QACN9D,SAAS;QACTkD,YAAY;IACd;AACF;AAEA,OAAO,eAAeiB,iBACpBrB,GAAoB,EACpBsB,GAAmB,EACnB3D,QAAgB,EAChBC,KAAyB,EACzB2D,UAAmD,EACnDC,KAAsB,EACtBC,aAAiC,EACjCC,aAAiC;IAEjC,uEAAuE;IACvE5I,YAAY;QAAEkH,KAAKA;IAAW,GAAG,WAAWjH,gBAAgBiH,IAAI2B,OAAO;IAEvE,MAAMC,WAAsC,CAAC;IAE7CA,SAASC,gBAAgB,GACvB,AAACN,WAAWH,GAAG,IAAIG,WAAWM,gBAAgB,IAAK;IAErD,IAAIN,WAAWH,GAAG,IAAI,CAACQ,SAASC,gBAAgB,EAAE;QAChD,MAAMC,YAAY,AAAC9B,CAAAA,IAAI2B,OAAO,CAAC,aAAa,IAAI,EAAC,EAAGI,WAAW;QAC/D,IAAID,UAAUE,QAAQ,CAAC,aAAa,CAACF,UAAUE,QAAQ,CAAC,WAAW;YACjE,+EAA+E;YAC/E,4EAA4E;YAC5E,6FAA6F;YAC7F,yFAAyF;YACzF,iCAAiC;YACjCJ,SAASC,gBAAgB,GAAG,CAAC,IAAI,EAAEI,KAAKC,GAAG,IAAI;QACjD;IACF;IAEA,iEAAiE;IACjE,IAAIT,cAAcU,YAAY,EAAE;QAC9BP,SAASC,gBAAgB,IAAI,GAAGD,SAASC,gBAAgB,GAAG,MAAM,IAAI,IAAI,EACxEJ,cAAcU,YAAY,EAC1B;IACJ;IAEA,qCAAqC;IACrCvE,QAAQwE,OAAOC,MAAM,CAAC,CAAC,GAAGzE;IAE1B,MAAM,EACJkD,GAAG,EACHM,MAAM,KAAK,EACXkB,UAAU,EAAE,EACZC,aAAa,CAAC,CAAC,EACfC,aAAa,EACbC,qBAAqB,EACrBC,UAAU,EACVC,cAAc,EACdC,cAAc,EACdC,kBAAkB,EAClBC,iBAAiB,EACjBC,MAAM,EACNC,YAAY,EACZhF,QAAQ,EACRiF,MAAM,EACNC,SAASC,aAAa,EACtBC,qBAAqB,EACrBC,UAAU,EACX,GAAG9B;IACJ,MAAM,EAAErC,GAAG,EAAE,GAAGsC;IAEhB,MAAMK,mBAAmBD,SAASC,gBAAgB;IAElD,IAAIyB,WAAW9B,MAAM8B,QAAQ;IAE7B,IAAInE,YACFoC,WAAWpC,SAAS;IACtB,MAAMoE,kBAAkBpE;IAExB,MAAMrB,aAAa4D,cAAc5D,UAAU,IAAI;IAC/C,MAAM0F,kBAAkB9B,cAAc+B,6BAA6B;IAEnE,+CAA+C;IAC/CjI,qBAAqBoC;IAErB,MAAM8F,QAAQ,CAAC,CAACf;IAChB,MAAMgB,iBAAiBD,SAASnC,WAAWqC,UAAU;IACrD,MAAMC,4BACJ3E,IAAI4E,eAAe,KAAK,AAAC5E,IAAY6E,mBAAmB;IAE1D,MAAMC,yBAAyB,CAAC,EAAE7E,6BAAD,AAACA,UAAmB2E,eAAe;IACpE,MAAMG,iBAAkB9E,6BAAD,AAACA,UAAmB+E,qBAAqB;IAEhE,MAAMC,gBAAgB3J,eAAemD;IAErC,MAAMyG,8BACJzG,aAAa,aACb,AAACwB,UAAkB2E,eAAe,KAChC,AAAC3E,UAAkB4E,mBAAmB;IAE1C,IACExC,WAAWqC,UAAU,IACrBI,0BACA,CAACI,6BACD;QACA9H,KACE,CAAC,kCAAkC,EAAEqB,SAAS,CAAC,CAAC,GAC9C,CAAC,6DAA6D,CAAC,GAC/D,CAAC,wDAAwD,CAAC,GAC1D,CAAC,sEAAsE,CAAC;IAE9E;IAEA,IAAI0G,eACF,CAACL,0BACDH,6BACA,CAACH,SACD,CAACb;IAEH,2DAA2D;IAC3D,uDAAuD;IACvD,4DAA4D;IAC5D,gBAAgB;IAChB,IAAIwB,gBAAgB,CAACjD,OAAOgC,uBAAuB;QACjD9B,IAAIgD,SAAS,CACX,iBACAnI,sBAAsB;YAAEoI,YAAY;YAAOC,QAAQnB;QAAW;QAEhEgB,eAAe;IACjB;IAEA,IAAIL,0BAA0BN,OAAO;QACnC,MAAM,qBAA0D,CAA1D,IAAIvG,MAAMzD,iCAAiC,CAAC,CAAC,EAAEiE,UAAU,GAAzD,qBAAA;mBAAA;wBAAA;0BAAA;QAAyD;IACjE;IAEA,IAAIqG,0BAA0BnB,oBAAoB;QAChD,MAAM,qBAAgE,CAAhE,IAAI1F,MAAM3D,uCAAuC,CAAC,CAAC,EAAEmE,UAAU,GAA/D,qBAAA;mBAAA;wBAAA;0BAAA;QAA+D;IACvE;IAEA,IAAIkF,sBAAsBa,OAAO;QAC/B,MAAM,qBAAqD,CAArD,IAAIvG,MAAM1D,4BAA4B,CAAC,CAAC,EAAEkE,UAAU,GAApD,qBAAA;mBAAA;wBAAA;0BAAA;QAAoD;IAC5D;IAEA,IAAIkF,sBAAsBtB,WAAWkD,gBAAgB,KAAK,UAAU;QAClE,MAAM,qBAEL,CAFK,IAAItH,MACR,8IADI,qBAAA;mBAAA;wBAAA;0BAAA;QAEN;IACF;IAEA,IAAIyF,kBAAkB,CAACuB,eAAe;QACpC,MAAM,qBAGL,CAHK,IAAIhH,MACR,CAAC,uEAAuE,EAAEQ,SAAS,EAAE,CAAC,GACpF,CAAC,8EAA8E,CAAC,GAF9E,qBAAA;mBAAA;wBAAA;0BAAA;QAGN;IACF;IAEA,IAAI,CAAC,CAACiF,kBAAkB,CAACc,OAAO;QAC9B,MAAM,qBAEL,CAFK,IAAIvG,MACR,CAAC,qDAAqD,EAAEQ,SAAS,qDAAqD,CAAC,GADnH,qBAAA;mBAAA;wBAAA;0BAAA;QAEN;IACF;IAEA,IAAI+F,SAASS,iBAAiB,CAACvB,gBAAgB;QAC7C,MAAM,qBAGL,CAHK,IAAIzF,MACR,CAAC,qEAAqE,EAAEQ,SAAS,EAAE,CAAC,GAClF,CAAC,0EAA0E,CAAC,GAF1E,qBAAA;mBAAA;wBAAA;0BAAA;QAGN;IACF;IAEA,IAAIc,SAAiB8C,WAAWmD,cAAc,IAAK1E,IAAIY,GAAG;IAE1D,IAAIQ,KAAK;QACP,MAAM,EAAEuD,kBAAkB,EAAE,GAAG/H,QAAQ;QACvC,IAAI,CAAC+H,mBAAmBxF,YAAY;YAClC,MAAM,qBAEL,CAFK,IAAIhC,MACR,CAAC,sDAAsD,EAAEQ,SAAS,CAAC,CAAC,GADhE,qBAAA;uBAAA;4BAAA;8BAAA;YAEN;QACF;QAEA,IAAI,CAACgH,mBAAmBzF,MAAM;YAC5B,MAAM,qBAEL,CAFK,IAAI/B,MACR,CAAC,4DAA4D,CAAC,GAD1D,qBAAA;uBAAA;4BAAA;8BAAA;YAEN;QACF;QAEA,IAAI,CAACwH,mBAAmBrB,WAAW;YACjC,MAAM,qBAEL,CAFK,IAAInG,MACR,CAAC,iEAAiE,CAAC,GAD/D,qBAAA;uBAAA;4BAAA;8BAAA;YAEN;QACF;QAEA,IAAIkH,gBAAgBvG,YAAY;YAC9B,iEAAiE;YACjEF,QAAQ;gBACN,GAAIA,MAAMgH,GAAG,GACT;oBACEA,KAAKhH,MAAMgH,GAAG;gBAChB,IACA,CAAC,CAAC;YACR;YACAnG,SAAS,GAAGd,WACV,qEAAqE;YACrEqC,IAAIY,GAAG,CAAEiE,QAAQ,CAAC,QAAQlH,aAAa,OAAO,CAACwG,gBAAgB,MAAM,IACrE;YACFnE,IAAIY,GAAG,GAAGjD;QACZ;QAEA,IAAIA,aAAa,UAAWqG,CAAAA,0BAA0BnB,kBAAiB,GAAI;YACzE,MAAM,qBAEL,CAFK,IAAI1F,MACR,CAAC,cAAc,EAAE5D,4CAA4C,GADzD,qBAAA;uBAAA;4BAAA;8BAAA;YAEN;QACF;QACA,IACEQ,oBAAoBiI,QAAQ,CAACrE,aAC5BqG,CAAAA,0BAA0BnB,kBAAiB,GAC5C;YACA,MAAM,qBAEL,CAFK,IAAI1F,MACR,CAAC,OAAO,EAAEQ,SAAS,GAAG,EAAEpE,4CAA4C,GADhE,qBAAA;uBAAA;4BAAA;8BAAA;YAEN;QACF;QAEA,IAAIgI,8BAAAA,WAAYuD,YAAY,EAAE;YAC5BvD,WAAWuD,YAAY,CAACrG,QAAQiF,SAASW,eAAe,OAAO;QACjE;IACF;IAEA,KAAK,MAAM5E,cAAc;QACvB;QACA;QACA;KACD,CAAE;QACD,IAAKN,6BAAD,AAACA,SAAmB,CAACM,WAAW,EAAE;YACpC,MAAM,qBAEL,CAFK,IAAItC,MACR,CAAC,KAAK,EAAEQ,SAAS,CAAC,EAAE8B,WAAW,CAAC,EAAEpG,6BAA6B,GAD3D,qBAAA;uBAAA;4BAAA;8BAAA;YAEN;QACF;IACF;IAEA,MAAMgB,SAAS0K,UAAU,GAAG,2CAA2C;;IAEvE,IAAI1G,YAAiC2G;IACrC,IAAIC;IAEJ,IACE,AAACvB,CAAAA,SAASb,kBAAiB,KAC3B,CAAC/E,cACDrB,QAAQC,GAAG,CAACC,YAAY,KAAK,UAC7BqG,cACA;QACA,uEAAuE;QACvE,oEAAoE;QACpE,UAAU;QACViC,cAAc5I,kBACZ2D,KACAsB,KACA0B,cACA,CAAC,CAACzB,WAAW2D,kBAAkB;QAEjC7G,YAAY4G,gBAAgB;IAC9B;IAEA,yBAAyB;IACzB,MAAME,gBAAgB,CAAC,CACrBtC,CAAAA,sBACAmB,0BACC,CAACH,6BAA6B,CAACH,SAChCN,qBAAoB;IAEtB,MAAMgC,SAAS,IAAI3H,aACjBE,UACAC,OACAa,QACA;QACEX,YAAYA;IACd,GACAqH,eACAnH,UACAuD,WAAWtD,MAAM,EACjBsD,WAAWrD,OAAO,EAClBqD,WAAWpD,aAAa,EACxBoD,WAAWnD,aAAa,EACxBC,WACAtD,eAAeiF,KAAK;IAGtB,MAAMqF,YAAY5J,0BAA0B2J;IAE5C,IAAIE,eAAoB,CAAC;IACzB,MAAMC,mBAAmBpM;IACzB,MAAMqM,WAAW;QACfC,UAAUlD,WAAWqC,GAAG,KAAK;QAC7Bc,UAAUC,QAAQ/H,MAAMgH,GAAG;QAC3BgB,QAAQrD,WAAWqC,GAAG,KAAK;IAC7B;IAEA,wCAAwC;IACxC,MAAMiB,YAAYpJ,QAAQC,GAAG,CAACC,YAAY,KAAK,UAAU1C,YAAYuL;IACrE,IAAIM,OAAsB3L,YAAY0L;IACtC,MAAME,uBAAiC,EAAE;IAEzC,IAAIC,iBAAsB,CAAC;IAC3B,IAAI/B,gBAAgB;QAClB+B,eAAeC,iBAAiB,GAAG,EAAE,CAClCC,MAAM,CAACjC,kBACPkC,MAAM,CAAC,CAACC,SAAgBA,OAAO7G,KAAK,CAAC8G,QAAQ,KAAK,qBAClDC,GAAG,CAAC,CAACF,SAAgBA,OAAO7G,KAAK;IACtC;IAEA,MAAMgH,eAAe,CAAC,EAAEC,QAAQ,EAA6B,iBAC3D,KAAC3K,iBAAiB4K,QAAQ;YAACC,OAAOrB;sBAChC,cAAA,KAACvJ,oBAAoB2K,QAAQ;gBAACC,OAAO/K,qBAAqByJ;0BACxD,cAAA,KAACxJ;oBACCwJ,QAAQA;oBACRf,cAAcA;8BAEd,cAAA,KAACtI,kBAAkB0K,QAAQ;wBAACC,OAAOhL,mBAAmB0J;kCACpD,cAAA,KAAC7K,cAAckM,QAAQ;4BAACC,OAAOtB;sCAC7B,cAAA,KAAClL,gBAAgBuM,QAAQ;gCAACC,OAAOlB;0CAC/B,cAAA,KAACpL,mBAAmBqM,QAAQ;oCAC1BC,OAAO;wCACLC,YAAY,CAACC;4CACXd,OAAOc;wCACT;wCACAC,eAAe,CAACC;4CACdxB,eAAewB;wCACjB;wCACAA,SAASd;wCACTe,kBAAkB,IAAIC;oCACxB;8CAEA,cAAA,KAAC1M,gBAAgBmM,QAAQ;wCACvBC,OAAO,CAACO,aACNlB,qBAAqBrH,IAAI,CAACuI;kDAG5B,cAAA,KAAC/N;4CAAcgO,UAAU3B;sDACvB,cAAA,KAACjK,mBAAmBmL,QAAQ;gDAACC,OAAOzD;0DACjCuD;;;;;;;;;;;IAavB,yEAAyE;IACzE,4EAA4E;IAC5E,uDAAuD;IACvD,6EAA6E;IAC7E,iBAAiB;IACjB,+CAA+C;IAC/C,MAAMW,OAAO,IAAM;IACnB,MAAMC,2CAED,CAAC,EAAEZ,QAAQ,EAAE;QAChB,qBACE;;8BAEE,KAACW;8BACD,KAACZ;8BACC,cAAA;;4BAEGnF,oBACC;;oCACGoF;kDACD,KAACW;;iCAGHX;0CAGF,KAACW;;;;;;IAKX;IAEA,MAAME,MAAM;QACVvG;QACAd,KAAKqE,eAAeW,YAAYhF;QAChCsB,KAAK+C,eAAeW,YAAY1D;QAChC3D;QACAC;QACAa;QACAR,QAAQsD,WAAWtD,MAAM;QACzBC,SAASqD,WAAWrD,OAAO;QAC3BC,eAAeoD,WAAWpD,aAAa;QACvCmJ,SAAS,CAAC/H;YACR,qBACE,KAAC6H;0BACE9H,eAAeJ,KAAKqE,iBAAiB;oBAAE,GAAGhE,KAAK;oBAAE6F;gBAAO;;QAG/D;QACAmC,wBAAwB,OACtBC,QACAvI,UAA8B,CAAC,CAAC;YAEhC,MAAMG,aAAa,CAACqI;gBAClB,OAAO,CAAClI,sBAAe,KAACkI;wBAAS,GAAGlI,KAAK;;YAC3C;YAEA,MAAM,EAAEvC,IAAI,EAAE8I,MAAM4B,cAAc,EAAE,GAAG,MAAMF,OAAOG,UAAU,CAAC;gBAC7DvI;YACF;YACA,MAAMwI,SAASrC,iBAAiBqC,MAAM,CAAC;gBAAEC,OAAO5I,QAAQ4I,KAAK;YAAC;YAC9DtC,iBAAiBuC,KAAK;YACtB,OAAO;gBAAE9K;gBAAM8I,MAAM4B;gBAAgBE;YAAO;QAC9C;IACF;IACA,IAAIrI;IAEJ,MAAMqE,aACJ,CAACF,SAAUnC,CAAAA,WAAWqC,UAAU,IAAKxC,OAAQiD,CAAAA,gBAAgBvG,UAAS,CAAE;IAE1E,MAAMiK,wBAAwB;QAC5B,MAAMH,SAASrC,iBAAiBqC,MAAM;QACtCrC,iBAAiBuC,KAAK;QACtB,qBAAO;sBAAGF;;IACZ;IAEArI,QAAQ,MAAM5E,oBAAoBuE,KAAK;QACrCoI,SAASD,IAAIC,OAAO;QACpBnI;QACAiG;QACAiC;IACF;IAEA,IAAI,AAAC3D,CAAAA,SAASb,kBAAiB,KAAMxE,WAAW;QAC9CkB,MAAMyI,WAAW,GAAG;IACtB;IAEA,IAAItE,OAAO;QACTnE,KAAK,CAACzF,gBAAgB,GAAG;IAC3B;IAEA,IAAI4J,SAAS,CAAC5F,YAAY;QACxB,IAAImK;QAEJ,IAAI;YACFA,OAAO,MAAMjM,YAAYkM,KAAK,CAC5BjM,WAAW0G,cAAc,EACzB;gBACEwF,UAAU,CAAC,eAAe,EAAExK,UAAU;gBACtCyK,YAAY;oBACV,cAAczK;gBAChB;YACF,GACA,IACEgF,eAAe;oBACb,GAAIwB,gBAAgB;wBAAEpB;oBAAO,IAAIiC,SAAS;oBAC1C,GAAI3G,YACA;wBAAEgK,WAAW;wBAAMC,SAAS;wBAAMrD,aAAaA;oBAAY,IAC3DD,SAAS;oBACb9G,SAAS;2BAAKqD,WAAWrD,OAAO,IAAI,EAAE;qBAAE;oBACxCD,QAAQsD,WAAWtD,MAAM;oBACzBE,eAAeoD,WAAWpD,aAAa;oBACvCoK,kBAAkBhH,WAAWiH,oBAAoB,GAC7C,cACA7E,iBACE,UACA;gBACR;QAEN,EAAE,OAAO8E,kBAAuB;YAC9B,2DAA2D;YAC3D,gBAAgB;YAChB,IAAIA,oBAAoBA,iBAAiBC,IAAI,KAAK,UAAU;gBAC1D,OAAOD,iBAAiBC,IAAI;YAC9B;YACA,MAAMD;QACR;QAEA,IAAIR,QAAQ,MAAM;YAChB,MAAM,qBAAgC,CAAhC,IAAI9K,MAAM/D,wBAAV,qBAAA;uBAAA;4BAAA;8BAAA;YAA+B;QACvC;QAEA,MAAMsG,cAAc0C,OAAOuG,IAAI,CAACV,MAAM9B,MAAM,CAC1C,CAACyC,MACCA,QAAQ,gBACRA,QAAQ,WACRA,QAAQ,cACRA,QAAQ;QAGZ,IAAIlJ,YAAYsC,QAAQ,CAAC,wBAAwB;YAC/C,MAAM,qBAA2C,CAA3C,IAAI7E,MAAMxD,mCAAV,qBAAA;uBAAA;4BAAA;8BAAA;YAA0C;QAClD;QAEA,IAAI+F,YAAYiB,MAAM,EAAE;YACtB,MAAM,qBAAwD,CAAxD,IAAIxD,MAAMqC,eAAe,kBAAkBE,eAA3C,qBAAA;uBAAA;4BAAA;8BAAA;YAAuD;QAC/D;QAEA,IAAIjD,QAAQC,GAAG,CAACmM,QAAQ,KAAK,cAAc;YACzC,IACE,OAAO,AAACZ,KAAaa,QAAQ,KAAK,eAClC,OAAO,AAACb,KAAalI,QAAQ,KAAK,aAClC;gBACA,MAAM,qBAIL,CAJK,IAAI5C,MACR,CAAC,4DAA4D,EAC3DuG,QAAQ,mBAAmB,qBAC5B,yBAAyB,EAAE/F,SAAS,oFAAoF,CAAC,GAHtH,qBAAA;2BAAA;gCAAA;kCAAA;gBAIN;YACF;QACF;QAEA,IAAI,cAAcsK,QAAQA,KAAKa,QAAQ,EAAE;YACvC,IAAInL,aAAa,QAAQ;gBACvB,MAAM,qBAEL,CAFK,IAAIR,MACR,CAAC,wFAAwF,CAAC,GADtF,qBAAA;2BAAA;gCAAA;kCAAA;gBAEN;YACF;YAEAyE,SAASmH,UAAU,GAAG;QACxB;QAEA,IACE,cAAcd,QACdA,KAAKlI,QAAQ,IACb,OAAOkI,KAAKlI,QAAQ,KAAK,UACzB;YACAD,oBAAoBmI,KAAKlI,QAAQ,EAAcC,KAAK;YAEpD,IAAI2D,gBAAgB;gBAClB,MAAM,qBAGL,CAHK,IAAIxG,MACR,CAAC,0EAA0E,EAAE6C,IAAIY,GAAG,CAAC,GAAG,CAAC,GACvF,CAAC,kFAAkF,CAAC,GAFlF,qBAAA;2BAAA;gCAAA;kCAAA;gBAGN;YACF;;YAEEqH,KAAa1I,KAAK,GAAG;gBACrByJ,cAAcf,KAAKlI,QAAQ,CAACG,WAAW;gBACvC+I,qBAAqBhO,kBAAkBgN,KAAKlI,QAAQ;YACtD;YACA,IAAI,OAAOkI,KAAKlI,QAAQ,CAAC/B,QAAQ,KAAK,aAAa;;gBAC/CiK,KAAa1I,KAAK,CAAC2J,sBAAsB,GAAGjB,KAAKlI,QAAQ,CAAC/B,QAAQ;YACtE;YACA4D,SAASuH,UAAU,GAAG;QACxB;QAEA,IACE,AAAC/H,CAAAA,OAAOuC,cAAa,KACrB,CAAC/B,SAASmH,UAAU,IACpB,CAAC/O,oBAAoB2D,UAAU,kBAAkB,AAACsK,KAAa1I,KAAK,GACpE;YACA,kEAAkE;YAClE,MAAM,qBAEL,CAFK,IAAIpC,MACR,8EADI,qBAAA;uBAAA;4BAAA;8BAAA;YAEN;QACF;QAEA,IAAIoH;QACJ,IAAI,gBAAgB0D,MAAM;YACxB,IAAIA,KAAK1D,UAAU,IAAIhD,WAAWkD,gBAAgB,KAAK,UAAU;gBAC/D,MAAM,qBAEL,CAFK,IAAItH,MACR,+HADI,qBAAA;2BAAA;gCAAA;kCAAA;gBAEN;YACF;YACA,IAAI,OAAO8K,KAAK1D,UAAU,KAAK,UAAU;gBACvC,IAAI,CAAC6E,OAAOC,SAAS,CAACpB,KAAK1D,UAAU,GAAG;oBACtC,MAAM,qBAKL,CALK,IAAIpH,MACR,CAAC,6EAA6E,EAAE6C,IAAIY,GAAG,CAAC,0BAA0B,EAAEqH,KAAK1D,UAAU,CAAC,kBAAkB,CAAC,GACrJ,CAAC,6BAA6B,EAAE+E,KAAKC,IAAI,CACvCtB,KAAK1D,UAAU,EACf,yDAAyD,CAAC,GAJ1D,qBAAA;+BAAA;oCAAA;sCAAA;oBAKN;gBACF,OAAO,IAAI0D,KAAK1D,UAAU,IAAI,GAAG;oBAC/B,MAAM,qBAIL,CAJK,IAAIpH,MACR,CAAC,qEAAqE,EAAE6C,IAAIY,GAAG,CAAC,oHAAoH,CAAC,GACnM,CAAC,2FAA2F,CAAC,GAC7F,CAAC,oEAAoE,CAAC,GAHpE,qBAAA;+BAAA;oCAAA;sCAAA;oBAIN;gBACF,OAAO;oBACL,IAAIqH,KAAK1D,UAAU,GAAG,UAAU;wBAC9B,oDAAoD;wBACpD1H,QAAQP,IAAI,CACV,CAAC,oEAAoE,EAAE0D,IAAIY,GAAG,CAAC,mCAAmC,CAAC,GACjH,CAAC,kHAAkH,CAAC;oBAE1H;oBAEA2D,aAAa0D,KAAK1D,UAAU;gBAC9B;YACF,OAAO,IAAI0D,KAAK1D,UAAU,KAAK,MAAM;gBACnC,qEAAqE;gBACrE,0DAA0D;gBAC1D,yBAAyB;gBACzBA,aAAa;YACf,OAAO,IACL0D,KAAK1D,UAAU,KAAK,SACpB,OAAO0D,KAAK1D,UAAU,KAAK,aAC3B;gBACA,mCAAmC;gBACnCA,aAAa;YACf,OAAO;gBACL,MAAM,qBAIL,CAJK,IAAIpH,MACR,CAAC,8HAA8H,EAAEqM,KAAKC,SAAS,CAC7IxB,KAAK1D,UAAU,EACf,MAAM,EAAEvE,IAAIY,GAAG,EAAE,GAHf,qBAAA;2BAAA;gCAAA;kCAAA;gBAIN;YACF;QACF,OAAO;YACL,mCAAmC;YACnC2D,aAAa;QACf;QAEAhF,MAAMmK,SAAS,GAAGtH,OAAOC,MAAM,CAC7B,CAAC,GACD9C,MAAMmK,SAAS,EACf,WAAWzB,OAAOA,KAAK1I,KAAK,GAAGyF;QAGjC,6CAA6C;QAC7CpD,SAAS+H,YAAY,GAAG;YAAEpF;YAAYC,QAAQQ;QAAU;QACxDpD,SAASgI,QAAQ,GAAGrK;QAEpB,+DAA+D;QAC/D,IAAIqC,SAASmH,UAAU,EAAE;YACvB,OAAO,IAAI7N,aAAa,MAAM;gBAAE0G;YAAS;QAC3C;IACF;IAEA,IAAIiB,oBAAoB;QACtBtD,KAAK,CAAC1F,gBAAgB,GAAG;IAC3B;IAEA,IAAIgJ,sBAAsB,CAAC/E,YAAY;QACrC,IAAImK;QAEJ,IAAI4B,eAAe;QACnB,IAAIC,aAAaxI;QACjB,IAAIyI,kBAAkB;QACtB,IAAItN,QAAQC,GAAG,CAACmM,QAAQ,KAAK,cAAc;YACzCiB,aAAa,IAAIE,MAAsB1I,KAAK;gBAC1C2I,KAAK,SAAUC,GAAG,EAAEC,IAAI;oBACtB,IAAI,CAACN,cAAc;wBACjB,MAAM3M,UACJ,CAAC,8DAA8D,CAAC,GAChE,CAAC,kEAAkE,CAAC;wBAEtE,IAAI6M,iBAAiB;4BACnB,MAAM,qBAAkB,CAAlB,IAAI5M,MAAMD,UAAV,qBAAA;uCAAA;4CAAA;8CAAA;4BAAiB;wBACzB,OAAO;4BACLZ,KAAKY;wBACP;oBACF;oBAEA,IAAI,OAAOiN,SAAS,UAAU;wBAC5B,OAAOjO,eAAe+N,GAAG,CAACC,KAAKC,MAAM7I;oBACvC;oBAEA,OAAOpF,eAAe+N,GAAG,CAACC,KAAKC,MAAM7I;gBACvC;YACF;QACF;QAEA,IAAI;YACF2G,OAAO,MAAMjM,YAAYkM,KAAK,CAC5BjM,WAAW4G,kBAAkB,EAC7B;gBACEsF,UAAU,CAAC,mBAAmB,EAAExK,UAAU;gBAC1CyK,YAAY;oBACV,cAAczK;gBAChB;YACF,GACA,UACEkF,mBAAmB;oBACjB7C,KAAKA;oBAGLsB,KAAKwI;oBACLlM;oBACAwM,aAAa7I,WAAW6I,WAAW;oBACnC,GAAIjG,gBAAgB;wBAAEpB;oBAAO,IAAIiC,SAAS;oBAC1C,GAAIC,gBAAgB,QAChB;wBAAEoD,WAAW;wBAAMC,SAAS;wBAAMrD,aAAaA;oBAAY,IAC3DD,SAAS;oBACb,qDAAqD;oBACrD,6DAA6D;oBAC7D,sDAAsD;oBACtD9G,SAAS;2BAAKqD,WAAWrD,OAAO,IAAI,EAAE;qBAAE;oBACxCD,QAAQsD,WAAWtD,MAAM;oBACzBE,eAAeoD,WAAWpD,aAAa;gBACzC;YAEJ0L,eAAe;YACfjI,SAAS+H,YAAY,GAAG;gBAAEpF,YAAY;gBAAGC,QAAQQ;YAAU;QAC7D,EAAE,OAAOqF,sBAA2B;YAClC,2DAA2D;YAC3D,gBAAgB;YAChB,IACElP,QAAQkP,yBACRA,qBAAqB3B,IAAI,KAAK,UAC9B;gBACA,OAAO2B,qBAAqB3B,IAAI;YAClC;YACA,MAAM2B;QACR;QAEA,IAAIpC,QAAQ,MAAM;YAChB,MAAM,qBAAiC,CAAjC,IAAI9K,MAAM7D,yBAAV,qBAAA;uBAAA;4BAAA;8BAAA;YAAgC;QACxC;QAEA,IAAI,AAAC2O,KAAa1I,KAAK,YAAY+K,SAAS;YAC1CP,kBAAkB;QACpB;QAEA,MAAMrK,cAAc0C,OAAOuG,IAAI,CAACV,MAAM9B,MAAM,CAC1C,CAACyC,MAAQA,QAAQ,WAAWA,QAAQ,cAAcA,QAAQ;QAG5D,IAAI,AAACX,KAAasC,iBAAiB,EAAE;YACnC,MAAM,qBAEL,CAFK,IAAIpN,MACR,CAAC,2FAA2F,EAAEQ,UAAU,GADpG,qBAAA;uBAAA;4BAAA;8BAAA;YAEN;QACF;QACA,IAAI,AAACsK,KAAauC,iBAAiB,EAAE;YACnC,MAAM,qBAEL,CAFK,IAAIrN,MACR,CAAC,2FAA2F,EAAEQ,UAAU,GADpG,qBAAA;uBAAA;4BAAA;8BAAA;YAEN;QACF;QAEA,IAAI+B,YAAYiB,MAAM,EAAE;YACtB,MAAM,qBAA4D,CAA5D,IAAIxD,MAAMqC,eAAe,sBAAsBE,eAA/C,qBAAA;uBAAA;4BAAA;8BAAA;YAA2D;QACnE;QAEA,IAAI,cAAcuI,QAAQA,KAAKa,QAAQ,EAAE;YACvC,IAAInL,aAAa,QAAQ;gBACvB,MAAM,qBAEL,CAFK,IAAIR,MACR,CAAC,wFAAwF,CAAC,GADtF,qBAAA;2BAAA;gCAAA;kCAAA;gBAEN;YACF;YAEAyE,SAASmH,UAAU,GAAG;YACtB,OAAO,IAAI7N,aAAa,MAAM;gBAAE0G;YAAS;QAC3C;QAEA,IAAI,cAAcqG,QAAQ,OAAOA,KAAKlI,QAAQ,KAAK,UAAU;YAC3DD,oBAAoBmI,KAAKlI,QAAQ,EAAcC,KAAK;YAClDiI,KAAa1I,KAAK,GAAG;gBACrByJ,cAAcf,KAAKlI,QAAQ,CAACG,WAAW;gBACvC+I,qBAAqBhO,kBAAkBgN,KAAKlI,QAAQ;YACtD;YACA,IAAI,OAAOkI,KAAKlI,QAAQ,CAAC/B,QAAQ,KAAK,aAAa;;gBAC/CiK,KAAa1I,KAAK,CAAC2J,sBAAsB,GAAGjB,KAAKlI,QAAQ,CAAC/B,QAAQ;YACtE;YACA4D,SAASuH,UAAU,GAAG;QACxB;QAEA,IAAIY,iBAAiB;;YACjB9B,KAAa1I,KAAK,GAAG,MAAM,AAAC0I,KAAa1I,KAAK;QAClD;QAEA,IACE,AAAC6B,CAAAA,OAAOuC,cAAa,KACrB,CAAC3J,oBAAoB2D,UAAU,sBAAsB,AAACsK,KAAa1I,KAAK,GACxE;YACA,kEAAkE;YAClE,MAAM,qBAEL,CAFK,IAAIpC,MACR,kFADI,qBAAA;uBAAA;4BAAA;8BAAA;YAEN;QACF;QAEAoC,MAAMmK,SAAS,GAAGtH,OAAOC,MAAM,CAAC,CAAC,GAAG9C,MAAMmK,SAAS,EAAE,AAACzB,KAAa1I,KAAK;QACxEqC,SAASgI,QAAQ,GAAGrK;IACtB;IAEA,IACE,CAACmE,SAAS,6CAA6C;IACvD,CAACb,sBACDpG,QAAQC,GAAG,CAACmM,QAAQ,KAAK,gBACzBzG,OAAOuG,IAAI,CAACpJ,CAAAA,yBAAAA,MAAOmK,SAAS,KAAI,CAAC,GAAG1H,QAAQ,CAAC,QAC7C;QACAnF,QAAQP,IAAI,CACV,CAAC,iGAAiG,EAAEqB,SAAS,EAAE,CAAC,GAC9G,CAAC,uEAAuE,CAAC;IAE/E;IAEA,0EAA0E;IAC1E,kDAAkD;IAClD,IAAI,AAACmF,qBAAqB,CAACY,SAAU9B,SAASuH,UAAU,EAAE;QACxD,OAAO,IAAIjO,aAAasO,KAAKC,SAAS,CAAClK,QAAQ;YAC7CqC;QACF;IACF;IAEA,sEAAsE;IACtE,gEAAgE;IAChE,IAAI9D,YAAY;QACdyB,MAAMmK,SAAS,GAAG,CAAC;IACrB;IAEA,6DAA6D;IAC7D,IAAIhP,UAAU4G,QAAQ,CAACoC,OAAO,OAAO,IAAIxI,aAAa,MAAM;QAAE0G;IAAS;IAEvE,6DAA6D;IAC7D,qCAAqC;IACrC,IAAI6I,wBAAwBjI;IAC5B,IAAI6B,gBAAgBF,eAAe;QACjC,MAAMuG,OAAO5P,oBAAoBD,kBAAkB8C;QACnD,0EAA0E;QAC1E,sEAAsE;QACtE,UAAU;QACV,IAAI+M,QAAQD,sBAAsBE,KAAK,EAAE;YACvCF,wBAAwB;gBACtB,GAAGA,qBAAqB;gBACxBE,OAAO;oBACL,GAAGF,sBAAsBE,KAAK;oBAC9B,CAACD,KAAK,EAAE;2BACHD,sBAAsBE,KAAK,CAACD,KAAK;2BACjCD,sBAAsBG,gBAAgB,CAACzE,MAAM,CAAC,CAAC0E,IAChDA,EAAE7I,QAAQ,CAAC;qBAEd;gBACH;gBACA4I,kBAAkBH,sBAAsBG,gBAAgB,CAACzE,MAAM,CAC7D,CAAC0E,IAAM,CAACA,EAAE7I,QAAQ,CAAC;YAEvB;QACF;IACF;IAEA,MAAM8I,OAAO,CAAC,EAAEtE,QAAQ,EAA6B;QACnD,OAAOX,YAAYW,yBAAW,KAACuE;YAAIC,IAAG;sBAAUxE;;IAClD;IAEA,MAAMyE,iBAAiB;QACrB,6DAA6D;QAC7D,2DAA2D;QAC3D,oEAAoE;QAEpE,MAAMC,4BAAsD,AAC1D5H,QACD,CAAC1J,sBAAsB;QAExB,IAAI6C,QAAQC,GAAG,CAACC,YAAY,KAAK,UAAU2G,SAASQ,eAAe,EAAE;YACnE,mEAAmE;YACnE,6CAA6C;YAC7C,IAAIoH,2BAA2B;gBAC7B5H,WAAW4H;YACb,OAAO;gBACL,MAAM,qBAEL,CAFK,IAAI/N,MACR,oFADI,qBAAA;2BAAA;gCAAA;kCAAA;gBAEN;YACF;QACF;QAEA,eAAegO,yBACbC,WAGiC;YAEjC,MAAMzD,aAAyB,OAC7B1I,UAA8B,CAAC,CAAC;gBAEhC,IAAIoI,IAAIvG,GAAG,IAAI4B,YAAY;oBACzB,6DAA6D;oBAC7D,IAAI0I,aAAa;wBACfA,YAAYlM,KAAKC;oBACnB;oBAEA,MAAMnC,OAAO,MAAMI,6BACjB,KAAC0N;kCACC,cAAA,KAACpI;;oBAGL,OAAO;wBAAE1F;wBAAM8I;oBAAK;gBACtB;gBAEA,IAAI1E,OAAQ7B,CAAAA,MAAM6F,MAAM,IAAI7F,MAAMJ,SAAS,AAAD,GAAI;oBAC5C,MAAM,qBAEL,CAFK,IAAIhC,MACR,CAAC,sIAAsI,CAAC,GADpI,qBAAA;+BAAA;oCAAA;sCAAA;oBAEN;gBACF;gBAEA,MAAM,EAAE+B,KAAKmM,WAAW,EAAElM,WAAWmM,iBAAiB,EAAE,GACtDtM,kBAAkBC,SAASC,KAAKC;gBAElC,MAAMoM,SAAS,MAAMH,YAAYC,aAAaC;gBAC9C,MAAMC,OAAO/N,QAAQ;gBACrB,MAAMR,OAAO,MAAM5B,eAAemQ;gBAElC,OAAO;oBAAEvO;oBAAM8I;gBAAK;YACtB;YACA,MAAM0F,cAAc;gBAAE,GAAGnE,GAAG;gBAAEM;YAAW;YACzC,MAAM8D,WAAiC,MAAM9Q,oBAC3C2I,UACAkI;YAEF,6DAA6D;YAC7D,IAAI9Q,UAAU4G,QAAQ,CAACoC,OAAO,OAAO;YAErC,IAAI,CAAC+H,YAAY,OAAOA,SAASzO,IAAI,KAAK,UAAU;gBAClD,MAAME,UAAU,CAAC,CAAC,EAAEzC,eAClB6I,UACA,+FAA+F,CAAC;gBAClG,MAAM,qBAAkB,CAAlB,IAAInG,MAAMD,UAAV,qBAAA;2BAAA;gCAAA;kCAAA;gBAAiB;YACzB;YAEA,OAAO;gBAAEuO;gBAAUD;YAAY;QACjC;QAEA,MAAME,gBAAgB,CAACC,MAAeC;YACpC,MAAMP,cAAcM,QAAQzM;YAC5B,MAAMoM,oBAAoBM,cAAczM;YAExC,OAAOkI,IAAIvG,GAAG,IAAI4B,2BAChB,KAACoI;0BACC,cAAA,KAACpI;+BAGH,KAACoI;0BACC,cAAA,KAAC1D;8BACE9H,eAAe+L,aAAaC,mBAAmB;wBAC9C,GAAG/L,KAAK;wBACR6F;oBACF;;;QAIR;QAEA,gFAAgF;QAChF,MAAMgG,cAAc,OAClBC,aACAC;YAEA,MAAMO,UAAUH,cAAcL,aAAaC;YAC3C,OAAO,MAAMjQ,0BAA0B;gBACrCyQ,gBAAgB7S;gBAChBoE,SAASwO;YACX;QACF;QAEA,MAAME,6BACJtP,QAAQC,GAAG,CAACC,YAAY,KAAK,UAAU,CAAC,CAAC2G,SAASQ,eAAe;QAEnE,uEAAuE;QACvE,gCAAgC;QAChC,IAAIkI;QAIJ,MAAM,CAACC,0BAA0BJ,QAAQ,GAAG,MAAMvB,QAAQ4B,GAAG,CAAC;YAC5D9O,eAAe2K;YACd,CAAA;gBACC,IAAIgE,4BAA4B;oBAC9BC,0BAA0B,MAAMb,yBAAyBC;oBACzD,IAAIY,4BAA4B,MAAM,OAAO;oBAC7C,MAAM,EAAEP,QAAQ,EAAE,GAAGO;oBACrB,OAAOP,SAASzO,IAAI;gBACtB,OAAO;oBACLgP,0BAA0B,CAAC;oBAC3B,MAAMT,SAAS,MAAMH,YAAYlM,KAAKC;oBACtC,MAAMoM,OAAO/N,QAAQ;oBACrB,OAAOpC,eAAemQ;gBACxB;YACF,CAAA;SACD;QAED,IAAIM,YAAY,MAAM;YACpB,OAAO;QACT;QAEA,MAAMM,cAAcF,2BAA2BJ;QAE/C,6CAA6C;QAC7C,MAAM,EAAEJ,QAAQ,EAAE,GAAG,AAACO,2BAAmC,CAAC;QAC1D,MAAMI,kBAAkB,CAACC;YACvB,IAAI5P,QAAQC,GAAG,CAACC,YAAY,KAAK,QAAQ;gBACvC,OAAO,AAAC2G;YACV,OAAO;gBACL,qBAAO,KAACA;oBAAU,GAAG+I,SAAS;oBAAG,GAAGZ,QAAQ;;YAC9C;QACF;QAEA,IAAI7D;QACJ,IAAImE,4BAA4B;YAC9BnE,SAAS6D,SAAS7D,MAAM;YACxB9B,OAAO2F,SAAS3F,IAAI;QACtB,OAAO;YACL8B,SAASrC,iBAAiBqC,MAAM;YAChCrC,iBAAiBuC,KAAK;QACxB;QAEA,OAAO;YACLqE;YACAC;YACAtG;YACAwG,UAAU,EAAE;YACZ1E;QACF;IACF;IAEA5L,YAAYuQ,oBAAoB,CAAC,cAAchL,WAAWmJ,IAAI;IAC9D,MAAM8B,iBAAiB,MAAMxQ,YAAYkM,KAAK,CAC5CjM,WAAWgP,cAAc,EACzB;QACE9C,UAAU,CAAC,qBAAqB,EAAE5G,WAAWmJ,IAAI,EAAE;QACnDtC,YAAY;YACV,cAAc7G,WAAWmJ,IAAI;QAC/B;IACF,GACA,UAAYO;IAEd,IAAI,CAACuB,gBAAgB;QACnB,OAAO,IAAItR,aAAa,MAAM;YAAE0G;QAAS;IAC3C;IAEA,MAAM6K,oBAAoB,IAAIzF;IAC9B,MAAM0F,iBAAiB,IAAI1F;IAE3B,KAAK,MAAM2F,OAAO5G,qBAAsB;QACtC,MAAM6G,eAAenK,qBAAqB,CAACkK,IAAI;QAE/C,IAAIC,cAAc;YAChBH,kBAAkBI,GAAG,CAACD,aAAa5B,EAAE;YACrC4B,aAAaE,KAAK,CAACC,OAAO,CAAC,CAACC;gBAC1BN,eAAeG,GAAG,CAACG;YACrB;QACF;IACF;IAEA,MAAMC,YAAYzH,SAASI,MAAM;IACjC,MAAMsH,wBAAgE,CAAC;IAEvE,MAAM,EACJC,WAAW,EACXhP,aAAa,EACbiP,uBAAuB,EACvBhP,aAAa,EACbH,MAAM,EACNC,OAAO,EACPmP,aAAa,EACd,GAAG9L;IACJ,MAAM8K,YAAuB;QAC3BiB,eAAe;YACb/N;YACAmL,MAAM/M;YACNC;YACA2P,SAAS9L,cAAc8L,OAAO;YAC9BJ,aAAaA,gBAAgB,KAAKnI,YAAYmI;YAC9CE;YACAzJ,YAAYA,eAAe,OAAO,OAAOoB;YACzCwI,YAAYnJ,iBAAiB,OAAO,OAAOW;YAC3ClH;YACAsF;YACAqK,YACEhB,kBAAkBiB,IAAI,KAAK,IACvB1I,YACA2I,MAAMC,IAAI,CAACnB;YACjB3L,KAAKS,WAAWT,GAAG,GAAGK,eAAeC,KAAKG,WAAWT,GAAG,IAAIkE;YAC5D6I,KAAK,CAAC,CAAClL,iBAAiB,OAAOqC;YAC/B8I,MAAM,CAAC,CAACjL,qBAAqB,OAAOmC;YACpC+I,cAActM,cAAcsM,YAAY;YACxCC,KAAKhK,yBAAyB,OAAOgB;YACrCiJ,QAAQ,CAACpK,4BAA4B,OAAOmB;YAC5C/G;YACAC;YACAC;YACAC;YACAC,WAAWA,cAAc,OAAO,OAAO2G;YACvCxB,iBAAiBA,mBAAmBpC,MAAMoC,kBAAkBwB;QAC9D;QACAkJ,gBAAgB3M,WAAW2M,cAAc;QACzC1L,eAAeiI;QACfyC;QACAiB,iBAAiB/I,OAAO3G,MAAM;QAC9B2P,eACE,CAAC7M,WAAWe,OAAO,IAAIvH,eAAeiF,KAAK,oBACvC,GAAGuB,WAAW6M,aAAa,IAAI,GAAG,CAAC,EAAE7M,WAAWtD,MAAM,EAAE,GACxDsD,WAAW6M,aAAa;QAC9B9L;QACAuD;QACAwI,eAAe,CAAC,CAACjN;QACjB6L;QACAP,gBAAgBiB,MAAMC,IAAI,CAAClB;QAC3B4B,oBAAoB,IAAItH,IAAIzF,WAAW+M,kBAAkB,IAAI,EAAE;QAC/DnB;QACA,2GAA2G;QAC3GoB,oBACE9R,QAAQC,GAAG,CAACmM,QAAQ,KAAK,eACrBtG,WAAWgM,kBAAkB,GAC7BvJ;QACNwJ,oBAAoBjM,WAAWiM,kBAAkB;QACjD3M;QACAyD;QACArH;QACAmP;QACAtH,MAAM0G,eAAe1G,IAAI;QACzBwG,UAAUE,eAAeF,QAAQ;QACjC1E,QAAQ4E,eAAe5E,MAAM;QAC7B6G,aAAalN,WAAWkN,WAAW;QACnCC,aAAanN,WAAWmN,WAAW;QACnCjK,kBAAkBlD,WAAWkD,gBAAgB;QAC7CkK,mBAAmBpN,WAAWoN,iBAAiB;QAC/CzL,SAASC;QACTyL,oBAAoBrN,WAAWqN,kBAAkB;QACjDC,kBAAkBtN,WAAWsN,gBAAgB;QAC7CC,iCACEvN,WAAWwN,YAAY,CAACC,mBAAmB;IAC/C;IAEA,MAAMC,yBACJ,KAAC/U,gBAAgBuM,QAAQ;QAACC,OAAOlB;kBAC/B,cAAA,KAAC5K,YAAY6L,QAAQ;YAACC,OAAO2F;sBAC1BG,eAAeJ,eAAe,CAACC;;;IAKtC,MAAM6C,eAAe,MAAMlT,YAAYkM,KAAK,CAC1CjM,WAAWmB,cAAc,EACzB,UAAYA,eAAe6R;IAG7B,IAAIxS,QAAQC,GAAG,CAACmM,QAAQ,KAAK,cAAc;QACzC,MAAMsG,wBAAwB,EAAE;QAChC,MAAMC,wBAAwB;YAAC;YAAQ;YAAQ;YAAc;SAAO;QAEpE,KAAK,MAAMC,QAAQD,sBAAuB;YACxC,IAAI,CAAC,AAAClC,qBAA6B,CAACmC,KAAK,EAAE;gBACzCF,sBAAsBzQ,IAAI,CAAC2Q;YAC7B;QACF;QAEA,IAAIF,sBAAsBxO,MAAM,EAAE;YAChC,MAAM2O,uBAAuBH,sBAC1B7I,GAAG,CAAC,CAACiJ,IAAM,CAAC,CAAC,EAAEA,EAAE,GAAG,CAAC,EACrB1P,IAAI,CAAC;YACR,MAAM2P,SAASL,sBAAsBxO,MAAM,KAAK,IAAI,MAAM;YAC1D9D,QAAQP,IAAI,CACV,CAAC,mFAAmF,EAAEkT,OAAO,GAAG,CAAC,GAC/F,CAAC,iBAAiB,EAAEA,OAAO,EAAE,EAAEF,qBAAqB,EAAE,CAAC,GACvD;QAEN;IACF;IAEA,MAAM,CAACG,oBAAoBC,mBAAmB,GAAGR,aAAaS,KAAK,CACjE,+EACA;IAGF,IAAIC,SAAS;IACb,IAAI,CAACV,aAAaW,UAAU,CAACrT,UAAU;QACrCoT,UAAUpT;IACZ;IACAoT,UAAUH;IACV,IAAI5J,WAAW;QACb+J,UAAU;IACZ;IAEA,MAAM/D,UAAU+D,SAASpD,eAAeL,WAAW,GAAGuD;IAEtD,MAAMI,gBAAgB,MAAMvT,gBAAgBoB,UAAUkO,SAAStK,YAAY;QACzEsE;QACAoH;IACF;IAEA,OAAO,IAAI/R,aAAa4U,eAAe;QAAElO;IAAS;AACpD;AAYA,OAAO,MAAMmO,eAA4B,CACvC/P,KACAsB,KACA3D,UACAC,OACA2D,YACAE,eACAC;IAEA,OAAOL,iBACLrB,KACAsB,KACA3D,UACAC,OACA2D,YACAA,YACAE,eACAC;AAEJ,EAAC"}