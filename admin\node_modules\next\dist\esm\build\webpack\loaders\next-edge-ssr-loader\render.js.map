{"version": 3, "sources": ["../../../../../src/build/webpack/loaders/next-edge-ssr-loader/render.ts"], "sourcesContent": ["import type { NextConfigComplete } from '../../../../server/config-shared'\n\nimport type { DocumentType, AppType } from '../../../../shared/lib/utils'\nimport type { BuildManifest } from '../../../../server/get-page-files'\nimport type {\n  DynamicCssManifest,\n  ReactLoadableManifest,\n} from '../../../../server/load-components'\nimport type { ClientReferenceManifest } from '../../plugins/flight-manifest-plugin'\nimport type { NextFontManifest } from '../../plugins/next-font-manifest-plugin'\nimport type { NextFetchEvent } from '../../../../server/web/spec-extension/fetch-event'\n\nimport WebServer from '../../../../server/web-server'\nimport {\n  WebNextRequest,\n  WebNextResponse,\n} from '../../../../server/base-http/web'\nimport { SERVER_RUNTIME } from '../../../../lib/constants'\nimport type { ManifestRewriteRoute } from '../../..'\nimport { normalizeAppPath } from '../../../../shared/lib/router/utils/app-paths'\nimport type { SizeLimit } from '../../../../types'\nimport { internal_getCurrentFunctionWaitUntil } from '../../../../server/web/internal-edge-wait-until'\nimport type { PAGE_TYPES } from '../../../../lib/page-types'\nimport type { NextRequestHint } from '../../../../server/web/adapter'\n\nexport function getRender({\n  dev,\n  page,\n  appMod,\n  pageMod,\n  errorMod,\n  error500Mod,\n  pagesType,\n  Document,\n  buildManifest,\n  reactLoadableManifest,\n  dynamicCssManifest,\n  interceptionRouteRewrites,\n  renderToHTML,\n  clientReferenceManifest,\n  subresourceIntegrityManifest,\n  serverActionsManifest,\n  serverActions,\n  config,\n  buildId,\n  nextFontManifest,\n  incrementalCacheHandler,\n}: {\n  pagesType: PAGE_TYPES\n  dev: boolean\n  page: string\n  appMod: any\n  pageMod: any\n  errorMod: any\n  error500Mod: any\n  renderToHTML?: any\n  Document: DocumentType\n  buildManifest: BuildManifest\n  reactLoadableManifest: ReactLoadableManifest\n  dynamicCssManifest?: DynamicCssManifest\n  subresourceIntegrityManifest?: Record<string, string>\n  interceptionRouteRewrites?: ManifestRewriteRoute[]\n  clientReferenceManifest?: ClientReferenceManifest\n  serverActionsManifest?: any\n  serverActions?: {\n    bodySizeLimit?: SizeLimit\n    allowedOrigins?: string[]\n  }\n  config: NextConfigComplete\n  buildId: string\n  nextFontManifest: NextFontManifest\n  incrementalCacheHandler?: any\n}) {\n  const isAppPath = pagesType === 'app'\n  const baseLoadComponentResult = {\n    dev,\n    buildManifest,\n    reactLoadableManifest,\n    dynamicCssManifest,\n    subresourceIntegrityManifest,\n    Document,\n    App: appMod?.default as AppType,\n    clientReferenceManifest,\n  }\n\n  const server = new WebServer({\n    dev,\n    buildId,\n    conf: config,\n    minimalMode: true,\n    webServerConfig: {\n      page,\n      pathname: isAppPath ? normalizeAppPath(page) : page,\n      pagesType,\n      interceptionRouteRewrites,\n      extendRenderOpts: {\n        runtime: SERVER_RUNTIME.experimentalEdge,\n        supportsDynamicResponse: true,\n        disableOptimizedLoading: true,\n        serverActionsManifest,\n        serverActions,\n        nextFontManifest,\n      },\n      renderToHTML,\n      incrementalCacheHandler,\n      loadComponent: async (inputPage) => {\n        if (inputPage === page) {\n          return {\n            ...baseLoadComponentResult,\n            Component: pageMod.default,\n            pageConfig: pageMod.config || {},\n            getStaticProps: pageMod.getStaticProps,\n            getServerSideProps: pageMod.getServerSideProps,\n            getStaticPaths: pageMod.getStaticPaths,\n            ComponentMod: pageMod,\n            isAppPath: !!pageMod.__next_app__,\n            page: inputPage,\n            routeModule: pageMod.routeModule,\n          }\n        }\n\n        // If there is a custom 500 page, we need to handle it separately.\n        if (inputPage === '/500' && error500Mod) {\n          return {\n            ...baseLoadComponentResult,\n            Component: error500Mod.default,\n            pageConfig: error500Mod.config || {},\n            getStaticProps: error500Mod.getStaticProps,\n            getServerSideProps: error500Mod.getServerSideProps,\n            getStaticPaths: error500Mod.getStaticPaths,\n            ComponentMod: error500Mod,\n            page: inputPage,\n            routeModule: error500Mod.routeModule,\n          }\n        }\n\n        if (inputPage === '/_error') {\n          return {\n            ...baseLoadComponentResult,\n            Component: errorMod.default,\n            pageConfig: errorMod.config || {},\n            getStaticProps: errorMod.getStaticProps,\n            getServerSideProps: errorMod.getServerSideProps,\n            getStaticPaths: errorMod.getStaticPaths,\n            ComponentMod: errorMod,\n            page: inputPage,\n            routeModule: errorMod.routeModule,\n          }\n        }\n\n        return null\n      },\n    },\n  })\n\n  const handler = server.getRequestHandler()\n\n  return async function render(\n    request: NextRequestHint,\n    event?: NextFetchEvent\n  ) {\n    const extendedReq = new WebNextRequest(request)\n    const extendedRes = new WebNextResponse(undefined)\n\n    handler(extendedReq, extendedRes)\n    const result = await extendedRes.toResponse()\n    request.fetchMetrics = extendedReq.fetchMetrics\n\n    if (event?.waitUntil) {\n      // TODO(after):\n      // remove `internal_runWithWaitUntil` and the `internal-edge-wait-until` module\n      // when consumers switch to `after`.\n      const waitUntilPromise = internal_getCurrentFunctionWaitUntil()\n      if (waitUntilPromise) {\n        event.waitUntil(waitUntilPromise)\n      }\n    }\n\n    return result\n  }\n}\n"], "names": ["WebServer", "WebNextRequest", "WebNextResponse", "SERVER_RUNTIME", "normalizeAppPath", "internal_getCurrentFunctionWaitUntil", "getRender", "dev", "page", "appMod", "pageMod", "errorMod", "error500Mod", "pagesType", "Document", "buildManifest", "reactLoadableManifest", "dynamicCssManifest", "interceptionRouteRewrites", "renderToHTML", "clientReferenceManifest", "subresourceIntegrityManifest", "serverActionsManifest", "serverActions", "config", "buildId", "nextFontManifest", "incremental<PERSON>ache<PERSON><PERSON><PERSON>", "isAppPath", "baseLoadComponentResult", "App", "default", "server", "conf", "minimalMode", "webServerConfig", "pathname", "extendRenderOpts", "runtime", "experimentalEdge", "supportsDynamicResponse", "disableOptimizedLoading", "loadComponent", "inputPage", "Component", "pageConfig", "getStaticProps", "getServerSideProps", "getStaticPaths", "ComponentMod", "__next_app__", "routeModule", "handler", "getRequestHandler", "render", "request", "event", "extendedReq", "extendedRes", "undefined", "result", "toResponse", "fetchMetrics", "waitUntil", "waitUntilPromise"], "mappings": "AAYA,OAAOA,eAAe,gCAA+B;AACrD,SACEC,cAAc,EACdC,eAAe,QACV,mCAAkC;AACzC,SAASC,cAAc,QAAQ,4BAA2B;AAE1D,SAASC,gBAAgB,QAAQ,gDAA+C;AAEhF,SAASC,oCAAoC,QAAQ,kDAAiD;AAItG,OAAO,SAASC,UAAU,EACxBC,GAAG,EACHC,IAAI,EACJC,MAAM,EACNC,OAAO,EACPC,QAAQ,EACRC,WAAW,EACXC,SAAS,EACTC,QAAQ,EACRC,aAAa,EACbC,qBAAqB,EACrBC,kBAAkB,EAClBC,yBAAyB,EACzBC,YAAY,EACZC,uBAAuB,EACvBC,4BAA4B,EAC5BC,qBAAqB,EACrBC,aAAa,EACbC,MAAM,EACNC,OAAO,EACPC,gBAAgB,EAChBC,uBAAuB,EA0BxB;IACC,MAAMC,YAAYf,cAAc;IAChC,MAAMgB,0BAA0B;QAC9BtB;QACAQ;QACAC;QACAC;QACAI;QACAP;QACAgB,GAAG,EAAErB,0BAAAA,OAAQsB,OAAO;QACpBX;IACF;IAEA,MAAMY,SAAS,IAAIhC,UAAU;QAC3BO;QACAkB;QACAQ,MAAMT;QACNU,aAAa;QACbC,iBAAiB;YACf3B;YACA4B,UAAUR,YAAYxB,iBAAiBI,QAAQA;YAC/CK;YACAK;YACAmB,kBAAkB;gBAChBC,SAASnC,eAAeoC,gBAAgB;gBACxCC,yBAAyB;gBACzBC,yBAAyB;gBACzBnB;gBACAC;gBACAG;YACF;YACAP;YACAQ;YACAe,eAAe,OAAOC;gBACpB,IAAIA,cAAcnC,MAAM;oBACtB,OAAO;wBACL,GAAGqB,uBAAuB;wBAC1Be,WAAWlC,QAAQqB,OAAO;wBAC1Bc,YAAYnC,QAAQc,MAAM,IAAI,CAAC;wBAC/BsB,gBAAgBpC,QAAQoC,cAAc;wBACtCC,oBAAoBrC,QAAQqC,kBAAkB;wBAC9CC,gBAAgBtC,QAAQsC,cAAc;wBACtCC,cAAcvC;wBACdkB,WAAW,CAAC,CAAClB,QAAQwC,YAAY;wBACjC1C,MAAMmC;wBACNQ,aAAazC,QAAQyC,WAAW;oBAClC;gBACF;gBAEA,kEAAkE;gBAClE,IAAIR,cAAc,UAAU/B,aAAa;oBACvC,OAAO;wBACL,GAAGiB,uBAAuB;wBAC1Be,WAAWhC,YAAYmB,OAAO;wBAC9Bc,YAAYjC,YAAYY,MAAM,IAAI,CAAC;wBACnCsB,gBAAgBlC,YAAYkC,cAAc;wBAC1CC,oBAAoBnC,YAAYmC,kBAAkB;wBAClDC,gBAAgBpC,YAAYoC,cAAc;wBAC1CC,cAAcrC;wBACdJ,MAAMmC;wBACNQ,aAAavC,YAAYuC,WAAW;oBACtC;gBACF;gBAEA,IAAIR,cAAc,WAAW;oBAC3B,OAAO;wBACL,GAAGd,uBAAuB;wBAC1Be,WAAWjC,SAASoB,OAAO;wBAC3Bc,YAAYlC,SAASa,MAAM,IAAI,CAAC;wBAChCsB,gBAAgBnC,SAASmC,cAAc;wBACvCC,oBAAoBpC,SAASoC,kBAAkB;wBAC/CC,gBAAgBrC,SAASqC,cAAc;wBACvCC,cAActC;wBACdH,MAAMmC;wBACNQ,aAAaxC,SAASwC,WAAW;oBACnC;gBACF;gBAEA,OAAO;YACT;QACF;IACF;IAEA,MAAMC,UAAUpB,OAAOqB,iBAAiB;IAExC,OAAO,eAAeC,OACpBC,OAAwB,EACxBC,KAAsB;QAEtB,MAAMC,cAAc,IAAIxD,eAAesD;QACvC,MAAMG,cAAc,IAAIxD,gBAAgByD;QAExCP,QAAQK,aAAaC;QACrB,MAAME,SAAS,MAAMF,YAAYG,UAAU;QAC3CN,QAAQO,YAAY,GAAGL,YAAYK,YAAY;QAE/C,IAAIN,yBAAAA,MAAOO,SAAS,EAAE;YACpB,eAAe;YACf,+EAA+E;YAC/E,oCAAoC;YACpC,MAAMC,mBAAmB3D;YACzB,IAAI2D,kBAAkB;gBACpBR,MAAMO,SAAS,CAACC;YAClB;QACF;QAEA,OAAOJ;IACT;AACF"}