// Legacy schemas - DEPRECATED
// Use schemas from @/lib/validation/common-validations instead

import * as z from 'zod';

// Re-export new schemas for backward compatibility (non-conflicting ones)
export {
    LoginSchema as loginSchema,
    MunicipioSchema as municipioSchema,
    TurismoExperienciaSchema as turismoExperienciaSchema,
    SaboresCulturaSchema as saboresCulturaSchema,
    NewsletterSchema as newsletterSchema,

    // Types
    type LoginFormData as LoginForm,
    type MunicipioFormData as MunicipioForm,
    type TurismoExperienciaFormData as TurismoExperienciaForm,
    type SaboresCulturaFormData as SaboresCulturaForm,
    type NewsletterFormData as NewsletterForm,
} from '@/lib/validation/common-validations';

// Legacy local schemas - keeping for backward compatibility with existing code
// These should eventually be migrated to use the schemas from common-validations

export const usuarioSchemaLegacy = z.object({
    id: z.string().optional(),
    nome: z.string().min(1, 'Nome é obrigatório'),
    email: z.string().min(1, 'Email é obrigatório').email('Email inválido'),
    senha: z.string().min(1, 'Senha é obrigatória')
})

// Schema para edição onde a senha é opcional
export const usuarioEditSchemaLegacy = z.object({
    id: z.string().optional(),
    nome: z.string().min(1, 'Nome é obrigatório'),
    email: z.string().min(1, 'Email é obrigatório').email('Email inválido'),
    senha: z.string().optional().or(z.literal(''))
})

export const eventoSchemaLegacy = z.object({
    id: z.string().optional(),
    nome: z.string().min(1, 'Nome é obrigatório'),
    descricao: z.string().min(1, 'Descrição é obrigatória'),
    data: z
        .string()
        .min(1, 'Data é obrigatória')
        .refine(date => {
            const inputDate = new Date(date);
            if (isNaN(inputDate.getTime())) return false;
            const today = new Date();
            today.setHours(0, 0, 0, 0);
            return inputDate >= today;
        }, { message: 'Data deve ser futura ou igual ao dia atual' }),
    horaInicio: z.string().min(1, 'Hora de início é obrigatória'),
    horaFim: z.string().min(1, 'Hora de fim é obrigatória'),
    local: z.string().min(1, 'Local é obrigatório'),
    imagem: z.instanceof(File, { message: 'Imagem é obrigatória' }).optional(),
    categoria: z.string().min(1, 'Categoria é obrigatória'),
    destaque: z.boolean(),
    municipioId: z.string().min(1, 'ID de município é obrigatório').uuid('ID de município inválido')
});

// Export legacy schemas with their original names for backward compatibility
export const usuarioSchema = usuarioSchemaLegacy;
export const usuarioEditSchema = usuarioEditSchemaLegacy;
export const eventoSchema = eventoSchemaLegacy;

// Legacy types - use the re-exported types from common-validations instead
export type EventoFormLegacy = z.infer<typeof eventoSchemaLegacy>;
export type UsuarioFormLegacy = z.infer<typeof usuarioSchemaLegacy>;
export type UsuarioEditFormLegacy = z.infer<typeof usuarioEditSchemaLegacy>;
export type UsuarioFormUnion = UsuarioFormLegacy | UsuarioEditFormLegacy;

// Backward compatibility aliases - these point to the re-exported types above
export type EventoForm = EventoFormLegacy;
export type UsuarioForm = UsuarioFormLegacy;
export type UsuarioEditForm = UsuarioEditFormLegacy;
