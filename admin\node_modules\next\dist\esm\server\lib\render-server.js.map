{"version": 3, "sources": ["../../../src/server/lib/render-server.ts"], "sourcesContent": ["import type { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, UpgradeHandler } from '../next'\nimport type { DevBundlerService } from './dev-bundler-service'\nimport type { PropagateToWorkersField } from './router-utils/types'\n\nimport next from '../next'\nimport type { Span } from '../../trace'\n\nexport type ServerInitResult = {\n  requestHandler: RequestHandler\n  upgradeHandler: UpgradeHandler\n  server: NextServer\n  // Make an effort to close upgraded HTTP requests (e.g. Turbopack HMR websockets)\n  closeUpgraded: () => void\n}\n\nlet initializations: Record<string, Promise<ServerInitResult> | undefined> = {}\n\nlet sandboxContext: undefined | typeof import('../web/sandbox/context')\n\nif (process.env.NODE_ENV !== 'production') {\n  sandboxContext = require('../web/sandbox/context')\n}\n\nexport function clearAllModuleContexts() {\n  return sandboxContext?.clearAllModuleContexts()\n}\n\nexport function clearModuleContext(target: string) {\n  return sandboxContext?.clearModuleContext(target)\n}\n\nexport async function getServerField(\n  dir: string,\n  field: PropagateToWorkersField\n) {\n  const initialization = await initializations[dir]\n  if (!initialization) {\n    throw new Error('Invariant cant propagate server field, no app initialized')\n  }\n  const { server } = initialization\n  let wrappedServer = server['server']! // NextServer.server is private\n  return wrappedServer[field as keyof typeof wrappedServer]\n}\n\nexport async function propagateServerField(\n  dir: string,\n  field: PropagateToWorkersField,\n  value: any\n) {\n  const initialization = await initializations[dir]\n  if (!initialization) {\n    throw new Error('Invariant cant propagate server field, no app initialized')\n  }\n  const { server } = initialization\n  let wrappedServer = server['server']\n  const _field = field as keyof NonNullable<typeof wrappedServer>\n\n  if (wrappedServer) {\n    if (typeof wrappedServer[_field] === 'function') {\n      // @ts-expect-error\n      await wrappedServer[_field].apply(\n        wrappedServer,\n        Array.isArray(value) ? value : []\n      )\n    } else {\n      // @ts-expect-error\n      wrappedServer[_field] = value\n    }\n  }\n}\n\nasync function initializeImpl(opts: {\n  dir: string\n  port: number\n  dev: boolean\n  minimalMode?: boolean\n  hostname?: string\n  keepAliveTimeout?: number\n  serverFields?: any\n  server?: any\n  experimentalTestProxy: boolean\n  experimentalHttpsServer: boolean\n  _ipcPort?: string\n  _ipcKey?: string\n  bundlerService: DevBundlerService | undefined\n  startServerSpan: Span | undefined\n  quiet?: boolean\n  onDevServerCleanup: ((listener: () => Promise<void>) => void) | undefined\n}): Promise<ServerInitResult> {\n  const type = process.env.__NEXT_PRIVATE_RENDER_WORKER\n  if (type) {\n    process.title = 'next-render-worker-' + type\n  }\n\n  let requestHandler: RequestHandler\n  let upgradeHandler: UpgradeHandler\n\n  const server = next({\n    ...opts,\n    hostname: opts.hostname || 'localhost',\n    customServer: false,\n    httpServer: opts.server,\n    port: opts.port,\n  }) as NextServer // should return a NextServer when `customServer: false`\n  requestHandler = server.getRequestHandler()\n  upgradeHandler = server.getUpgradeHandler()\n\n  await server.prepare(opts.serverFields)\n\n  return {\n    requestHandler,\n    upgradeHandler,\n    server,\n    closeUpgraded() {\n      opts.bundlerService?.close()\n    },\n  }\n}\n\nexport async function initialize(\n  opts: Parameters<typeof initializeImpl>[0]\n): Promise<ServerInitResult> {\n  // if we already setup the server return as we only need to do\n  // this on first worker boot\n  if (initializations[opts.dir]) {\n    return initializations[opts.dir]!\n  }\n  return (initializations[opts.dir] = initializeImpl(opts))\n}\n"], "names": ["next", "initializations", "sandboxContext", "process", "env", "NODE_ENV", "require", "clearAllModuleContexts", "clearModuleContext", "target", "getServerField", "dir", "field", "initialization", "Error", "server", "wrappedServer", "propagateServerField", "value", "_field", "apply", "Array", "isArray", "initializeImpl", "opts", "type", "__NEXT_PRIVATE_RENDER_WORKER", "title", "requestHandler", "upgradeHandler", "hostname", "customServer", "httpServer", "port", "getRequestHandler", "getUpgradeHandler", "prepare", "serverFields", "closeUpgraded", "bundlerService", "close", "initialize"], "mappings": "AAIA,OAAOA,UAAU,UAAS;AAW1B,IAAIC,kBAAyE,CAAC;AAE9E,IAAIC;AAEJ,IAAIC,QAAQC,GAAG,CAACC,QAAQ,KAAK,cAAc;IACzCH,iBAAiBI,QAAQ;AAC3B;AAEA,OAAO,SAASC;IACd,OAAOL,kCAAAA,eAAgBK,sBAAsB;AAC/C;AAEA,OAAO,SAASC,mBAAmBC,MAAc;IAC/C,OAAOP,kCAAAA,eAAgBM,kBAAkB,CAACC;AAC5C;AAEA,OAAO,eAAeC,eACpBC,GAAW,EACXC,KAA8B;IAE9B,MAAMC,iBAAiB,MAAMZ,eAAe,CAACU,IAAI;IACjD,IAAI,CAACE,gBAAgB;QACnB,MAAM,qBAAsE,CAAtE,IAAIC,MAAM,8DAAV,qBAAA;mBAAA;wBAAA;0BAAA;QAAqE;IAC7E;IACA,MAAM,EAAEC,MAAM,EAAE,GAAGF;IACnB,IAAIG,gBAAgBD,MAAM,CAAC,SAAS,AAAE,+BAA+B;;IACrE,OAAOC,aAAa,CAACJ,MAAoC;AAC3D;AAEA,OAAO,eAAeK,qBACpBN,GAAW,EACXC,KAA8B,EAC9BM,KAAU;IAEV,MAAML,iBAAiB,MAAMZ,eAAe,CAACU,IAAI;IACjD,IAAI,CAACE,gBAAgB;QACnB,MAAM,qBAAsE,CAAtE,IAAIC,MAAM,8DAAV,qBAAA;mBAAA;wBAAA;0BAAA;QAAqE;IAC7E;IACA,MAAM,EAAEC,MAAM,EAAE,GAAGF;IACnB,IAAIG,gBAAgBD,MAAM,CAAC,SAAS;IACpC,MAAMI,SAASP;IAEf,IAAII,eAAe;QACjB,IAAI,OAAOA,aAAa,CAACG,OAAO,KAAK,YAAY;YAC/C,mBAAmB;YACnB,MAAMH,aAAa,CAACG,OAAO,CAACC,KAAK,CAC/BJ,eACAK,MAAMC,OAAO,CAACJ,SAASA,QAAQ,EAAE;QAErC,OAAO;YACL,mBAAmB;YACnBF,aAAa,CAACG,OAAO,GAAGD;QAC1B;IACF;AACF;AAEA,eAAeK,eAAeC,IAiB7B;IACC,MAAMC,OAAOtB,QAAQC,GAAG,CAACsB,4BAA4B;IACrD,IAAID,MAAM;QACRtB,QAAQwB,KAAK,GAAG,wBAAwBF;IAC1C;IAEA,IAAIG;IACJ,IAAIC;IAEJ,MAAMd,SAASf,KAAK;QAClB,GAAGwB,IAAI;QACPM,UAAUN,KAAKM,QAAQ,IAAI;QAC3BC,cAAc;QACdC,YAAYR,KAAKT,MAAM;QACvBkB,MAAMT,KAAKS,IAAI;IACjB,EAAiB,wDAAwD;;IACzEL,iBAAiBb,OAAOmB,iBAAiB;IACzCL,iBAAiBd,OAAOoB,iBAAiB;IAEzC,MAAMpB,OAAOqB,OAAO,CAACZ,KAAKa,YAAY;IAEtC,OAAO;QACLT;QACAC;QACAd;QACAuB;gBACEd;aAAAA,uBAAAA,KAAKe,cAAc,qBAAnBf,qBAAqBgB,KAAK;QAC5B;IACF;AACF;AAEA,OAAO,eAAeC,WACpBjB,IAA0C;IAE1C,8DAA8D;IAC9D,4BAA4B;IAC5B,IAAIvB,eAAe,CAACuB,KAAKb,GAAG,CAAC,EAAE;QAC7B,OAAOV,eAAe,CAACuB,KAAKb,GAAG,CAAC;IAClC;IACA,OAAQV,eAAe,CAACuB,KAAKb,GAAG,CAAC,GAAGY,eAAeC;AACrD"}