{"version": 3, "sources": ["../../../../../../src/client/components/react-dev-overlay/ui/icons/system-icon.tsx"], "sourcesContent": ["export default function SystemIcon() {\n  return (\n    <svg width=\"16\" height=\"16\" strokeLinejoin=\"round\">\n      <path\n        fill=\"currentColor\"\n        fillRule=\"evenodd\"\n        d=\"M0 2a1 1 0 0 1 1-1h14a1 1 0 0 1 1 1v8.5a1 1 0 0 1-1 1H8.75v3h1.75V16h-5v-1.5h1.75v-3H1a1 1 0 0 1-1-1V2Zm1.5.5V10h13V2.5h-13Z\"\n        clipRule=\"evenodd\"\n      />\n    </svg>\n  )\n}\n"], "names": ["SystemIcon", "svg", "width", "height", "strokeLinejoin", "path", "fill", "fillRule", "d", "clipRule"], "mappings": ";;;;+BAAA;;;eAAwBA;;;;AAAT,SAASA;IACtB,qBACE,qBAACC;QAAIC,OAAM;QAAKC,QAAO;QAAKC,gBAAe;kBACzC,cAAA,qBAACC;YACCC,MAAK;YACLC,UAAS;YACTC,GAAE;YACFC,UAAS;;;AAIjB"}