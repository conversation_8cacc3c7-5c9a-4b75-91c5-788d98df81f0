{"version": 3, "sources": ["../../../src/server/lib/worker-utils.ts"], "sourcesContent": ["import http from 'http'\n\nexport const getFreePort = async (): Promise<number> => {\n  return new Promise((resolve, reject) => {\n    const server = http.createServer(() => {})\n    server.listen(0, () => {\n      const address = server.address()\n      server.close()\n\n      if (address && typeof address === 'object') {\n        resolve(address.port)\n      } else {\n        reject(new Error('invalid address from server: ' + address?.toString()))\n      }\n    })\n  })\n}\n"], "names": ["getFreePort", "Promise", "resolve", "reject", "server", "http", "createServer", "listen", "address", "close", "port", "Error", "toString"], "mappings": ";;;;+BAEaA;;;eAAAA;;;6DAFI;;;;;;AAEV,MAAMA,cAAc;IACzB,OAAO,IAAIC,QAAQ,CAACC,SAASC;QAC3B,MAAMC,SAASC,aAAI,CAACC,YAAY,CAAC,KAAO;QACxCF,OAAOG,MAAM,CAAC,GAAG;YACf,MAAMC,UAAUJ,OAAOI,OAAO;YAC9BJ,OAAOK,KAAK;YAEZ,IAAID,WAAW,OAAOA,YAAY,UAAU;gBAC1CN,QAAQM,QAAQE,IAAI;YACtB,OAAO;gBACLP,OAAO,qBAAgE,CAAhE,IAAIQ,MAAM,mCAAkCH,2BAAAA,QAASI,QAAQ,MAA7D,qBAAA;2BAAA;gCAAA;kCAAA;gBAA+D;YACxE;QACF;IACF;AACF"}