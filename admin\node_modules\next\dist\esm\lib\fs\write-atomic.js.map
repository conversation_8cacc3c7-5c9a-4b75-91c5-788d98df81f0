{"version": 3, "sources": ["../../../src/lib/fs/write-atomic.ts"], "sourcesContent": ["import { unlink, writeFile } from 'fs/promises'\nimport { rename } from './rename'\n\nexport async function writeFileAtomic(\n  filePath: string,\n  content: string\n): Promise<void> {\n  const tempPath = filePath + '.tmp.' + Math.random().toString(36).slice(2)\n  try {\n    await writeFile(tempPath, content, 'utf-8')\n    await rename(tempPath, filePath)\n  } catch (e) {\n    try {\n      await unlink(tempPath)\n    } catch {\n      // ignore\n    }\n    throw e\n  }\n}\n"], "names": ["unlink", "writeFile", "rename", "writeFileAtomic", "filePath", "content", "temp<PERSON>ath", "Math", "random", "toString", "slice", "e"], "mappings": "AAAA,SAASA,MAAM,EAAEC,SAAS,QAAQ,cAAa;AAC/C,SAASC,MAAM,QAAQ,WAAU;AAEjC,OAAO,eAAeC,gBACpBC,QAAgB,EAChBC,OAAe;IAEf,MAAMC,WAAWF,WAAW,UAAUG,KAAKC,MAAM,GAAGC,QAAQ,CAAC,IAAIC,KAAK,CAAC;IACvE,IAAI;QACF,MAAMT,UAAUK,UAAUD,SAAS;QACnC,MAAMH,OAAOI,UAAUF;IACzB,EAAE,OAAOO,GAAG;QACV,IAAI;YACF,MAAMX,OAAOM;QACf,EAAE,OAAM;QACN,SAAS;QACX;QACA,MAAMK;IACR;AACF"}