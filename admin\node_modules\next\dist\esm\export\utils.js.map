{"version": 3, "sources": ["../../src/export/utils.ts"], "sourcesContent": ["import type { NextConfigComplete } from '../server/config-shared'\n\nexport function hasCustomExportOutput(config: NextConfigComplete) {\n  // In the past, a user had to run \"next build\" to generate\n  // \".next\" (or whatever the distDir) followed by \"next export\"\n  // to generate \"out\" (or whatever the outDir). However, when\n  // \"output: export\" is configured, \"next build\" does both steps.\n  // So the user-configured distDir is actually the outDir.\n  // We'll do some custom logic when meeting this condition.\n  // e.g.\n  // Will set config.distDir to .next to make sure the manifests\n  // are still reading from temporary .next directory.\n  return config.output === 'export' && config.distDir !== '.next'\n}\n"], "names": ["hasCustomExportOutput", "config", "output", "distDir"], "mappings": "AAEA,OAAO,SAASA,sBAAsBC,MAA0B;IAC9D,0DAA0D;IAC1D,8DAA8D;IAC9D,4DAA4D;IAC5D,gEAAgE;IAChE,yDAAyD;IACzD,0DAA0D;IAC1D,OAAO;IACP,8DAA8D;IAC9D,oDAAoD;IACpD,OAAOA,OAAOC,MAAM,KAAK,YAAYD,OAAOE,OAAO,KAAK;AAC1D"}