{"version": 3, "sources": ["../../../src/client/tracing/report-to-socket.ts"], "sourcesContent": ["import { sendMessage } from '../components/react-dev-overlay/pages/websocket'\nimport type { Span } from './tracer'\n\nexport default function reportToSocket(span: Span) {\n  if (span.state.state !== 'ended') {\n    throw new Error('Expected span to be ended')\n  }\n\n  sendMessage(\n    JSON.stringify({\n      event: 'span-end',\n      startTime: span.startTime,\n      endTime: span.state.endTime,\n      spanName: span.name,\n      attributes: span.attributes,\n    })\n  )\n}\n"], "names": ["reportToSocket", "span", "state", "Error", "sendMessage", "JSON", "stringify", "event", "startTime", "endTime", "spanName", "name", "attributes"], "mappings": ";;;;+BAGA;;;eAAwBA;;;2BAHI;AAGb,SAASA,eAAeC,IAAU;IAC/C,IAAIA,KAAKC,KAAK,CAACA,KAAK,KAAK,SAAS;QAChC,MAAM,qBAAsC,CAAtC,IAAIC,MAAM,8BAAV,qBAAA;mBAAA;wBAAA;0BAAA;QAAqC;IAC7C;IAEAC,IAAAA,sBAAW,EACTC,KAAKC,SAAS,CAAC;QACbC,OAAO;QACPC,WAAWP,KAAKO,SAAS;QACzBC,SAASR,KAAKC,KAAK,CAACO,OAAO;QAC3BC,UAAUT,KAAKU,IAAI;QACnBC,YAAYX,KAAKW,UAAU;IAC7B;AAEJ"}