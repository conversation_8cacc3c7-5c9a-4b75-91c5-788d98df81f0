{"version": 3, "sources": ["../../../../../src/client/components/react-dev-overlay/utils/stack-frame.ts"], "sourcesContent": ["import type { StackFrame } from 'next/dist/compiled/stacktrace-parser'\nimport type {\n  OriginalStackFrameResponse,\n  OriginalStackFrameResponseResult,\n  OriginalStackFramesRequest,\n} from '../server/shared'\nimport {\n  isWebpackInternalResource,\n  formatFrameSourceFile,\n} from './webpack-module-path'\n\nexport interface ResolvedOriginalStackFrame extends OriginalStackFrameResponse {\n  error: false\n  reason: null\n  external: boolean\n  ignored: boolean\n  sourceStackFrame: StackFrame\n}\n\nexport interface RejectedOriginalStackFrame extends OriginalStackFrameResponse {\n  error: true\n  reason: string\n  external: boolean\n  ignored: boolean\n  sourceStackFrame: StackFrame\n}\n\nexport type OriginalStackFrame =\n  | ResolvedOriginalStackFrame\n  | RejectedOriginalStackFrame\n\nfunction getOriginalStackFrame(\n  source: StackFrame,\n  response: OriginalStackFrameResponseResult\n): Promise<OriginalStackFrame> {\n  async function _getOriginalStackFrame(): Promise<ResolvedOriginalStackFrame> {\n    if (response.status === 'rejected') {\n      throw new Error(response.reason)\n    }\n\n    const body: OriginalStackFrameResponse = response.value\n\n    return {\n      error: false,\n      reason: null,\n      external: false,\n      sourceStackFrame: source,\n      originalStackFrame: body.originalStackFrame,\n      originalCodeFrame: body.originalCodeFrame || null,\n      ignored: body.originalStackFrame?.ignored || false,\n    }\n  }\n\n  // TODO: merge this section into ignoredList handling\n  if (source.file === 'file://' || source.file?.match(/https?:\\/\\//)) {\n    return Promise.resolve({\n      error: false,\n      reason: null,\n      external: true,\n      sourceStackFrame: source,\n      originalStackFrame: null,\n      originalCodeFrame: null,\n      ignored: true,\n    })\n  }\n\n  return _getOriginalStackFrame().catch(\n    (err: Error): RejectedOriginalStackFrame => ({\n      error: true,\n      reason: err?.message ?? err?.toString() ?? 'Unknown Error',\n      external: false,\n      sourceStackFrame: source,\n      originalStackFrame: null,\n      originalCodeFrame: null,\n      ignored: false,\n    })\n  )\n}\n\nexport async function getOriginalStackFrames(\n  frames: StackFrame[],\n  type: 'server' | 'edge-server' | null,\n  isAppDir: boolean\n): Promise<OriginalStackFrame[]> {\n  const req: OriginalStackFramesRequest = {\n    frames,\n    isServer: type === 'server',\n    isEdgeServer: type === 'edge-server',\n    isAppDirectory: isAppDir,\n  }\n\n  let res: Response | undefined = undefined\n  let reason: string | undefined = undefined\n  try {\n    res = await fetch('/__nextjs_original-stack-frames', {\n      method: 'POST',\n      body: JSON.stringify(req),\n    })\n  } catch (e) {\n    reason = e + ''\n  }\n\n  // When fails to fetch the original stack frames, we reject here to be\n  // caught at `_getOriginalStackFrame()` and return the stack frames so\n  // that the error overlay can render.\n  if (res && res.ok && res.status !== 204) {\n    const data = await res.json()\n    return Promise.all(\n      frames.map((frame, index) => getOriginalStackFrame(frame, data[index]))\n    )\n  } else {\n    if (res) {\n      reason = await res.text()\n    }\n  }\n  return Promise.all(\n    frames.map((frame) =>\n      getOriginalStackFrame(frame, {\n        status: 'rejected',\n        reason: `Failed to fetch the original stack frames ${reason ? `: ${reason}` : ''}`,\n      })\n    )\n  )\n}\n\nexport function getFrameSource(frame: StackFrame): string {\n  if (!frame.file) return ''\n\n  const isWebpackFrame = isWebpackInternalResource(frame.file)\n\n  let str = ''\n  // Skip URL parsing for webpack internal file paths.\n  if (isWebpackFrame) {\n    str = formatFrameSourceFile(frame.file)\n  } else {\n    try {\n      const u = new URL(frame.file)\n\n      let parsedPath = ''\n      // Strip the origin for same-origin scripts.\n      if (globalThis.location?.origin !== u.origin) {\n        // URLs can be valid without an `origin`, so long as they have a\n        // `protocol`. However, `origin` is preferred.\n        if (u.origin === 'null') {\n          parsedPath += u.protocol\n        } else {\n          parsedPath += u.origin\n        }\n      }\n\n      // Strip query string information as it's typically too verbose to be\n      // meaningful.\n      parsedPath += u.pathname\n      str = formatFrameSourceFile(parsedPath)\n    } catch {\n      str = formatFrameSourceFile(frame.file)\n    }\n  }\n\n  if (!isWebpackInternalResource(frame.file) && frame.lineNumber != null) {\n    if (str) {\n      if (frame.column != null) {\n        str += ` (${frame.lineNumber}:${frame.column})`\n      } else {\n        str += ` (${frame.lineNumber})`\n      }\n    }\n  }\n  return str\n}\n"], "names": ["getFrameSource", "getOriginalStackFrames", "getOriginalStackFrame", "source", "response", "_getOriginalStackFrame", "body", "status", "Error", "reason", "value", "error", "external", "sourceStackFrame", "originalStackFrame", "originalCodeFrame", "ignored", "file", "match", "Promise", "resolve", "catch", "err", "message", "toString", "frames", "type", "isAppDir", "req", "isServer", "isEdgeServer", "isAppDirectory", "res", "undefined", "fetch", "method", "JSON", "stringify", "e", "ok", "data", "json", "all", "map", "frame", "index", "text", "isWebpackFrame", "isWebpackInternalResource", "str", "formatFrameSourceFile", "globalThis", "u", "URL", "parsed<PERSON><PERSON>", "location", "origin", "protocol", "pathname", "lineNumber", "column"], "mappings": ";;;;;;;;;;;;;;;IA6HgBA,cAAc;eAAdA;;IA9CMC,sBAAsB;eAAtBA;;;mCAtEf;AAsBP,SAASC,sBACPC,MAAkB,EAClBC,QAA0C;QAqBTD;IAnBjC,eAAeE;YAcFC;QAbX,IAAIF,SAASG,MAAM,KAAK,YAAY;YAClC,MAAM,qBAA0B,CAA1B,IAAIC,MAAMJ,SAASK,MAAM,GAAzB,qBAAA;uBAAA;4BAAA;8BAAA;YAAyB;QACjC;QAEA,MAAMH,OAAmCF,SAASM,KAAK;QAEvD,OAAO;YACLC,OAAO;YACPF,QAAQ;YACRG,UAAU;YACVC,kBAAkBV;YAClBW,oBAAoBR,KAAKQ,kBAAkB;YAC3CC,mBAAmBT,KAAKS,iBAAiB,IAAI;YAC7CC,SAASV,EAAAA,2BAAAA,KAAKQ,kBAAkB,qBAAvBR,yBAAyBU,OAAO,KAAI;QAC/C;IACF;IAEA,qDAAqD;IACrD,IAAIb,OAAOc,IAAI,KAAK,eAAad,eAAAA,OAAOc,IAAI,qBAAXd,aAAae,KAAK,CAAC,iBAAgB;QAClE,OAAOC,QAAQC,OAAO,CAAC;YACrBT,OAAO;YACPF,QAAQ;YACRG,UAAU;YACVC,kBAAkBV;YAClBW,oBAAoB;YACpBC,mBAAmB;YACnBC,SAAS;QACX;IACF;IAEA,OAAOX,yBAAyBgB,KAAK,CACnC,CAACC;YAESA,cAAAA;eAFmC;YAC3CX,OAAO;YACPF,QAAQa,CAAAA,OAAAA,CAAAA,eAAAA,uBAAAA,IAAKC,OAAO,YAAZD,eAAgBA,uBAAAA,IAAKE,QAAQ,cAA7BF,OAAmC;YAC3CV,UAAU;YACVC,kBAAkBV;YAClBW,oBAAoB;YACpBC,mBAAmB;YACnBC,SAAS;QACX;;AAEJ;AAEO,eAAef,uBACpBwB,MAAoB,EACpBC,IAAqC,EACrCC,QAAiB;IAEjB,MAAMC,MAAkC;QACtCH;QACAI,UAAUH,SAAS;QACnBI,cAAcJ,SAAS;QACvBK,gBAAgBJ;IAClB;IAEA,IAAIK,MAA4BC;IAChC,IAAIxB,SAA6BwB;IACjC,IAAI;QACFD,MAAM,MAAME,MAAM,mCAAmC;YACnDC,QAAQ;YACR7B,MAAM8B,KAAKC,SAAS,CAACT;QACvB;IACF,EAAE,OAAOU,GAAG;QACV7B,SAAS6B,IAAI;IACf;IAEA,sEAAsE;IACtE,sEAAsE;IACtE,qCAAqC;IACrC,IAAIN,OAAOA,IAAIO,EAAE,IAAIP,IAAIzB,MAAM,KAAK,KAAK;QACvC,MAAMiC,OAAO,MAAMR,IAAIS,IAAI;QAC3B,OAAOtB,QAAQuB,GAAG,CAChBjB,OAAOkB,GAAG,CAAC,CAACC,OAAOC,QAAU3C,sBAAsB0C,OAAOJ,IAAI,CAACK,MAAM;IAEzE,OAAO;QACL,IAAIb,KAAK;YACPvB,SAAS,MAAMuB,IAAIc,IAAI;QACzB;IACF;IACA,OAAO3B,QAAQuB,GAAG,CAChBjB,OAAOkB,GAAG,CAAC,CAACC,QACV1C,sBAAsB0C,OAAO;YAC3BrC,QAAQ;YACRE,QAAQ,AAAC,+CAA4CA,CAAAA,SAAS,AAAC,OAAIA,SAAW,EAAC;QACjF;AAGN;AAEO,SAAST,eAAe4C,KAAiB;IAC9C,IAAI,CAACA,MAAM3B,IAAI,EAAE,OAAO;IAExB,MAAM8B,iBAAiBC,IAAAA,4CAAyB,EAACJ,MAAM3B,IAAI;IAE3D,IAAIgC,MAAM;IACV,oDAAoD;IACpD,IAAIF,gBAAgB;QAClBE,MAAMC,IAAAA,wCAAqB,EAACN,MAAM3B,IAAI;IACxC,OAAO;QACL,IAAI;gBAKEkC;YAJJ,MAAMC,IAAI,IAAIC,IAAIT,MAAM3B,IAAI;YAE5B,IAAIqC,aAAa;YACjB,4CAA4C;YAC5C,IAAIH,EAAAA,uBAAAA,WAAWI,QAAQ,qBAAnBJ,qBAAqBK,MAAM,MAAKJ,EAAEI,MAAM,EAAE;gBAC5C,gEAAgE;gBAChE,8CAA8C;gBAC9C,IAAIJ,EAAEI,MAAM,KAAK,QAAQ;oBACvBF,cAAcF,EAAEK,QAAQ;gBAC1B,OAAO;oBACLH,cAAcF,EAAEI,MAAM;gBACxB;YACF;YAEA,qEAAqE;YACrE,cAAc;YACdF,cAAcF,EAAEM,QAAQ;YACxBT,MAAMC,IAAAA,wCAAqB,EAACI;QAC9B,EAAE,UAAM;YACNL,MAAMC,IAAAA,wCAAqB,EAACN,MAAM3B,IAAI;QACxC;IACF;IAEA,IAAI,CAAC+B,IAAAA,4CAAyB,EAACJ,MAAM3B,IAAI,KAAK2B,MAAMe,UAAU,IAAI,MAAM;QACtE,IAAIV,KAAK;YACP,IAAIL,MAAMgB,MAAM,IAAI,MAAM;gBACxBX,OAAO,AAAC,OAAIL,MAAMe,UAAU,GAAC,MAAGf,MAAMgB,MAAM,GAAC;YAC/C,OAAO;gBACLX,OAAO,AAAC,OAAIL,MAAMe,UAAU,GAAC;YAC/B;QACF;IACF;IACA,OAAOV;AACT"}